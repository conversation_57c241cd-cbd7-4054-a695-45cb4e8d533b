{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\goodsForm.vue?vue&type=style&index=0&id=d5869944&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\goodsForm.vue", "mtime": 1754840036318}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1734093918845}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1734093920151}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1734093919263}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1734093918467}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubWFpbi1wYW5lbCB7CiAgIHBhZGRpbmctdG9wOiA1cHg7CiAgIC5jb250ZW50IHsKICAgICAgIG1hcmdpbi10b3A6IDMwcHg7CiAgICAgICBtYXJnaW4tbGVmdDogMjBweDsKICAgfQogIC5mb290ZXIgewogICAgIG1hcmdpbi10b3A6IDIwcHg7CiAgfQogIC5jcmVhdGUtc24gewogICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICBjb2xvcjogYmx1ZTsKICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgd2lkdGg6IDEwMHB4OwogIH0KICAudGltZS1jb25maWctdGlwIHsKICAgIHBhZGRpbmc6IDEwcHggMjBweDsKICB9Cn0K"}, {"version": 3, "sources": ["goodsForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "goodsForm.vue", "sourceRoot": "src/views/goods/goods", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"main-panel\">\n      <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n        <el-tab-pane label=\"基础信息\" name=\"base\">\n          <div class=\"content\">\n            <el-form ref=\"baseForm\" :model=\"baseForm\" :rules=\"baseRules\" label-width=\"120px\">\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品类型\" prop=\"cateId\">\n                    <el-select class=\"input\" v-model=\"baseForm.type\" placeholder=\"请选择商品类型\" @change=\"handleTypeChange\">\n                      <el-option\n                        v-for=\"item in typeOptions\"\n                        :key=\"item.key\"\n                        :label=\"item.name\"\n                        :value=\"item.key\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品名称\" prop=\"name\">\n                    <el-input class=\"input\" v-model=\"baseForm.name\" placeholder=\"请输入商品名称\" maxlength=\"200\" />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品条码\" prop=\"goodsNo\">\n                    <el-input class=\"input\" v-model=\"baseForm.goodsNo\" placeholder=\"请输入商品条码，或使用扫码枪扫描\" maxlength=\"50\"/>\n                    <div class=\"create-sn\" @click=\"createGoodsSn()\">随机生成条码</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品分类\" prop=\"cateId\">\n                    <el-select class=\"input\" v-model=\"baseForm.cateId\" placeholder=\"请选择商品分类\">\n                      <el-option\n                        v-for=\"item in cateOptions\"\n                        :key=\"item.id\"\n                        :label=\"item.name\"\n                        :value=\"item.id\"\n                        :disabled=\"item.status !== 'A'\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属店铺\" prop=\"storeId\">\n                    <el-select class=\"input\" v-model=\"baseForm.storeId\" clearable placeholder=\"请选择所属店铺\">\n                      <el-option :key=\"0\" label=\"公共商品\" v-if=\"storeId == 0\" :value=\"0\"/>\n                      <el-option\n                        v-for=\"item in storeOptions\"\n                        :key=\"item.id\"\n                        :label=\"item.name\"\n                        :value=\"item.id\"\n                        :disabled=\"item.status !== 'A'\"\n                      ></el-option>\n                    </el-select>\n                    <div class=\"form-tips\">提示：未选择则属于公共商品</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品图片\" prop=\"images\">\n                    <el-upload class=\"form__head-icon-upload\"\n                               :action=\"uploadAction\"\n                               list-type=\"picture-card\"\n                               :file-list=\"uploadFiles\"\n                               :limit=\"10\"\n                               :auto-upload=\"true\"\n                               :headers=\"uploadHeader\"\n                               :on-success=\"handleUploadSuccess\"\n                               :on-remove=\"handleRemove\">\n                      <i class=\"el-icon-plus\"></i>\n                    </el-upload>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"显示排序\" prop=\"sort\">\n                    <el-input-number v-model=\"baseForm.sort\" :min=\"0\" />\n                    <div class=\"form-tips\">提示：数值越小，排行越靠前</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品状态\">\n                    <el-radio-group v-model=\"baseForm.status\">\n                      <el-radio key=\"A\" label=\"A\" value=\"A\">上架</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">下架</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"扩展信息\" name=\"extend\">\n          <div class=\"content\">\n            <el-form ref=\"extendForm\" :model=\"extendForm\" :rules=\"extendRules\" label-width=\"120px\">\n              <!-- <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"积分抵扣\" prop=\"canUsePoint\">\n                    <el-radio-group v-model=\"extendForm.canUsePoint\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">可用</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">不可用</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品卖点\" prop=\"salePoint\">\n                    <el-input class=\"input\" v-model=\"extendForm.salePoint\" placeholder=\"请输入商品卖点，几个字总结\" maxlength=\"50\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"baseForm.type == 'goods'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"饮料制作机\" prop=\"drinkMaker\">\n                    <el-select\n                      v-model=\"extendForm.drinkMaker\"\n                      placeholder=\"请选择饮料制作机\"\n                      clearable\n                    >\n                      <el-option label=\"21路\" value=\"21\"/>\n                      <el-option label=\"21+BLENDER\" value=\"21+BLENDER\"/>\n                      <el-option label=\"21+SODA\" value=\"21+SODA\"/>\n                      <el-option label=\"21+NESPRESSO\" value=\"21+NESPRESSO\"/>\n                      <el-option label=\"NESPRESSO\" value=\"NESPRESSO\"/>\n                      <el-option label=\"21+ICECREAM\" value=\"21+ICECREAM\"/>\n                      <el-option label=\"ICECREAM+NESPRESSO\" value=\"ICECREAM+NESPRESSO\"/>\n                      <el-option label=\"ICECREAM\" value=\"ICECREAM\"/>\n                      <el-option label=\"8路设备\" value=\"8\"/>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n              <el-row  v-if=\"baseForm.type == 'goods'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品制作编码\" prop=\"makeCode\">\n                    <el-input class=\"input\" v-model=\"extendForm.makeCode\" placeholder=\"请输入商品制作编码\" maxlength=\"5\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"会员折扣\" prop=\"isMemberDiscount\">\n                    <el-radio-group v-model=\"extendForm.isMemberDiscount\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">有折扣</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">无折扣</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row  v-if=\"baseForm.type != 'package'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"规格类型\" prop=\"isSingleSpec\">\n                    <el-radio-group v-model=\"extendForm.isSingleSpec\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">单规格</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">多规格</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'N'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品规格\" prop=\"goodsSpec\">\n                     <Sku ref=\"Sku\" :skuData=\"skuData\" :makeCode=\"extendForm.makeCode\" :goodsId=\"baseForm.goodsId\" :uploadDomain=\"uploadDomain\" @skuChange=\"skuChange\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n\n              <el-row v-if=\"baseForm.type == 'service'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"服务时长\" prop=\"serviceTime\">\n                    <el-input v-model=\"extendForm.serviceTime\" class=\"min-input\" placeholder=\"请输入服务时长，单位：分钟\" maxlength=\"50\"/>\n                    <div class=\"form-tips\">提示：输入数字，单位：分钟</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"baseForm.type == 'coupon'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"卡券ID\" prop=\"couponIds\">\n                    <el-input v-model=\"extendForm.couponIds\" class=\"input\" rows=\"2\" type=\"textarea\" placeholder=\"请输入购买的卡券ID，英文逗号分隔，如：1000,1001,1002\" maxlength=\"1000\"/>\n                    <div class=\"form-tips\">提示：购买的卡券ID，英文逗号分隔</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y' && baseForm.type == 'goods' \">\n                <el-col :span=\"24\">\n                  <el-form-item  prop=\"produceCode\">\n                    <template slot=\"label\">\n                      餐品制作码\n                      <el-tooltip placement=\"top\" effect=\"light\">\n                        <template slot=\"content\">\n                          <img src='@/assets/images/cup-qrcode-demo.jpg' style='width: 800px;'>\n                        </template>\n                        <i class=\"el-icon-info\"></i>\n                      </el-tooltip>\n                    </template>\n                    <el-input :trim=\"true\" v-model=\"extendForm.produceCode\" class=\"min-input\" placeholder=\"请输入餐品制作码\" maxlength=\"20\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"库存数量\" prop=\"stock\">\n                    <el-input v-model=\"extendForm.stock\" class=\"min-input\" placeholder=\"请输入库存数量\" maxlength=\"50\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品价格\" prop=\"price\">\n                    <el-input v-model=\"extendForm.price\" class=\"min-input\" placeholder=\"请输入商品价格\" maxlength=\"50\"/>\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"会员专属价格\" prop=\"gradePrice\">\n                    <el-input v-model=\"extendForm.gradePrice\" class=\"min-input\" placeholder=\"请输入会员专属价格\" maxlength=\"50\" />\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n               <el-row v-if=\"baseForm.type == 'package'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"套餐商品\" prop=\"packageItems\">\n                    <PackageItemNew\n                      ref=\"packageItem\"\n                      :goodsId=\"baseForm.goodsId\"\n                      :uploadDomain=\"uploadDomain\"\n                      :typeOptions=\"typeOptions\"\n                      :packageData=\"packageData\"\n                      @change=\"handlePackageChange\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <!-- <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"划线价格\" prop=\"linePrice\">\n                    <el-input v-model=\"extendForm.linePrice\" class=\"min-input\" placeholder=\"请输入商品划线，空则不显示\" maxlength=\"50\" />\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n              <!-- <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"初始销量\" prop=\"initSale\">\n                    <el-input v-model=\"extendForm.initSale\" class=\"min-input\" placeholder=\"请输入初始销量\" maxlength=\"10\"/>\n                    <div class=\"form-tips\">提示：输入数字，虚拟销量</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n              <!-- <el-row v-if=\"extendForm.isSingleSpec == 'Y' && baseForm.type == 'product'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品重量\" prop=\"weight\">\n                    <el-input v-model=\"extendForm.weight\" class=\"min-input\" placeholder=\"请输入商品重量\" maxlength=\"10\"/>\n                    <div class=\"form-tips\">提示：输入数字，单位kg</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"商品介绍\" name=\"detail\">\n          <div class=\"content\" style=\"width: 375px;margin-left: 80px;\">\n            <el-form ref=\"detailForm\" :model=\"detailForm\" :rules=\"detailRules\" label-width=\"120px\">\n               <editor v-model=\"detailForm.description\" :min-height=\"550\"/>\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"时间段配置\" name=\"timeConfig\" v-if=\"baseForm.goodsId && baseForm.goodsId !== '0'\">\n          <div class=\"time-config-tip\">\n            <el-alert\n              title=\"提示：可以为商品设置销售时间段，如每日通用、按周设置或自定义日期。设置后，商品只在指定时间段内可售。\"\n              type=\"info\"\n              show-icon\n              :closable=\"false\"\n            />\n          </div>\n          <TimeConfigList :goods-id=\"baseForm.goodsId\" />\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"footer\">\n         <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n         <el-button @click=\"cancel\">取消</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { saveGoods, getGoodsInfo, getPackageItems, savePackageItems } from \"@/api/goods\";\nimport Sku from '../components/Sku';\nimport PackageItemNew from '../components/PackageItemNew';\nimport TimeConfigList from '../components/TimeConfigList';\nexport default {\n  name: \"GoodsForm\",\n  components: { Sku, PackageItemNew, TimeConfigList },\n  data() {\n    return {\n      storeId: this.$store.getters.storeId,\n      // 遮罩层\n      loading: false,\n      activeTab: 'base',\n      storeOptions: [],\n      cateOptions: [],\n      typeOptions: [],\n      skuData: { attrList: [], skuList: [], initSkuList: [] },\n      // 套餐商品数据\n      packageData: [],\n      // 基础信息表单\n      baseForm: { type: 'goods', goodsId: '', name: '', storeId: this.$store.getters.storeId, cateId: '', goodsNo: '', images: [], status: \"A\", sort: 0 },\n      // 扩展信息表单\n      extendForm: { goodsId: '', canUsePoint: 'Y', isMemberDiscount: 'Y', isSingleSpec: 'Y', produceCode: '', serviceTime: 0, couponIds: '', stock: '', price: '', gradePrice: '' ,linePrice: '', salePoint: '', makeCode: '', drinkMaker: '', initSale: '', weight: '', skuData: null },\n      // 详情信息表单\n      detailForm: { goodsId: '', description : '' },\n      // 上传地址\n      uploadAction: process.env.VUE_APP_SERVER_URL + 'backendApi/file/upload',\n      uploadHeader: { 'Access-Token' : getToken() },\n      // 上传域名\n      uploadDomain: '',\n      // 上传文件列表\n      uploadFiles: [],\n      // 基础信息表单校验\n      baseRules: {\n        name: [\n          { required: true, message: \"商品名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 30, message: '商品名称长度必须介于2和200 之间', trigger: 'blur' }\n        ],\n        goodsNo: [\n          { required: true, message: \"商品条码不能为空\", trigger: \"blur\" },\n          { min: 2, max: 100, message: '商品条码长度必须介于2和100之间', trigger: 'blur' }\n        ],\n        cateId: [\n          { required: true, message: \"请选择商品分类\", trigger: \"blur\" }\n        ],\n        images: [\n          { required: true, message: \"请上传商品图片\", trigger: \"blur\" }\n        ],\n      },\n      // 扩展信息表单校验\n      extendRules: {\n        couponIds: [\n          { required: true, message: \"卡券ID不能为空\", trigger: \"blur\" },\n          { min: 1, max: 1000, message: '卡券ID长度必须介于1和100之间', trigger: 'blur' }\n        ],\n        canUsePoint: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        isMemberDiscount: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        isSingleSpec: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        price: [\n          { required: true, message: \"请输入商品价格\", trigger: \"blur\" }, { pattern: /(^[1-9]\\d*(\\.\\d{1,2})?$)|(^0(\\.\\d{1,2})?$)/, message: `价格必须大于0`, trigger: 'blur' }\n        ],\n        /* produceCode: [\n          { pattern: /^=A(0[1-9]|[1-9][0-9])\\|C(0[1-9]|[1-9][0-9]),T(0[1-9]|[1-9][0-9]),W(0[1-9]|[1-9][0-9])$/, message: `输入正确的制作码`, trigger: 'input' }\n        ], */\n        stock: [\n          { required: true, message: \"请输入库存数量\", trigger: \"blur\" }, { pattern: /^[0-9]{1,10}$/, message: `库存数量必须是正整数`, trigger: 'blur' }\n        ],\n        initSale: [\n          { pattern: /^[0-9]{1,10}$/, message: `初始销量必须是正整数`, trigger: 'blur' }\n        ],\n        weight: [\n          { pattern: /(^[1-9]\\d*(\\.\\d{1,2})?$)|(^0(\\.\\d{1,2})?$)/, message: `重量必须大于等于0`, trigger: 'blur' }\n        ],\n      },\n      detailRules: {\n        description: [\n          { required: true, message: \"请输入商品详情\", trigger: \"blur\" }\n        ],\n      }\n    };\n  },\n  created() {\n    const goodsId = this.$route.query.goodsId ? this.$route.query.goodsId : '0'\n    this.getGoodsInfo(goodsId);\n\n    // 先只加载基础信息，等类型确认后再加载套餐数据\n  },\n  methods: {\n    handleTabClick() {\n       // empty\n    },\n\n    // 处理商品类型变化\n    handleTypeChange(value) {\n      // 如果不是套餐商品，清空套餐数据\n      if (value !== 'package') {\n        this.packageData = [];\n      } else if (this.baseForm.goodsId && this.baseForm.goodsId !== '0') {\n        // 如果是套餐商品，加载套餐数据\n        this.getPackageItems(this.baseForm.goodsId);\n      }\n    },\n    // 获取套餐商品项目\n    getPackageItems(goodsId) {\n      if (goodsId && goodsId !== '0') {\n        getPackageItems(goodsId).then(response => {\n          if (response.data && response.data.groups) {\n            this.packageData = response.data.groups;\n          }\n        });\n      }\n    },\n\n    // 处理套餐数据变化\n    handlePackageChange(packageItems) {\n      this.packageData = packageItems;\n    },\n\n    getGoodsInfo(goodsId) {\n      const app = this;\n      getGoodsInfo(goodsId).then(response => {\n          const goodsInfo = response.data.goods;\n          const imagePath = response.data.imagePath;\n          this.uploadDomain = imagePath;\n          if (goodsInfo) {\n              // 商品基础信息\n              this.baseForm.goodsId = goodsInfo.id+'';\n              this.baseForm.type = goodsInfo.type;\n              this.baseForm.name = goodsInfo.name;\n              this.baseForm.goodsNo = goodsInfo.goodsNo;\n              this.baseForm.cateId = goodsInfo.cateId;\n              this.baseForm.storeId = goodsInfo.storeId;\n              this.baseForm.sort = goodsInfo.sort;\n              this.baseForm.status = goodsInfo.status;\n\n              // 如果是套餐商品，获取套餐数据\n              if (goodsInfo.type === 'package' && goodsInfo.id) {\n                  this.getPackageItems(goodsInfo.id);\n              }\n              // 商品图片\n              this.baseForm.images = response.data.images;\n              const images = this.baseForm.images;\n              app.uploadFiles = [];\n              if (images && images.length > 0) {\n                  images.forEach(function(url){\n                     app.uploadFiles.push({ url: imagePath + url })\n                  })\n              }\n\n              // 扩展信息\n              this.extendForm.goodsId = goodsInfo.id;\n              this.extendForm.canUsePoint = goodsInfo.canUsePoint == null ? 'Y' : goodsInfo.canUsePoint;\n              this.extendForm.isMemberDiscount = goodsInfo.isMemberDiscount == null ? 'Y' : goodsInfo.isMemberDiscount;\n              this.extendForm.isSingleSpec = goodsInfo.isSingleSpec == null ? 'Y' : goodsInfo.isSingleSpec;\n              this.extendForm.stock = goodsInfo.stock;\n              this.extendForm.price = goodsInfo.price;\n              this.extendForm.gradePrice = goodsInfo.gradePrice ?? goodsInfo.price;\n              this.extendForm.linePrice = goodsInfo.linePrice;\n              this.extendForm.initSale = goodsInfo.initSale;\n              this.extendForm.salePoint = goodsInfo.salePoint;\n              this.extendForm.drinkMaker = goodsInfo.drinkMaker;\n              this.extendForm.makeCode = goodsInfo.makeCode;\n              this.extendForm.weight = goodsInfo.weight;\n              this.extendForm.serviceTime = goodsInfo.serviceTime;\n              this.extendForm.couponIds = goodsInfo.couponIds;\n              this.extendForm.produceCode = goodsInfo.produceCode;\n\n              // 多规格\n              this.skuData.attrList = response.data.specData;\n              this.skuData.skuList = response.data.skuData;\n              this.skuData.initSkuList = response.data.skuData;\n\n              // 商品详情\n              this.detailForm.goodsId = goodsInfo.id;\n              this.detailForm.description = goodsInfo.description;\n          }\n          this.cateOptions = response.data.cateList;\n          this.storeOptions = response.data.storeList;\n          this.typeOptions = response.data.typeList;\n      });\n    },\n    cancel() {\n      this.$store.dispatch('tagsView/delView', this.$route)\n      this.$router.push( { path: '/goods/goods/index' } );\n    },\n    skuChange(skuData) {\n       // empty\n    },\n    // 提交按钮\n    submitForm: function() {\n      const app = this;\n      if (app.activeTab == 'base') {\n          app.$refs[\"baseForm\"].validate(valid => {\n           if (valid) {\n               saveGoods(app.baseForm).then(response => {\n                  app.$modal.msgSuccess(\"保存成功！\");\n                  app.getGoodsInfo(response.data.goodsInfo.id);\n\n                  // 提示用户基础信息已保存\n                  app.$message.info('商品基础信息已保存')\n               }).catch(error => {\n                 console.error('保存基础信息失败:', error)\n                 app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n               });\n           }\n         });\n      } else if (app.activeTab == 'extend') {\n          if (!app.extendForm.goodsId) {\n              app.$modal.msgError(\"请先提交商品基础信息\");\n              app.activeTab = 'base';\n              return false;\n          }\n          // 多规格商品验证\n          if (app.skuData.skuList && app.skuData.skuList.length > 0) {\n              let isValid0 = true;\n              let isValid1 = true;\n              let isValid2 = true;\n              app.skuData.skuList.forEach(function(item) {\n                 if (!item.skuNo || item.skuNo.length < 1 ) {\n                     isValid0 = false;\n                 }\n                 if (item.stock < 0) {\n                     isValid1 = false;\n                 }\n                 if (item.price < 0) {\n                     isValid2 = false;\n                 }\n              })\n              if (!isValid1) {\n                  app.$modal.alert(\"商品sku编码长度需大于1，请仔细核对！\");\n                  return false;\n              }\n              if (!isValid1) {\n                  app.$modal.alert(\"商品库存须大于等于0，请仔细核对！\");\n                  return false;\n              }\n              if (!isValid2) {\n                  app.$modal.alert(\"商品价格须大于等于0，请仔细核对！\");\n                  return false;\n              }\n          }\n\n          app.extendForm.skuData = app.skuData.skuList;\n          app.extendForm.specData = app.skuData.attrList;\n\n          // 保存套餐信息\n          if (app.baseForm.type === 'package' && app.$refs.packageItem) {\n            const packageGroups = app.$refs.packageItem.getPackageData();\n            if (packageGroups.length === 0) {\n              app.$modal.msgError(\"请添加套餐分组\");\n              return false;\n            }\n\n            // 先保存基本信息\n            app.$refs[\"extendForm\"].validate(valid => {\n              if (valid) {\n                saveGoods(app.extendForm).then(response => {\n                  const goodsId = response.data.goodsInfo.id;\n                  // 再保存套餐项目\n                  savePackageItems({\n                    goodsId: goodsId,\n                    groups: packageGroups\n                  }).then(() => {\n                    app.$modal.msgSuccess(\"保存成功！\");\n                    app.getGoodsInfo(goodsId);\n                  });\n                });\n              }\n            });\n          } else {\n            // 普通商品保存\n            app.$refs[\"extendForm\"].validate(valid => {\n              if (valid) {\n                saveGoods(app.extendForm).then(response => {\n                 app.$modal.msgSuccess(\"保存成功！\");\n                 app.getGoodsInfo(response.data.goodsInfo.id);\n\n                 // 提示用户扩展信息已保存\n                 app.$message.info('商品扩展信息已保存')\n               }).catch(error => {\n                 console.error('保存扩展信息失败:', error)\n                 app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n               });\n              }\n            });\n          }\n      } else {\n          if (!app.detailForm.goodsId) {\n              app.$modal.msgError(\"请先提交商品基础信息\");\n              app.activeTab = 'base';\n              return false;\n          }\n          app.$refs[\"detailForm\"].validate(valid => {\n              if (valid) {\n                  saveGoods(app.detailForm).then(response => {\n                      app.$modal.msgSuccess(\"保存成功！\");\n                      app.getGoodsInfo(response.data.goodsInfo.id);\n\n                      // 提示用户详情信息已保存\n                      app.$message.info('商品详情信息已保存')\n                  }).catch(error => {\n                    console.error('保存详情信息失败:', error)\n                    app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n                  });\n              }\n          });\n      }\n    },\n    // 文件上传成功\n    handleUploadSuccess(file) {\n      this.baseForm.images.push(file.data.fileName)\n    },\n    // 文件删除处理\n    handleRemove(file) {\n      const newImages = [];\n      if (this.baseForm.images && this.baseForm.images.length > 0) {\n          this.baseForm.images.forEach(function(item){\n              if (file.url.indexOf(item) == -1) {\n                  newImages.push(item);\n              }\n          })\n      }\n      this.baseForm.images = newImages;\n    },\n    // 生成随机条码\n    createGoodsSn() {\n      let sn = (Math.random() + 1) * 100000000000000;\n      this.baseForm.goodsNo = sn.toFixed(0);\n    }\n  }\n};\n</script>\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n   .main-panel {\n      padding-top: 5px;\n      .content {\n          margin-top: 30px;\n          margin-left: 20px;\n      }\n     .footer {\n        margin-top: 20px;\n     }\n     .create-sn {\n        font-size: 12px;\n        color: blue;\n        cursor: pointer;\n        width: 100px;\n     }\n     .time-config-tip {\n       padding: 10px 20px;\n     }\n   }\n</style>\n"]}]}