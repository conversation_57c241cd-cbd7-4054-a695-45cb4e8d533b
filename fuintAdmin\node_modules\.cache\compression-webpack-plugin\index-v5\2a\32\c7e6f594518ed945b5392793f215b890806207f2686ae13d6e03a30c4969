
dff5f90b2b7df29ed55784f5469a5a55ff55be5c	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"6a05cad00c8a0694eb6c471717aad83d\"}","integrity":"sha512-9AvRvRW+TYcwk5unCOtgObGLEtuckYaRf4s1z1T4gqRFRIvV5WxYpand7Hwwf6/J/XCZUH8cuFZ0dDQV63eIkw==","time":1754920925198,"size":2946376}