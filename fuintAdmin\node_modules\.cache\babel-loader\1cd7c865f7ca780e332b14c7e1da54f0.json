{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\category-config.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\category-config.vue", "mtime": 1754921411934}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkQ6L3dvcmtzcGFjZS9mdWludEZvb2RTeXN0ZW0vZnVpbnRBZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCBfcmVnZW5lcmF0b3JSdW50aW1lIGZyb20gIkQ6L3dvcmtzcGFjZS9mdWludEZvb2RTeXN0ZW0vZnVpbnRBZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6L3dvcmtzcGFjZS9mdWludEZvb2RTeXN0ZW0vZnVpbnRBZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vYXN5bmNUb0dlbmVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbmQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgZ2V0UHJpbnRlckxpc3QsIGdldENhdGVnb3J5TGlzdCwgc2F2ZVByaW50ZXJDYXRlZ29yaWVzLCBnZXRQcmludGVyQ2F0ZWdvcmllcyB9IGZyb20gIkAvYXBpL3ByaW50ZXIiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlByaW50ZXJDYXRlZ29yeUNvbmZpZyIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBzYXZpbmc6IGZhbHNlLAogICAgICBjYXRlZ29yeUxvYWRpbmc6IGZhbHNlLAogICAgICAvLyDooajljZXmlbDmja4KICAgICAgZm9ybTogewogICAgICAgIHByaW50ZXJJZDogJycsCiAgICAgICAgY2F0ZUlkczogW10KICAgICAgfSwKICAgICAgLy8g5Z+656GA5pWw5o2uCiAgICAgIGxhYmVsUHJpbnRlcnM6IFtdLAogICAgICBjYXRlZ29yeUxpc3Q6IFtdLAogICAgICBzdG9yZUxpc3Q6IFtdLAogICAgICBwcmludGVyQ2F0ZWdvcmllc01hcDoge30KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5sb2FkRGF0YSgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g5Yqg6L295omA5pyJ5pWw5o2uCiAgICBsb2FkRGF0YTogZnVuY3Rpb24gbG9hZERhdGEoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDE7CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIFByb21pc2UuYWxsKFtfdGhpcy5sb2FkUHJpbnRlcnMoKSwgX3RoaXMubG9hZENhdGVnb3JpZXMoKV0pOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDY7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzLmxvYWRBbGxQcmludGVyQ2F0ZWdvcmllcygpOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDg6CiAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDg7CiAgICAgICAgICAgICAgX2NvbnRleHQudDAgPSBfY29udGV4dFsiY2F0Y2giXSgxKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mlbDmja7lpLHotKU6JywgX2NvbnRleHQudDApOwogICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAxMTsKICAgICAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmZpbmlzaCgxMSk7CiAgICAgICAgICAgIGNhc2UgMTQ6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzEsIDgsIDExLCAxNF1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5Yqg6L295omT5Y2w5py65YiX6KGoCiAgICBsb2FkUHJpbnRlcnM6IGZ1bmN0aW9uIGxvYWRQcmludGVycygpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHJldHVybiBnZXRQcmludGVyTGlzdCh7CiAgICAgICAgdHlwZTogJ0xBQkVMJwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi5sYWJlbFByaW50ZXJzID0gcmVzcG9uc2UuZGF0YS5wYWdpbmF0aW9uUmVzcG9uc2UuY29udGVudC5maWx0ZXIoZnVuY3Rpb24gKHApIHsKICAgICAgICAgIHJldHVybiBwLnR5cGUgPT09ICdMQUJFTCc7CiAgICAgICAgfSk7CiAgICAgICAgX3RoaXMyLnN0b3JlTGlzdCA9IHJlc3BvbnNlLmRhdGEuc3RvcmVMaXN0IHx8IFtdOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDliqDovb3liIbnsbvliJfooagKICAgIGxvYWRDYXRlZ29yaWVzOiBmdW5jdGlvbiBsb2FkQ2F0ZWdvcmllcygpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHJldHVybiBnZXRDYXRlZ29yeUxpc3QoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMy5jYXRlZ29yeUxpc3QgPSByZXNwb25zZS5kYXRhIHx8IFtdOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDliqDovb3miYDmnInmiZPljbDmnLrnmoTliIbnsbvlhbPogZQKICAgIGxvYWRBbGxQcmludGVyQ2F0ZWdvcmllczogZnVuY3Rpb24gbG9hZEFsbFByaW50ZXJDYXRlZ29yaWVzKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIF9pdGVyYXRvciwgX3N0ZXAsIHByaW50ZXIsIHJlc3BvbnNlOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfdGhpczQucHJpbnRlckNhdGVnb3JpZXNNYXAgPSB7fTsKICAgICAgICAgICAgICBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihfdGhpczQubGFiZWxQcmludGVycyk7CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAyOwogICAgICAgICAgICAgIF9pdGVyYXRvci5zKCk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBpZiAoKF9zdGVwID0gX2l0ZXJhdG9yLm4oKSkuZG9uZSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAxODsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBwcmludGVyID0gX3N0ZXAudmFsdWU7CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSA2OwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gOTsKICAgICAgICAgICAgICByZXR1cm4gZ2V0UHJpbnRlckNhdGVnb3JpZXMocHJpbnRlci5pZCk7CiAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0Mi5zZW50OwogICAgICAgICAgICAgIF90aGlzNC4kc2V0KF90aGlzNC5wcmludGVyQ2F0ZWdvcmllc01hcCwgcHJpbnRlci5pZCwgcmVzcG9uc2UuZGF0YSB8fCBbXSk7CiAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAxNjsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDEzOwogICAgICAgICAgICAgIF9jb250ZXh0Mi50MCA9IF9jb250ZXh0MlsiY2F0Y2giXSg2KTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCJcdTUyQTBcdThGN0RcdTYyNTNcdTUzNzBcdTY3M0EiLmNvbmNhdChwcmludGVyLmlkLCAiXHU1MjA2XHU3QzdCXHU1MTczXHU4MDU0XHU1OTMxXHU4RDI1OiIpLCBfY29udGV4dDIudDApOwogICAgICAgICAgICBjYXNlIDE2OgogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gNDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAxODoKICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDIzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDIwOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gMjA7CiAgICAgICAgICAgICAgX2NvbnRleHQyLnQxID0gX2NvbnRleHQyWyJjYXRjaCJdKDIpOwogICAgICAgICAgICAgIF9pdGVyYXRvci5lKF9jb250ZXh0Mi50MSk7CiAgICAgICAgICAgIGNhc2UgMjM6CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAyMzsKICAgICAgICAgICAgICBfaXRlcmF0b3IuZigpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuZmluaXNoKDIzKTsKICAgICAgICAgICAgY2FzZSAyNjoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMiwgbnVsbCwgW1syLCAyMCwgMjMsIDI2XSwgWzYsIDEzXV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDpgInmi6nmiZPljbDmnLoKICAgIHNlbGVjdFByaW50ZXI6IGZ1bmN0aW9uIHNlbGVjdFByaW50ZXIocHJpbnRlcikgewogICAgICB0aGlzLmZvcm0ucHJpbnRlcklkID0gcHJpbnRlci5pZDsKICAgICAgdGhpcy5vblByaW50ZXJDaGFuZ2UoKTsKICAgIH0sCiAgICAvLyDmiZPljbDmnLrlj5jljJbkuovku7YKICAgIG9uUHJpbnRlckNoYW5nZTogZnVuY3Rpb24gb25QcmludGVyQ2hhbmdlKCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgaWYgKCF0aGlzLmZvcm0ucHJpbnRlcklkKSB7CiAgICAgICAgdGhpcy5mb3JtLmNhdGVJZHMgPSBbXTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5jYXRlZ29yeUxvYWRpbmcgPSB0cnVlOwogICAgICBnZXRQcmludGVyQ2F0ZWdvcmllcyh0aGlzLmZvcm0ucHJpbnRlcklkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIHZhciBwcmludGVyQ2F0ZWdvcmllcyA9IHJlc3BvbnNlLmRhdGEgfHwgW107CiAgICAgICAgX3RoaXM1LmZvcm0uY2F0ZUlkcyA9IHByaW50ZXJDYXRlZ29yaWVzLm1hcChmdW5jdGlvbiAocGMpIHsKICAgICAgICAgIHJldHVybiBwYy5jYXRlSWQ7CiAgICAgICAgfSk7CiAgICAgICAgX3RoaXM1LmNhdGVnb3J5TG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3miZPljbDmnLrliIbnsbvlhbPogZTlpLHotKU6JywgZXJyb3IpOwogICAgICAgIF90aGlzNS5jYXRlZ29yeUxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5L+d5a2Y6YWN572uCiAgICBzYXZlQ29uZmlnOiBmdW5jdGlvbiBzYXZlQ29uZmlnKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgaWYgKCF0aGlzLmZvcm0ucHJpbnRlcklkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nmiZPljbDmnLonKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5zYXZpbmcgPSB0cnVlOwogICAgICB2YXIgZGF0YSA9IHsKICAgICAgICBwcmludGVySWQ6IHRoaXMuZm9ybS5wcmludGVySWQsCiAgICAgICAgY2F0ZUlkczogdGhpcy5mb3JtLmNhdGVJZHMKICAgICAgfTsKICAgICAgc2F2ZVByaW50ZXJDYXRlZ29yaWVzKGRhdGEpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM2LiRtZXNzYWdlLnN1Y2Nlc3MoJ+mFjee9ruS/neWtmOaIkOWKnycpOwogICAgICAgIF90aGlzNi5sb2FkQWxsUHJpbnRlckNhdGVnb3JpZXMoKTsKICAgICAgICBfdGhpczYuc2F2aW5nID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOmFjee9ruWksei0pTonLCBlcnJvcik7CiAgICAgICAgX3RoaXM2LnNhdmluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDph43nva7ooajljZUKICAgIHJlc2V0Rm9ybTogZnVuY3Rpb24gcmVzZXRGb3JtKCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgcHJpbnRlcklkOiAnJywKICAgICAgICBjYXRlSWRzOiBbXQogICAgICB9OwogICAgfSwKICAgIC8vIOWIt+aWsOaVsOaNrgogICAgcmVmcmVzaERhdGE6IGZ1bmN0aW9uIHJlZnJlc2hEYXRhKCkgewogICAgICB0aGlzLmxvYWREYXRhKCk7CiAgICB9LAogICAgLy8g6I635Y+W5omT5Y2w5py65YiG57G75YWz6IGUCiAgICBnZXRQcmludGVyQ2F0ZWdvcmllczogZnVuY3Rpb24gZ2V0UHJpbnRlckNhdGVnb3JpZXMocHJpbnRlcklkKSB7CiAgICAgIHJldHVybiB0aGlzLnByaW50ZXJDYXRlZ29yaWVzTWFwW3ByaW50ZXJJZF0gfHwgW107CiAgICB9LAogICAgLy8g6I635Y+W5omT5Y2w5py65YiG57G75ZCN56ewCiAgICBnZXRQcmludGVyQ2F0ZWdvcnlOYW1lczogZnVuY3Rpb24gZ2V0UHJpbnRlckNhdGVnb3J5TmFtZXMocHJpbnRlcklkKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB2YXIgcHJpbnRlckNhdGVnb3JpZXMgPSB0aGlzLmdldFByaW50ZXJDYXRlZ29yaWVzKHByaW50ZXJJZCk7CiAgICAgIHJldHVybiBwcmludGVyQ2F0ZWdvcmllcy5tYXAoZnVuY3Rpb24gKHBjKSB7CiAgICAgICAgdmFyIGNhdGVnb3J5ID0gX3RoaXM3LmNhdGVnb3J5TGlzdC5maW5kKGZ1bmN0aW9uIChjKSB7CiAgICAgICAgICByZXR1cm4gYy5pZCA9PT0gcGMuY2F0ZUlkOwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBjYXRlZ29yeSA/IGNhdGVnb3J5Lm5hbWUgOiAiXHU1MjA2XHU3QzdCIi5jb25jYXQocGMuY2F0ZUlkKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6I635Y+W5bqX6ZO65ZCN56ewCiAgICBnZXRTdG9yZU5hbWU6IGZ1bmN0aW9uIGdldFN0b3JlTmFtZShzdG9yZUlkKSB7CiAgICAgIGlmICghc3RvcmVJZCB8fCBzdG9yZUlkID09PSAwKSByZXR1cm4gJ+WFrOWFseaJgOaciSc7CiAgICAgIHZhciBzdG9yZSA9IHRoaXMuc3RvcmVMaXN0LmZpbmQoZnVuY3Rpb24gKHMpIHsKICAgICAgICByZXR1cm4gcy5pZCA9PT0gc3RvcmVJZDsKICAgICAgfSk7CiAgICAgIHJldHVybiBzdG9yZSA/IHN0b3JlLm5hbWUgOiAn5pyq55+l5bqX6ZO6JzsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["getPrinterList", "getCategoryList", "savePrinterCategories", "getPrinterCategories", "name", "data", "loading", "saving", "categoryLoading", "form", "printerId", "cateIds", "labelPrinters", "categoryList", "storeList", "printerCategoriesMap", "created", "loadData", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "Promise", "all", "loadPrinters", "loadCategories", "loadAllPrinterCategories", "t0", "console", "error", "finish", "stop", "_this2", "type", "then", "response", "paginationResponse", "content", "filter", "p", "_this3", "_this4", "_callee2", "_iterator", "_step", "printer", "_callee2$", "_context2", "_createForOfIteratorHelper", "s", "n", "done", "value", "id", "sent", "$set", "concat", "t1", "e", "f", "selectPrinter", "onPrinterChange", "_this5", "printerCategories", "map", "pc", "cateId", "catch", "saveConfig", "_this6", "$message", "warning", "success", "resetForm", "refreshData", "getPrinterCategoryNames", "_this7", "category", "find", "c", "getStoreName", "storeId", "store"], "sources": ["src/views/printer/category-config.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"page-header\">\n      <h2>打印机分类配置</h2>\n      <p>为标签打印机配置对应的商品分类，实现按分类智能打印</p>\n    </div>\n\n    <!-- 配置表单 -->\n    <el-card class=\"config-card\" shadow=\"hover\">\n      <div slot=\"header\">\n        <span>配置打印机分类关联</span>\n      </div>\n\n      <el-form :model=\"form\" label-width=\"120px\" class=\"config-form\">\n        <el-form-item label=\"选择打印机\" required>\n          <el-select\n            v-model=\"form.printerId\"\n            placeholder=\"请选择标签打印机\"\n            @change=\"onPrinterChange\"\n            style=\"width: 100%\"\n            clearable\n          >\n            <el-option\n              v-for=\"printer in labelPrinters\"\n              :key=\"printer.id\"\n              :label=\"`${printer.name} (${printer.sn})`\"\n              :value=\"printer.id\"\n            >\n              <span style=\"float: left\">{{ printer.name }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ printer.sn }}</span>\n            </el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"关联分类\" v-if=\"form.printerId\">\n          <div v-loading=\"categoryLoading\">\n            <el-checkbox-group v-model=\"form.cateIds\" class=\"category-group\">\n              <el-checkbox\n                v-for=\"category in categoryList\"\n                :key=\"category.id\"\n                :label=\"category.id\"\n                class=\"category-item\"\n              >\n                <span class=\"category-name\">{{ category.name }}</span>\n                <span class=\"category-desc\" v-if=\"category.description\">{{ category.description }}</span>\n              </el-checkbox>\n            </el-checkbox-group>\n          </div>\n        </el-form-item>\n\n        <el-form-item v-if=\"form.printerId\">\n          <el-button type=\"primary\" @click=\"saveConfig\" :loading=\"saving\" icon=\"el-icon-check\">\n            保存配置\n          </el-button>\n          <el-button @click=\"resetForm\" icon=\"el-icon-refresh\">\n            重置\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 当前配置展示 -->\n    <el-card class=\"status-card\" shadow=\"hover\">\n      <div slot=\"header\">\n        <span>当前配置状态</span>\n        <el-button\n          style=\"float: right; padding: 3px 0\"\n          type=\"text\"\n          @click=\"refreshData\"\n          :loading=\"loading\"\n        >\n          刷新\n        </el-button>\n      </div>\n\n      <div v-loading=\"loading\" class=\"printer-grid\">\n        <div\n          v-for=\"printer in labelPrinters\"\n          :key=\"printer.id\"\n          class=\"printer-card\"\n          :class=\"{ 'active': form.printerId === printer.id }\"\n          @click=\"selectPrinter(printer)\"\n        >\n          <div class=\"printer-header\">\n            <h4>{{ printer.name }}</h4>\n            <el-tag :type=\"printer.status === 'A' ? 'success' : 'danger'\" size=\"small\">\n              {{ printer.status === 'A' ? '启用' : '禁用' }}\n            </el-tag>\n          </div>\n\n          <div class=\"printer-info\">\n            <p><strong>编号:</strong> {{ printer.sn }}</p>\n            <p><strong>店铺:</strong> {{ getStoreName(printer.storeId) }}</p>\n          </div>\n\n          <div class=\"category-section\">\n            <p class=\"section-title\">关联分类:</p>\n            <div class=\"category-tags\">\n              <template v-if=\"getPrinterCategories(printer.id).length > 0\">\n                <el-tag\n                  v-for=\"categoryName in getPrinterCategoryNames(printer.id)\"\n                  :key=\"categoryName\"\n                  size=\"mini\"\n                  type=\"primary\"\n                  class=\"category-tag\"\n                >\n                  {{ categoryName }}\n                </el-tag>\n              </template>\n              <span v-else class=\"no-category\">未配置分类</span>\n            </div>\n          </div>\n\n          <div class=\"printer-actions\">\n            <el-button\n              size=\"mini\"\n              type=\"primary\"\n              @click.stop=\"selectPrinter(printer)\"\n              icon=\"el-icon-setting\"\n            >\n              配置\n            </el-button>\n          </div>\n        </div>\n      </div>\n\n      <div v-if=\"labelPrinters.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无标签打印机\">\n          <el-button type=\"primary\" @click=\"$router.push('/printer')\">\n            去添加打印机\n          </el-button>\n        </el-empty>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { getPrinterList, getCategoryList, savePrinterCategories, getPrinterCategories } from \"@/api/printer\";\n\nexport default {\n  name: \"PrinterCategoryConfig\",\n  data() {\n    return {\n      loading: false,\n      saving: false,\n      categoryLoading: false,\n\n      // 表单数据\n      form: {\n        printerId: '',\n        cateIds: []\n      },\n\n      // 基础数据\n      labelPrinters: [],\n      categoryList: [],\n      storeList: [],\n      printerCategoriesMap: {}\n    };\n  },\n\n  created() {\n    this.loadData();\n  },\n\n  methods: {\n    // 加载所有数据\n    async loadData() {\n      this.loading = true;\n      try {\n        await Promise.all([\n          this.loadPrinters(),\n          this.loadCategories()\n        ]);\n        await this.loadAllPrinterCategories();\n      } catch (error) {\n        console.error('加载数据失败:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 加载打印机列表\n    loadPrinters() {\n      return getPrinterList({ type: 'LABEL' }).then(response => {\n        this.labelPrinters = response.data.paginationResponse.content.filter(p => p.type === 'LABEL');\n        this.storeList = response.data.storeList || [];\n      });\n    },\n\n    // 加载分类列表\n    loadCategories() {\n      return getCategoryList().then(response => {\n        this.categoryList = response.data || [];\n      });\n    },\n\n    // 加载所有打印机的分类关联\n    async loadAllPrinterCategories() {\n      this.printerCategoriesMap = {};\n\n      for (const printer of this.labelPrinters) {\n        try {\n          const response = await getPrinterCategories(printer.id);\n          this.$set(this.printerCategoriesMap, printer.id, response.data || []);\n        } catch (error) {\n          console.error(`加载打印机${printer.id}分类关联失败:`, error);\n        }\n      }\n    },\n\n    // 选择打印机\n    selectPrinter(printer) {\n      this.form.printerId = printer.id;\n      this.onPrinterChange();\n    },\n\n    // 打印机变化事件\n    onPrinterChange() {\n      if (!this.form.printerId) {\n        this.form.cateIds = [];\n        return;\n      }\n\n      this.categoryLoading = true;\n      getPrinterCategories(this.form.printerId).then(response => {\n        const printerCategories = response.data || [];\n        this.form.cateIds = printerCategories.map(pc => pc.cateId);\n        this.categoryLoading = false;\n      }).catch(error => {\n        console.error('加载打印机分类关联失败:', error);\n        this.categoryLoading = false;\n      });\n    },\n\n    // 保存配置\n    saveConfig() {\n      if (!this.form.printerId) {\n        this.$message.warning('请先选择打印机');\n        return;\n      }\n\n      this.saving = true;\n\n      const data = {\n        printerId: this.form.printerId,\n        cateIds: this.form.cateIds\n      };\n\n      savePrinterCategories(data).then(response => {\n        this.$message.success('配置保存成功');\n        this.loadAllPrinterCategories();\n        this.saving = false;\n      }).catch(error => {\n        console.error('保存配置失败:', error);\n        this.saving = false;\n      });\n    },\n\n    // 重置表单\n    resetForm() {\n      this.form = {\n        printerId: '',\n        cateIds: []\n      };\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.loadData();\n    },\n\n    // 获取打印机分类关联\n    getPrinterCategories(printerId) {\n      return this.printerCategoriesMap[printerId] || [];\n    },\n\n    // 获取打印机分类名称\n    getPrinterCategoryNames(printerId) {\n      const printerCategories = this.getPrinterCategories(printerId);\n      return printerCategories.map(pc => {\n        const category = this.categoryList.find(c => c.id === pc.cateId);\n        return category ? category.name : `分类${pc.cateId}`;\n      });\n    },\n\n    // 获取店铺名称\n    getStoreName(storeId) {\n      if (!storeId || storeId === 0) return '公共所有';\n      const store = this.storeList.find(s => s.id === storeId);\n      return store ? store.name : '未知店铺';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px 0;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.page-header h2 {\n  margin: 0 0 8px 0;\n  color: #303133;\n}\n\n.page-header p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.config-card,\n.status-card {\n  margin-bottom: 20px;\n}\n\n.config-form {\n  max-width: 800px;\n}\n\n.category-group {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n  padding: 16px;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  background-color: #fafafa;\n}\n\n.category-item {\n  margin: 0 !important;\n  padding: 12px;\n  border: 1px solid #e0e0e0;\n  border-radius: 6px;\n  background: white;\n  transition: all 0.3s;\n  cursor: pointer;\n}\n\n.category-item:hover {\n  background: #f0f8ff;\n  border-color: #409eff;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n}\n\n.category-name {\n  display: block;\n  font-weight: 500;\n  color: #303133;\n}\n\n.category-desc {\n  display: block;\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.printer-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 20px;\n}\n\n.printer-card {\n  border: 2px solid #e4e7ed;\n  border-radius: 8px;\n  padding: 20px;\n  background: white;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.printer-card:hover {\n  border-color: #409eff;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.printer-card.active {\n  border-color: #409eff;\n  background: #f0f8ff;\n}\n\n.printer-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.printer-header h4 {\n  margin: 0;\n  color: #303133;\n}\n\n.printer-info {\n  margin-bottom: 16px;\n}\n\n.printer-info p {\n  margin: 4px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.category-section {\n  margin-bottom: 16px;\n}\n\n.section-title {\n  margin: 0 0 8px 0;\n  font-weight: 500;\n  color: #303133;\n  font-size: 14px;\n}\n\n.category-tags {\n  min-height: 24px;\n}\n\n.category-tag {\n  margin: 2px 4px 2px 0;\n}\n\n.no-category {\n  color: #c0c4cc;\n  font-style: italic;\n  font-size: 13px;\n}\n\n.printer-actions {\n  text-align: right;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 0;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA,SAAAA,cAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,oBAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,MAAA;MACAC,eAAA;MAEA;MACAC,IAAA;QACAC,SAAA;QACAC,OAAA;MACA;MAEA;MACAC,aAAA;MACAC,YAAA;MACAC,SAAA;MACAC,oBAAA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EAEAC,OAAA;IACA;IACAD,QAAA,WAAAA,SAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,KAAA,CAAAb,OAAA;cAAAoB,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAC,OAAA,CAAAC,GAAA,EACAX,KAAA,CAAAY,YAAA,IACAZ,KAAA,CAAAa,cAAA,GACA;YAAA;cAAAN,QAAA,CAAAE,IAAA;cAAA,OACAT,KAAA,CAAAc,wBAAA;YAAA;cAAAP,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEAS,OAAA,CAAAC,KAAA,YAAAV,QAAA,CAAAQ,EAAA;YAAA;cAAAR,QAAA,CAAAC,IAAA;cAEAR,KAAA,CAAAb,OAAA;cAAA,OAAAoB,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAf,OAAA;MAAA;IAEA;IAEA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAQ,MAAA;MACA,OAAAvC,cAAA;QAAAwC,IAAA;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA3B,aAAA,GAAA8B,QAAA,CAAArC,IAAA,CAAAsC,kBAAA,CAAAC,OAAA,CAAAC,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAN,IAAA;QAAA;QACAD,MAAA,CAAAzB,SAAA,GAAA4B,QAAA,CAAArC,IAAA,CAAAS,SAAA;MACA;IACA;IAEA;IACAkB,cAAA,WAAAA,eAAA;MAAA,IAAAe,MAAA;MACA,OAAA9C,eAAA,GAAAwC,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAAlC,YAAA,GAAA6B,QAAA,CAAArC,IAAA;MACA;IACA;IAEA;IACA4B,wBAAA,WAAAA,yBAAA;MAAA,IAAAe,MAAA;MAAA,OAAA5B,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAA;QAAA,IAAAC,SAAA,EAAAC,KAAA,EAAAC,OAAA,EAAAV,QAAA;QAAA,OAAArB,mBAAA,GAAAG,IAAA,UAAA6B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3B,IAAA,GAAA2B,SAAA,CAAA1B,IAAA;YAAA;cACAoB,MAAA,CAAAjC,oBAAA;cAAAmC,SAAA,GAAAK,0BAAA,CAEAP,MAAA,CAAApC,aAAA;cAAA0C,SAAA,CAAA3B,IAAA;cAAAuB,SAAA,CAAAM,CAAA;YAAA;cAAA,KAAAL,KAAA,GAAAD,SAAA,CAAAO,CAAA,IAAAC,IAAA;gBAAAJ,SAAA,CAAA1B,IAAA;gBAAA;cAAA;cAAAwB,OAAA,GAAAD,KAAA,CAAAQ,KAAA;cAAAL,SAAA,CAAA3B,IAAA;cAAA2B,SAAA,CAAA1B,IAAA;cAAA,OAEAzB,oBAAA,CAAAiD,OAAA,CAAAQ,EAAA;YAAA;cAAAlB,QAAA,GAAAY,SAAA,CAAAO,IAAA;cACAb,MAAA,CAAAc,IAAA,CAAAd,MAAA,CAAAjC,oBAAA,EAAAqC,OAAA,CAAAQ,EAAA,EAAAlB,QAAA,CAAArC,IAAA;cAAAiD,SAAA,CAAA1B,IAAA;cAAA;YAAA;cAAA0B,SAAA,CAAA3B,IAAA;cAAA2B,SAAA,CAAApB,EAAA,GAAAoB,SAAA;cAEAnB,OAAA,CAAAC,KAAA,kCAAA2B,MAAA,CAAAX,OAAA,CAAAQ,EAAA,4CAAAN,SAAA,CAAApB,EAAA;YAAA;cAAAoB,SAAA,CAAA1B,IAAA;cAAA;YAAA;cAAA0B,SAAA,CAAA1B,IAAA;cAAA;YAAA;cAAA0B,SAAA,CAAA3B,IAAA;cAAA2B,SAAA,CAAAU,EAAA,GAAAV,SAAA;cAAAJ,SAAA,CAAAe,CAAA,CAAAX,SAAA,CAAAU,EAAA;YAAA;cAAAV,SAAA,CAAA3B,IAAA;cAAAuB,SAAA,CAAAgB,CAAA;cAAA,OAAAZ,SAAA,CAAAjB,MAAA;YAAA;YAAA;cAAA,OAAAiB,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IAGA;IAEA;IACAkB,aAAA,WAAAA,cAAAf,OAAA;MACA,KAAA3C,IAAA,CAAAC,SAAA,GAAA0C,OAAA,CAAAQ,EAAA;MACA,KAAAQ,eAAA;IACA;IAEA;IACAA,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,UAAA5D,IAAA,CAAAC,SAAA;QACA,KAAAD,IAAA,CAAAE,OAAA;QACA;MACA;MAEA,KAAAH,eAAA;MACAL,oBAAA,MAAAM,IAAA,CAAAC,SAAA,EAAA+B,IAAA,WAAAC,QAAA;QACA,IAAA4B,iBAAA,GAAA5B,QAAA,CAAArC,IAAA;QACAgE,MAAA,CAAA5D,IAAA,CAAAE,OAAA,GAAA2D,iBAAA,CAAAC,GAAA,WAAAC,EAAA;UAAA,OAAAA,EAAA,CAAAC,MAAA;QAAA;QACAJ,MAAA,CAAA7D,eAAA;MACA,GAAAkE,KAAA,WAAAtC,KAAA;QACAD,OAAA,CAAAC,KAAA,iBAAAA,KAAA;QACAiC,MAAA,CAAA7D,eAAA;MACA;IACA;IAEA;IACAmE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,UAAAnE,IAAA,CAAAC,SAAA;QACA,KAAAmE,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAvE,MAAA;MAEA,IAAAF,IAAA;QACAK,SAAA,OAAAD,IAAA,CAAAC,SAAA;QACAC,OAAA,OAAAF,IAAA,CAAAE;MACA;MAEAT,qBAAA,CAAAG,IAAA,EAAAoC,IAAA,WAAAC,QAAA;QACAkC,MAAA,CAAAC,QAAA,CAAAE,OAAA;QACAH,MAAA,CAAA3C,wBAAA;QACA2C,MAAA,CAAArE,MAAA;MACA,GAAAmE,KAAA,WAAAtC,KAAA;QACAD,OAAA,CAAAC,KAAA,YAAAA,KAAA;QACAwC,MAAA,CAAArE,MAAA;MACA;IACA;IAEA;IACAyE,SAAA,WAAAA,UAAA;MACA,KAAAvE,IAAA;QACAC,SAAA;QACAC,OAAA;MACA;IACA;IAEA;IACAsE,WAAA,WAAAA,YAAA;MACA,KAAAhE,QAAA;IACA;IAEA;IACAd,oBAAA,WAAAA,qBAAAO,SAAA;MACA,YAAAK,oBAAA,CAAAL,SAAA;IACA;IAEA;IACAwE,uBAAA,WAAAA,wBAAAxE,SAAA;MAAA,IAAAyE,MAAA;MACA,IAAAb,iBAAA,QAAAnE,oBAAA,CAAAO,SAAA;MACA,OAAA4D,iBAAA,CAAAC,GAAA,WAAAC,EAAA;QACA,IAAAY,QAAA,GAAAD,MAAA,CAAAtE,YAAA,CAAAwE,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA1B,EAAA,KAAAY,EAAA,CAAAC,MAAA;QAAA;QACA,OAAAW,QAAA,GAAAA,QAAA,CAAAhF,IAAA,kBAAA2D,MAAA,CAAAS,EAAA,CAAAC,MAAA;MACA;IACA;IAEA;IACAc,YAAA,WAAAA,aAAAC,OAAA;MACA,KAAAA,OAAA,IAAAA,OAAA;MACA,IAAAC,KAAA,QAAA3E,SAAA,CAAAuE,IAAA,WAAA7B,CAAA;QAAA,OAAAA,CAAA,CAAAI,EAAA,KAAA4B,OAAA;MAAA;MACA,OAAAC,KAAA,GAAAA,KAAA,CAAArF,IAAA;IACA;EACA;AACA", "ignoreList": []}]}