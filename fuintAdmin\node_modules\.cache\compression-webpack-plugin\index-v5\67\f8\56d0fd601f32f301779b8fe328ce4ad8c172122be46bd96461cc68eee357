
465c4b88ffa413511f7b2c66b3ae53674d89d843	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"fb5d32f46df387ce3213167f683c5dfb\"}","integrity":"sha512-/x7NLZiAl5cHKdjVwr6pv36lxxk/WPykzd2XmihdPACQ/3VKJB2SDG9XvmkgM4dMQsl0eJkZOUO5GwtMm9LigQ==","time":1754921993853,"size":6668801}