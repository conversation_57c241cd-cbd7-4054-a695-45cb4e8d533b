
ddeba7fc32418f9db1c254af1a2c1e7c233abed7	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fchunk-vendors.js\",\"contentHash\":\"4c05e2754f584559fe440960e0cee9ae\"}","integrity":"sha512-ghuhCKGx6jrG0mP1OFeTh3zIaepy+Pp04JgcToAZ+NBY1cF3cJmfQ7uIplTsQ3cLzy5YdDj47eB/A5zUuycOPw==","time":1754920242457,"size":16672071}