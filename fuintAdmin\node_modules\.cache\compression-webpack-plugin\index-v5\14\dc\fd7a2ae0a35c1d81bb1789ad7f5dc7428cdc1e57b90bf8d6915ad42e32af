
9ed023677aa425108eeaadfdcc9ad479a13da29b	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"1fd704c5c904a88b28b8730f36efd3be\"}","integrity":"sha512-rUQixzFyoViOYBf4dCQnwg+BLnUoicPslp3E5QiQfwoHPnzZdA3UGzz1q9yzwtYgc5MQsh8BsgrHADZpovK7wQ==","time":1754921568068,"size":2882582}