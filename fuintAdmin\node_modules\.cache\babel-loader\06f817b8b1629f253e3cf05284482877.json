{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\index.vue", "mtime": 1754922008756}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getPrinterList", "updatePrinterStatus", "getPrinterInfo", "savePrinter", "saveSetting", "getSettingInfo", "printExpiryLabel", "getCategoryList", "savePrinterCategories", "getPrinterCategories", "name", "data", "loading", "title", "ids", "multiple", "showSearch", "total", "list", "open", "defaultSort", "prop", "order", "form", "id", "sn", "beforePay", "afterPay", "autoPrint", "storeId", "type", "status", "sort", "storeList", "queryParams", "page", "pageSize", "rules", "required", "message", "trigger", "min", "max", "openSetting", "settingForm", "userName", "<PERSON><PERSON><PERSON>", "enable", "settingRules", "printLabelOpen", "currentPrinterId", "labelForm", "productDate", "expireDays", "labelRules", "categoryConfigOpen", "categoryLoading", "categorySubmitting", "categoryList", "selectedCategories", "currentPrinter", "printerCategoriesMap", "created", "getList", "methods", "_this", "then", "response", "paginationResponse", "content", "totalElements", "loadPrinterCategories", "handleQuery", "handleSetting", "_this2", "reset<PERSON><PERSON>y", "resetForm", "$refs", "tables", "handleStatusChange", "row", "_this3", "text", "$modal", "confirm", "msgSuccess", "catch", "handleSelectionChange", "selection", "map", "item", "length", "handleSortChange", "column", "orderByColumn", "isAsc", "handleAdd", "reset", "description", "cancel", "submitForm", "_this4", "validate", "valid", "handleUpdate", "_this5", "printerInfo", "handleDelete", "_this6", "submitSettingForm", "_this7", "cancelSetting", "calculateExpireDate", "days", "date", "Date", "setDate", "getDate", "getFullYear", "String", "getMonth", "padStart", "handlePrintLabel", "cancelPrintLabel", "submitLabelForm", "_this8", "expireDate", "printerId", "loadCategoryList", "_this9", "params", "undefined", "error", "console", "_this10", "labelPrinters", "filter", "printer", "for<PERSON>ach", "$set", "concat", "getPrinterCategoryNames", "_this11", "printerCategories", "pc", "category", "find", "c", "cateId", "handleCategoryConfig", "_this12", "submitCategoryConfig", "_this13", "cateIds", "cancelCategoryConfig"], "sources": ["src/views/printer/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" class=\"main-search\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"设备名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入打印机名称\"\n          clearable\n          style=\"width: 200px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"编号\" prop=\"sn\">\n        <el-input\n          v-model=\"queryParams.sn\"\n          placeholder=\"请输入打印机编号\"\n          clearable\n          style=\"width: 200px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"所属店铺\" prop=\"store\">\n        <el-select\n          v-model=\"queryParams.storeId\"\n          placeholder=\"所属店铺\"\n          clearable\n          style=\"width: 180px\"\n        >\n          <el-option v-for=\"storeInfo in storeList\" :key=\"storeInfo.id\" :label=\"storeInfo.name\" :value=\"storeInfo.id\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 100px\"\n        >\n          <el-option key=\"A\" label=\"启用\" value=\"A\"/>\n          <el-option key=\"N\" label=\"禁用\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['printer:index']\"\n        >新增云打印设备</el-button>\n\n        <!-- <el-button type=\"primary\" icon=\"el-icon-setting\" size=\"mini\" v-hasPermi=\"['printer:setting']\" @click=\"handleSetting\">设置芯烨账号</el-button> -->\n      </el-form-item>\n    </el-form>\n\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\n      <el-table-column label=\"ID\" prop=\"id\" width=\"55\"/>\n      <el-table-column label=\"设备名称\" align=\"center\" prop=\"name\" />\n      <el-table-column label=\"设备编号\" align=\"center\" prop=\"sn\" />\n      <el-table-column label=\"类型\" align=\"center\" prop=\"type\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.type === 'LABEL' ? 'success' : 'primary'\" size=\"small\">\n            {{ scope.row.type === 'LABEL' ? '标签' : '小票' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所属店铺\" align=\"center\" prop=\"store\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.storeId && scope.row.storeId > 0\">\n              <span>{{ getName(storeList, scope.row.storeId) }}</span>\n          </span>\n          <span v-else>公共所有</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.updateTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"A\"\n            inactive-value=\"N\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            v-hasPermi=\"['printer:index']\"\n            @click=\"handleUpdate(scope.row)\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            v-hasPermi=\"['printer:index']\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-printer\"\n            v-hasPermi=\"['printer:index']\"\n            v-if=\"scope.row.type === 'LABEL'\"\n            @click=\"handlePrintLabel(scope.row)\"\n          >打印标签</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-setting\"\n            v-hasPermi=\"['printer:index']\"\n            v-if=\"scope.row.type === 'LABEL'\"\n            @click=\"handleCategoryConfig(scope.row)\"\n          >分类配置</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.page\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改对话框 start-->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" class=\"common-dialog\" width=\"700px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备名称\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入打印机名称\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备编号\" prop=\"sn\">\n              <el-input v-model=\"form.sn\" placeholder=\"请输入打印机编号\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"所属店铺\" prop=\"storeId\">\n              <el-select\n                v-model=\"form.storeId\"\n                style=\"width: 260px\"\n                placeholder=\"所属店铺，空则为公共所有\"\n              >\n                <el-option :key=\"0\" label=\"公共所有\" v-if=\"!this.$store.getters.storeId\" :value=\"0\"/>\n                <el-option v-for=\"storeInfo in storeList\" :key=\"storeInfo.id\" :label=\"storeInfo.name\" :value=\"storeInfo.id\"/>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"打印机类型\">\n              <el-radio-group v-model=\"form.type\">\n                <el-radio key=\"RECEIPT\" label=\"RECEIPT\" value=\"RECEIPT\">小票打印机</el-radio>\n                <el-radio key=\"LABEL\" label=\"LABEL\" value=\"LABEL\">标签打印机</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"支付前打印\">\n              <el-radio-group v-model=\"form.beforePay\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">是</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"支付后打印\">\n              <el-radio-group v-model=\"form.afterPay\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">是</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"打印订单\">\n              <el-radio-group v-model=\"form.autoPrint\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">自动打印</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">手动打印</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注信息\">\n              <el-input v-model=\"form.description\" type=\"textarea\" rows=\"5\" placeholder=\"请输入备注信息\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio key=\"A\" label=\"A\" value=\"A\">启用</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">禁用</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n        <el-button @click=\"cancel\">取消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改对话框 end-->\n\n    <!-- 打印设置对话框 start-->\n    <el-dialog title=\"芯烨云打印设置\" :visible.sync=\"openSetting\" class=\"common-dialog\" width=\"700px\" append-to-body>\n      <el-form ref=\"settingForm\" :model=\"settingForm\" :rules=\"settingRules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"开发者ID\" prop=\"userName\">\n              <el-input v-model=\"settingForm.userName\" placeholder=\"请输入开发者ID\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"开发者密钥\" prop=\"userKey\">\n              <el-input v-model=\"settingForm.userKey\" placeholder=\"请输入开发者密钥\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"settingForm.enable\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">启用</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">禁用</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitSettingForm\">确定</el-button>\n        <el-button @click=\"cancelSetting\">取消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 打印设置对话框 end-->\n\n    <!-- 打印标签对话框 -->\n    <el-dialog title=\"打印标签\" :visible.sync=\"printLabelOpen\" class=\"common-dialog\" width=\"500px\" append-to-body>\n      <el-form ref=\"labelForm\" :model=\"labelForm\" :rules=\"labelRules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"名称\" prop=\"name\">\n              <el-input v-model=\"labelForm.name\" placeholder=\"请输入名称\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产日期\" prop=\"productDate\">\n              <el-date-picker\n                v-model=\"labelForm.productDate\"\n                type=\"date\"\n                style=\"width: 100%\"\n                placeholder=\"选择生产日期\"\n                value-format=\"yyyy-MM-dd\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"有效期(天)\" prop=\"expireDays\">\n              <el-input-number\n                v-model=\"labelForm.expireDays\"\n                :min=\"1\"\n                :max=\"999\"\n                style=\"width: 100%\"\n                placeholder=\"请输入有效期天数\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitLabelForm\">确定</el-button>\n        <el-button @click=\"cancelPrintLabel\">取消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 分类配置对话框 -->\n    <el-dialog title=\"打印机分类配置\" :visible.sync=\"categoryConfigOpen\" class=\"common-dialog\" width=\"600px\" append-to-body>\n      <div style=\"margin-bottom: 20px;\">\n        <el-alert\n          title=\"配置说明\"\n          description=\"为标签打印机配置对应的商品分类。下方显示该打印机所属店铺的所有商品分类，可多选配置。打印时会优先使用配置了分类的打印机，未配置的使用通用打印机。\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon>\n        </el-alert>\n      </div>\n\n      <el-form label-width=\"120px\">\n        <el-form-item label=\"打印机\">\n          <el-input :value=\"currentPrinter.name + ' (' + currentPrinter.sn + ')'\" disabled />\n        </el-form-item>\n\n        <el-form-item label=\"关联分类\">\n          <div v-loading=\"categoryLoading\">\n            <el-checkbox-group v-model=\"selectedCategories\" class=\"category-checkbox-group\">\n              <el-checkbox\n                v-for=\"category in categoryList\"\n                :key=\"category.id\"\n                :label=\"category.id\"\n                class=\"category-checkbox\"\n              >\n                {{ category.name }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitCategoryConfig\" :loading=\"categorySubmitting\">保存配置</el-button>\n        <el-button @click=\"cancelCategoryConfig\">取消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPrinterList, updatePrinterStatus, getPrinterInfo, savePrinter, saveSetting, getSettingInfo, printExpiryLabel, getCategoryList, savePrinterCategories, getPrinterCategories } from \"@/api/printer\";\nexport default {\n  name: \"PrinterList\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 标题\n      title: \"\",\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 是否显示弹出层\n      open: false,\n      // 默认排序\n      defaultSort: {prop: 'sort', order: 'ascending'},\n      // 表单参数\n      form: { id: '', name: '', sn: '', beforePay: 'N', afterPay: 'Y', autoPrint: 'Y', storeId: 0, type: 'RECEIPT',  status: \"A\", sort: 0 },\n      // 店铺列表\n      storeList: [],\n      // 查询参数\n      queryParams: {\n        page: 1,\n        pageSize: 10,\n        name: '',\n        sn: '',\n        storeId: '',\n        status: ''\n      },\n      // 表单校验\n      rules: {\n        name: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '名称长度必须介于2 和 50 之间', trigger: 'blur' }\n        ],\n        sn: [\n          { required: true, message: \"编号不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '编号长度必须介于2 和 64 之间', trigger: 'blur' }\n        ],\n      },\n      // 打印设置\n      openSetting: false,\n      settingForm: { userName: '', userKey: '', enable: 'Y' },\n      // 表单校验\n      settingRules: {\n        userName: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '名称长度必须介于2 和 50 之间', trigger: 'blur' }\n        ],\n        userKey: [\n          { required: true, message: \"编号不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '编号长度必须介于2 和 64 之间', trigger: 'blur' }\n        ],\n      },\n      // 打印标签相关\n      printLabelOpen: false,\n      currentPrinterId: null,\n      labelForm: {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      },\n      labelRules: {\n        name: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" }\n        ],\n        productDate: [\n          { required: true, message: \"生产日期不能为空\", trigger: \"blur\" }\n        ],\n        expireDays: [\n          { required: true, message: \"有效期天数不能为空\", trigger: \"blur\" },\n          { type: 'number', min: 1, max: 999, message: '有效期天数必须在1-999之间', trigger: 'blur' }\n        ]\n      },\n      // 分类配置相关\n      categoryConfigOpen: false,\n      categoryLoading: false,\n      categorySubmitting: false,\n      categoryList: [],\n      selectedCategories: [],\n      currentPrinter: {},\n      printerCategoriesMap: {} // 存储每个打印机的分类关联\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 查询列表\n    getList() {\n      this.loading = true;\n      getPrinterList(this.queryParams).then( response => {\n          this.list = response.data.paginationResponse.content;\n          this.total = response.data.paginationResponse.totalElements;\n          this.storeList = response.data.storeList;\n          this.loading = false;\n          // 加载打印机分类关联数据\n          this.loadPrinterCategories();\n        }\n      );\n    },\n    // 搜索按钮操作\n    handleQuery() {\n      this.queryParams.page = 1;\n      this.getList();\n    },\n    // 打印设置\n    handleSetting() {\n      getSettingInfo().then(response => {\n        this.settingForm.userName = response.data.userName;\n        this.settingForm.userKey = response.data.userKey;\n        this.settingForm.enable = response.data.enable;\n        this.openSetting = true;\n      });\n    },\n    // 重置按钮操作\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\n      this.handleQuery();\n    },\n    // 状态修改\n    handleStatusChange(row) {\n      let text = row.status == \"A\" ? \"启用\" : \"禁用\";\n      this.$modal.confirm('确认要' + text + '\"' + row.title + '\"吗？').then(function() {\n        return updatePrinterStatus(row.id, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"N\" ? \"A\" : \"N\";\n      });\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.multiple = !selection.length\n    },\n    // 排序触发事件\n    handleSortChange(column, prop, order) {\n      this.queryParams.orderByColumn = column.prop;\n      this.queryParams.isAsc = column.order;\n      this.getList();\n    },\n    // 新增按钮操作\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"新增打印设备\";\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: \"\",\n        name: \"\",\n        sn: \"\",\n        storeId: \"\",\n        autoPrint: \"Y\",\n        type: 'RECEIPT',\n        beforePay: 'Y',\n        afterPay: 'Y',\n        description: \"\",\n        status: \"A\",\n      };\n      this.resetForm(\"form\");\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 提交按钮\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id) {\n              savePrinter(this.form).then(response => {\n                this.$modal.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n              });\n          } else {\n              savePrinter(this.form).then(response => {\n                this.$modal.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n              });\n          }\n        }\n      });\n    },\n    // 修改按钮操作\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getPrinterInfo(id).then(response => {\n        this.form = response.data.printerInfo;\n        this.open = true;\n        this.title = \"编辑打印机\";\n      });\n    },\n    // 删除按钮操作\n    handleDelete(row) {\n      const name = row.name\n      this.$modal.confirm('是否确认删除\"' + name + '\"打印机？').then(function() {\n        return updatePrinterStatus(row.id, 'D');\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      });\n    },\n    // 提交设置按钮\n    submitSettingForm: function() {\n      this.$refs[\"settingForm\"].validate(valid => {\n        if (valid) {\n            saveSetting(this.settingForm).then(response => {\n              this.$modal.msgSuccess(\"保存设置成功\");\n              this.openSetting = false;\n            });\n        }\n      });\n    },\n    // 取消设置按钮\n    cancelSetting() {\n      this.openSetting = false;\n    },\n    // 打印标签按钮操作\n    // 计算有效期日期\n    calculateExpireDate(productDate, days) {\n      const date = new Date(productDate);\n      date.setDate(date.getDate() + days);\n      return date.getFullYear() + '-' +\n             String(date.getMonth() + 1).padStart(2, '0') + '-' +\n             String(date.getDate()).padStart(2, '0');\n    },\n    // 打印标签按钮操作\n    handlePrintLabel(row) {\n      this.currentPrinterId = row.id;\n      this.labelForm = {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      };\n      this.printLabelOpen = true;\n    },\n    // 取消打印标签\n    cancelPrintLabel() {\n      this.printLabelOpen = false;\n      this.currentPrinterId = null;\n      this.labelForm = {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      };\n    },\n    // 提交打印标签\n    submitLabelForm() {\n      this.$refs[\"labelForm\"].validate(valid => {\n        if (valid) {\n          const expireDate = this.calculateExpireDate(this.labelForm.productDate, this.labelForm.expireDays);\n          const data = {\n            id: this.currentPrinterId,\n            name: this.labelForm.name,\n            productDate: this.labelForm.productDate,\n            expireDate: expireDate,\n            printerId: this.currentPrinterId\n          };\n          printExpiryLabel(data).then(response => {\n            this.$modal.msgSuccess(\"打印成功\");\n            this.printLabelOpen = false;\n            this.cancelPrintLabel();\n          });\n        }\n      });\n    },\n    // 加载商品分类列表\n        loadCategoryList(storeId) {\n          const params = {};\n          if (storeId !== undefined && storeId !== null) {\n            params.storeId = storeId;\n          }\n          getCategoryList(params).then(response => {\n            this.categoryList = response.data || [];\n          }).catch(error => {\n            console.error('加载分类列表失败:', error);\n            this.categoryList = [];\n          });\n        },\n    // 加载打印机分类关联数据\n    loadPrinterCategories() {\n      this.printerCategoriesMap = {};\n      const labelPrinters = this.list.filter(printer => printer.type === 'LABEL');\n\n      labelPrinters.forEach(printer => {\n        getPrinterCategories(printer.id).then(response => {\n          this.$set(this.printerCategoriesMap, printer.id, response.data || []);\n        }).catch(error => {\n          console.error(`加载打印机${printer.id}分类关联失败:`, error);\n        });\n      });\n    },\n    // 获取打印机关联的分类名称\n    getPrinterCategoryNames(printerId) {\n      const printerCategories = this.printerCategoriesMap[printerId] || [];\n      return printerCategories.map(pc => {\n        const category = this.categoryList.find(c => c.id === pc.cateId);\n        return category ? category.name : `分类${pc.cateId}`;\n      });\n    },\n    // 打开分类配置对话框\n        handleCategoryConfig(row) {\n          this.currentPrinter = row;\n          this.categoryConfigOpen = true;\n\n          // 加载当前打印机的分类关联\n          this.categoryLoading = true;\n          getPrinterCategories(row.id).then(response => {\n            const printerCategories = response.data || [];\n            this.selectedCategories = printerCategories.map(pc => pc.cateId);\n            this.categoryLoading = false;\n          }).catch(error => {\n            console.error('加载打印机分类关联失败:', error);\n            this.categoryLoading = false;\n          });\n\n          // 重新加载该店铺的商品分类\n          this.loadCategoryList(row.storeId);\n        },\n    // 提交分类配置\n    submitCategoryConfig() {\n      this.categorySubmitting = true;\n\n      const data = {\n        printerId: this.currentPrinter.id,\n        cateIds: this.selectedCategories\n      };\n\n      savePrinterCategories(data).then(response => {\n        this.$modal.msgSuccess(\"配置保存成功\");\n        this.categoryConfigOpen = false;\n        this.categorySubmitting = false;\n        // 重新加载分类关联数据\n        this.loadPrinterCategories();\n      }).catch(error => {\n        console.error('保存分类配置失败:', error);\n        this.categorySubmitting = false;\n      });\n    },\n    // 取消分类配置\n    cancelCategoryConfig() {\n      this.categoryConfigOpen = false;\n      this.selectedCategories = [];\n      this.currentPrinter = {};\n    },\n  }\n};\n</script>\n\n<style scoped>\n.category-checkbox-group {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 10px;\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #fafafa;\n}\n\n.category-checkbox {\n  margin-right: 0 !important;\n  margin-bottom: 8px;\n  padding: 8px;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  background: white;\n  transition: all 0.3s;\n}\n\n.category-checkbox:hover {\n  background: #f0f8ff;\n  border-color: #409eff;\n}\n\n.category-checkbox.is-checked {\n  background: #ecf5ff;\n  border-color: #409eff;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqWA,SAAAA,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,oBAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QAAAC,IAAA;QAAAC,KAAA;MAAA;MACA;MACAC,IAAA;QAAAC,EAAA;QAAAd,IAAA;QAAAe,EAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,OAAA;QAAAC,IAAA;QAAAC,MAAA;QAAAC,IAAA;MAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,IAAA;QACAC,QAAA;QACA1B,IAAA;QACAe,EAAA;QACAI,OAAA;QACAE,MAAA;MACA;MACA;MACAM,KAAA;QACA3B,IAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,EAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAG,WAAA;MACAC,WAAA;QAAAC,QAAA;QAAAC,OAAA;QAAAC,MAAA;MAAA;MACA;MACAC,YAAA;QACAH,QAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,OAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAS,cAAA;MACAC,gBAAA;MACAC,SAAA;QACAzC,IAAA;QACA0C,WAAA;QACAC,UAAA;MACA;MACAC,UAAA;QACA5C,IAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAY,WAAA,GACA;UAAAd,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAa,UAAA,GACA;UAAAf,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAV,IAAA;UAAAW,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAe,kBAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,YAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,oBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAArD,OAAA;MACAZ,cAAA,MAAAkC,WAAA,EAAAgC,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAA/C,IAAA,GAAAiD,QAAA,CAAAxD,IAAA,CAAAyD,kBAAA,CAAAC,OAAA;QACAJ,KAAA,CAAAhD,KAAA,GAAAkD,QAAA,CAAAxD,IAAA,CAAAyD,kBAAA,CAAAE,aAAA;QACAL,KAAA,CAAAhC,SAAA,GAAAkC,QAAA,CAAAxD,IAAA,CAAAsB,SAAA;QACAgC,KAAA,CAAArD,OAAA;QACA;QACAqD,KAAA,CAAAM,qBAAA;MACA,CACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAtC,WAAA,CAAAC,IAAA;MACA,KAAA4B,OAAA;IACA;IACA;IACAU,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACArE,cAAA,GAAA6D,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAA9B,WAAA,CAAAC,QAAA,GAAAsB,QAAA,CAAAxD,IAAA,CAAAkC,QAAA;QACA6B,MAAA,CAAA9B,WAAA,CAAAE,OAAA,GAAAqB,QAAA,CAAAxD,IAAA,CAAAmC,OAAA;QACA4B,MAAA,CAAA9B,WAAA,CAAAG,MAAA,GAAAoB,QAAA,CAAAxD,IAAA,CAAAoC,MAAA;QACA2B,MAAA,CAAA/B,WAAA;MACA;IACA;IACA;IACAgC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAA9C,IAAA,MAAAZ,WAAA,CAAAC,IAAA,OAAAD,WAAA,CAAAE,KAAA;MACA,KAAAkD,WAAA;IACA;IACA;IACAO,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAjD,MAAA;MACA,KAAAoD,MAAA,CAAAC,OAAA,SAAAF,IAAA,SAAAF,GAAA,CAAAnE,KAAA,UAAAqD,IAAA;QACA,OAAAjE,mBAAA,CAAA+E,GAAA,CAAAxD,EAAA,EAAAwD,GAAA,CAAAjD,MAAA;MACA,GAAAmC,IAAA;QACAe,MAAA,CAAAE,MAAA,CAAAE,UAAA,CAAAH,IAAA;MACA,GAAAI,KAAA;QACAN,GAAA,CAAAjD,MAAA,GAAAiD,GAAA,CAAAjD,MAAA;MACA;IACA;IACA;IACAwD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1E,GAAA,GAAA0E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAlE,EAAA;MAAA;MACA,KAAAT,QAAA,IAAAyE,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,MAAA,EAAAxE,IAAA,EAAAC,KAAA;MACA,KAAAY,WAAA,CAAA4D,aAAA,GAAAD,MAAA,CAAAxE,IAAA;MACA,KAAAa,WAAA,CAAA6D,KAAA,GAAAF,MAAA,CAAAvE,KAAA;MACA,KAAAyC,OAAA;IACA;IACA;IACAiC,SAAA,WAAAA,UAAA;MACA,KAAAC,KAAA;MACA,KAAA9E,IAAA;MACA,KAAAN,KAAA;IACA;IACA;IACAoF,KAAA,WAAAA,MAAA;MACA,KAAA1E,IAAA;QACAC,EAAA;QACAd,IAAA;QACAe,EAAA;QACAI,OAAA;QACAD,SAAA;QACAE,IAAA;QACAJ,SAAA;QACAC,QAAA;QACAuE,WAAA;QACAnE,MAAA;MACA;MACA,KAAA6C,SAAA;IACA;IACA;IACAuB,MAAA,WAAAA,OAAA;MACA,KAAAhF,IAAA;MACA,KAAA8E,KAAA;IACA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAxB,KAAA,SAAAyB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAA9E,IAAA,CAAAC,EAAA;YACArB,WAAA,CAAAkG,MAAA,CAAA9E,IAAA,EAAA2C,IAAA,WAAAC,QAAA;cACAkC,MAAA,CAAAlB,MAAA,CAAAE,UAAA;cACAgB,MAAA,CAAAlF,IAAA;cACAkF,MAAA,CAAAtC,OAAA;YACA;UACA;YACA5D,WAAA,CAAAkG,MAAA,CAAA9E,IAAA,EAAA2C,IAAA,WAAAC,QAAA;cACAkC,MAAA,CAAAlB,MAAA,CAAAE,UAAA;cACAgB,MAAA,CAAAlF,IAAA;cACAkF,MAAA,CAAAtC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAyC,YAAA,WAAAA,aAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,KAAAR,KAAA;MACA,IAAAzE,EAAA,GAAAwD,GAAA,CAAAxD,EAAA,SAAAV,GAAA;MACAZ,cAAA,CAAAsB,EAAA,EAAA0C,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAAlF,IAAA,GAAA4C,QAAA,CAAAxD,IAAA,CAAA+F,WAAA;QACAD,MAAA,CAAAtF,IAAA;QACAsF,MAAA,CAAA5F,KAAA;MACA;IACA;IACA;IACA8F,YAAA,WAAAA,aAAA3B,GAAA;MAAA,IAAA4B,MAAA;MACA,IAAAlG,IAAA,GAAAsE,GAAA,CAAAtE,IAAA;MACA,KAAAyE,MAAA,CAAAC,OAAA,aAAA1E,IAAA,YAAAwD,IAAA;QACA,OAAAjE,mBAAA,CAAA+E,GAAA,CAAAxD,EAAA;MACA,GAAA0C,IAAA;QACA0C,MAAA,CAAA7C,OAAA;QACA6C,MAAA,CAAAzB,MAAA,CAAAE,UAAA;MACA;IACA;IACA;IACAwB,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAjC,KAAA,gBAAAyB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAnG,WAAA,CAAA0G,MAAA,CAAAlE,WAAA,EAAAsB,IAAA,WAAAC,QAAA;YACA2C,MAAA,CAAA3B,MAAA,CAAAE,UAAA;YACAyB,MAAA,CAAAnE,WAAA;UACA;QACA;MACA;IACA;IACA;IACAoE,aAAA,WAAAA,cAAA;MACA,KAAApE,WAAA;IACA;IACA;IACA;IACAqE,mBAAA,WAAAA,oBAAA5D,WAAA,EAAA6D,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAA/D,WAAA;MACA8D,IAAA,CAAAE,OAAA,CAAAF,IAAA,CAAAG,OAAA,KAAAJ,IAAA;MACA,OAAAC,IAAA,CAAAI,WAAA,WACAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA,iBACAF,MAAA,CAAAL,IAAA,CAAAG,OAAA,IAAAI,QAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAA1C,GAAA;MACA,KAAA9B,gBAAA,GAAA8B,GAAA,CAAAxD,EAAA;MACA,KAAA2B,SAAA;QACAzC,IAAA;QACA0C,WAAA;QACAC,UAAA;MACA;MACA,KAAAJ,cAAA;IACA;IACA;IACA0E,gBAAA,WAAAA,iBAAA;MACA,KAAA1E,cAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,SAAA;QACAzC,IAAA;QACA0C,WAAA;QACAC,UAAA;MACA;IACA;IACA;IACAuE,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAhD,KAAA,cAAAyB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAuB,UAAA,GAAAD,MAAA,CAAAb,mBAAA,CAAAa,MAAA,CAAA1E,SAAA,CAAAC,WAAA,EAAAyE,MAAA,CAAA1E,SAAA,CAAAE,UAAA;UACA,IAAA1C,IAAA;YACAa,EAAA,EAAAqG,MAAA,CAAA3E,gBAAA;YACAxC,IAAA,EAAAmH,MAAA,CAAA1E,SAAA,CAAAzC,IAAA;YACA0C,WAAA,EAAAyE,MAAA,CAAA1E,SAAA,CAAAC,WAAA;YACA0E,UAAA,EAAAA,UAAA;YACAC,SAAA,EAAAF,MAAA,CAAA3E;UACA;UACA5C,gBAAA,CAAAK,IAAA,EAAAuD,IAAA,WAAAC,QAAA;YACA0D,MAAA,CAAA1C,MAAA,CAAAE,UAAA;YACAwC,MAAA,CAAA5E,cAAA;YACA4E,MAAA,CAAAF,gBAAA;UACA;QACA;MACA;IACA;IACA;IACAK,gBAAA,WAAAA,iBAAAnG,OAAA;MAAA,IAAAoG,MAAA;MACA,IAAAC,MAAA;MACA,IAAArG,OAAA,KAAAsG,SAAA,IAAAtG,OAAA;QACAqG,MAAA,CAAArG,OAAA,GAAAA,OAAA;MACA;MACAtB,eAAA,CAAA2H,MAAA,EAAAhE,IAAA,WAAAC,QAAA;QACA8D,MAAA,CAAAvE,YAAA,GAAAS,QAAA,CAAAxD,IAAA;MACA,GAAA2E,KAAA,WAAA8C,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAH,MAAA,CAAAvE,YAAA;MACA;IACA;IACA;IACAa,qBAAA,WAAAA,sBAAA;MAAA,IAAA+D,OAAA;MACA,KAAAzE,oBAAA;MACA,IAAA0E,aAAA,QAAArH,IAAA,CAAAsH,MAAA,WAAAC,OAAA;QAAA,OAAAA,OAAA,CAAA3G,IAAA;MAAA;MAEAyG,aAAA,CAAAG,OAAA,WAAAD,OAAA;QACAhI,oBAAA,CAAAgI,OAAA,CAAAjH,EAAA,EAAA0C,IAAA,WAAAC,QAAA;UACAmE,OAAA,CAAAK,IAAA,CAAAL,OAAA,CAAAzE,oBAAA,EAAA4E,OAAA,CAAAjH,EAAA,EAAA2C,QAAA,CAAAxD,IAAA;QACA,GAAA2E,KAAA,WAAA8C,KAAA;UACAC,OAAA,CAAAD,KAAA,kCAAAQ,MAAA,CAAAH,OAAA,CAAAjH,EAAA,4CAAA4G,KAAA;QACA;MACA;IACA;IACA;IACAS,uBAAA,WAAAA,wBAAAd,SAAA;MAAA,IAAAe,OAAA;MACA,IAAAC,iBAAA,QAAAlF,oBAAA,CAAAkE,SAAA;MACA,OAAAgB,iBAAA,CAAAtD,GAAA,WAAAuD,EAAA;QACA,IAAAC,QAAA,GAAAH,OAAA,CAAApF,YAAA,CAAAwF,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA3H,EAAA,KAAAwH,EAAA,CAAAI,MAAA;QAAA;QACA,OAAAH,QAAA,GAAAA,QAAA,CAAAvI,IAAA,kBAAAkI,MAAA,CAAAI,EAAA,CAAAI,MAAA;MACA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAArE,GAAA;MAAA,IAAAsE,OAAA;MACA,KAAA1F,cAAA,GAAAoB,GAAA;MACA,KAAAzB,kBAAA;;MAEA;MACA,KAAAC,eAAA;MACA/C,oBAAA,CAAAuE,GAAA,CAAAxD,EAAA,EAAA0C,IAAA,WAAAC,QAAA;QACA,IAAA4E,iBAAA,GAAA5E,QAAA,CAAAxD,IAAA;QACA2I,OAAA,CAAA3F,kBAAA,GAAAoF,iBAAA,CAAAtD,GAAA,WAAAuD,EAAA;UAAA,OAAAA,EAAA,CAAAI,MAAA;QAAA;QACAE,OAAA,CAAA9F,eAAA;MACA,GAAA8B,KAAA,WAAA8C,KAAA;QACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;QACAkB,OAAA,CAAA9F,eAAA;MACA;;MAEA;MACA,KAAAwE,gBAAA,CAAAhD,GAAA,CAAAnD,OAAA;IACA;IACA;IACA0H,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAA/F,kBAAA;MAEA,IAAA9C,IAAA;QACAoH,SAAA,OAAAnE,cAAA,CAAApC,EAAA;QACAiI,OAAA,OAAA9F;MACA;MAEAnD,qBAAA,CAAAG,IAAA,EAAAuD,IAAA,WAAAC,QAAA;QACAqF,OAAA,CAAArE,MAAA,CAAAE,UAAA;QACAmE,OAAA,CAAAjG,kBAAA;QACAiG,OAAA,CAAA/F,kBAAA;QACA;QACA+F,OAAA,CAAAjF,qBAAA;MACA,GAAAe,KAAA,WAAA8C,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAoB,OAAA,CAAA/F,kBAAA;MACA;IACA;IACA;IACAiG,oBAAA,WAAAA,qBAAA;MACA,KAAAnG,kBAAA;MACA,KAAAI,kBAAA;MACA,KAAAC,cAAA;IACA;EACA;AACA", "ignoreList": []}]}