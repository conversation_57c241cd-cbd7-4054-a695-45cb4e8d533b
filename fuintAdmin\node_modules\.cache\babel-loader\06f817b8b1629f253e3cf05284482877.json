{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\index.vue", "mtime": 1754917901136}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getPrinterList", "updatePrinterStatus", "getPrinterInfo", "savePrinter", "saveSetting", "getSettingInfo", "printExpiryLabel", "getCategoryList", "savePrinterCategories", "getPrinterCategories", "name", "data", "loading", "title", "ids", "multiple", "showSearch", "total", "list", "open", "defaultSort", "prop", "order", "form", "id", "sn", "beforePay", "afterPay", "autoPrint", "storeId", "type", "status", "sort", "storeList", "queryParams", "page", "pageSize", "rules", "required", "message", "trigger", "min", "max", "openSetting", "settingForm", "userName", "<PERSON><PERSON><PERSON>", "enable", "settingRules", "printLabelOpen", "currentPrinterId", "labelForm", "productDate", "expireDays", "labelRules", "showCategoryColumn", "categoryConfigOpen", "categoryLoading", "categorySubmitting", "categoryList", "selectedCategories", "currentPrinter", "printerCategoriesMap", "created", "getList", "loadCategoryList", "methods", "_this", "then", "response", "paginationResponse", "content", "totalElements", "loadPrinterCategories", "handleQuery", "handleSetting", "_this2", "reset<PERSON><PERSON>y", "resetForm", "$refs", "tables", "handleStatusChange", "row", "_this3", "text", "$modal", "confirm", "msgSuccess", "catch", "handleSelectionChange", "selection", "map", "item", "length", "handleSortChange", "column", "orderByColumn", "isAsc", "handleAdd", "reset", "description", "cancel", "submitForm", "_this4", "validate", "valid", "handleUpdate", "_this5", "printerInfo", "handleDelete", "_this6", "submitSettingForm", "_this7", "cancelSetting", "calculateExpireDate", "days", "date", "Date", "setDate", "getDate", "getFullYear", "String", "getMonth", "padStart", "handlePrintLabel", "cancelPrintLabel", "submitLabelForm", "_this8", "expireDate", "printerId", "_this9", "error", "console", "_this10", "labelPrinters", "filter", "printer", "for<PERSON>ach", "$set", "concat", "getPrinterCategoryNames", "_this11", "printerCategories", "pc", "category", "find", "c", "cateId", "handleCategoryConfig", "_this12", "submitCategoryConfig", "_this13", "cateIds", "cancelCategoryConfig", "goToCategoryConfig", "$router", "push"], "sources": ["src/views/printer/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" class=\"main-search\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"设备名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入打印机名称\"\n          clearable\n          style=\"width: 200px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"编号\" prop=\"sn\">\n        <el-input\n          v-model=\"queryParams.sn\"\n          placeholder=\"请输入打印机编号\"\n          clearable\n          style=\"width: 200px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"所属店铺\" prop=\"store\">\n        <el-select\n          v-model=\"queryParams.storeId\"\n          placeholder=\"所属店铺\"\n          clearable\n          style=\"width: 180px\"\n        >\n          <el-option v-for=\"storeInfo in storeList\" :key=\"storeInfo.id\" :label=\"storeInfo.name\" :value=\"storeInfo.id\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 100px\"\n        >\n          <el-option key=\"A\" label=\"启用\" value=\"A\"/>\n          <el-option key=\"N\" label=\"禁用\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['printer:index']\"\n        >新增云打印设备</el-button>\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-setting\"\n          size=\"mini\"\n          @click=\"showCategoryColumn = !showCategoryColumn\"\n        >{{ showCategoryColumn ? '隐藏' : '显示' }}分类列</el-button>\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-s-tools\"\n          size=\"mini\"\n          @click=\"goToCategoryConfig\"\n          v-hasPermi=\"['printer:index']\"\n        >分类配置管理</el-button>\n        <!-- <el-button type=\"primary\" icon=\"el-icon-setting\" size=\"mini\" v-hasPermi=\"['printer:setting']\" @click=\"handleSetting\">设置芯烨账号</el-button> -->\n      </el-form-item>\n    </el-form>\n\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\n      <el-table-column label=\"ID\" prop=\"id\" width=\"55\"/>\n      <el-table-column label=\"设备名称\" align=\"center\" prop=\"name\" />\n      <el-table-column label=\"设备编号\" align=\"center\" prop=\"sn\" />\n      <el-table-column label=\"类型\" align=\"center\" prop=\"type\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.type === 'LABEL' ? 'success' : 'primary'\" size=\"small\">\n            {{ scope.row.type === 'LABEL' ? '标签' : '小票' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所属店铺\" align=\"center\" prop=\"store\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.storeId && scope.row.storeId > 0\">\n              <span>{{ getName(storeList, scope.row.storeId) }}</span>\n          </span>\n          <span v-else>公共所有</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"关联分类\" align=\"center\" prop=\"categories\" width=\"150\" v-if=\"showCategoryColumn\">\n        <template slot-scope=\"scope\">\n          <div v-if=\"scope.row.type === 'LABEL'\">\n            <el-tag\n              v-for=\"category in getPrinterCategoryNames(scope.row.id)\"\n              :key=\"category\"\n              size=\"mini\"\n              style=\"margin: 2px;\"\n            >\n              {{ category }}\n            </el-tag>\n            <span v-if=\"getPrinterCategoryNames(scope.row.id).length === 0\" style=\"color: #999;\">\n              未配置\n            </span>\n          </div>\n          <span v-else style=\"color: #999;\">-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.updateTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"A\"\n            inactive-value=\"N\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            v-hasPermi=\"['printer:index']\"\n            @click=\"handleUpdate(scope.row)\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            v-hasPermi=\"['printer:index']\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-printer\"\n            v-hasPermi=\"['printer:index']\"\n            v-if=\"scope.row.type === 'LABEL'\"\n            @click=\"handlePrintLabel(scope.row)\"\n          >打印标签</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-setting\"\n            v-hasPermi=\"['printer:index']\"\n            v-if=\"scope.row.type === 'LABEL'\"\n            @click=\"handleCategoryConfig(scope.row)\"\n          >分类配置</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.page\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改对话框 start-->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" class=\"common-dialog\" width=\"700px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备名称\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入打印机名称\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备编号\" prop=\"sn\">\n              <el-input v-model=\"form.sn\" placeholder=\"请输入打印机编号\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"所属店铺\" prop=\"storeId\">\n              <el-select\n                v-model=\"form.storeId\"\n                style=\"width: 260px\"\n                placeholder=\"所属店铺，空则为公共所有\"\n              >\n                <el-option :key=\"0\" label=\"公共所有\" v-if=\"!this.$store.getters.storeId\" :value=\"0\"/>\n                <el-option v-for=\"storeInfo in storeList\" :key=\"storeInfo.id\" :label=\"storeInfo.name\" :value=\"storeInfo.id\"/>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"打印机类型\">\n              <el-radio-group v-model=\"form.type\">\n                <el-radio key=\"RECEIPT\" label=\"RECEIPT\" value=\"RECEIPT\">小票打印机</el-radio>\n                <el-radio key=\"LABEL\" label=\"LABEL\" value=\"LABEL\">标签打印机</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"支付前打印\">\n              <el-radio-group v-model=\"form.beforePay\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">是</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"支付后打印\">\n              <el-radio-group v-model=\"form.afterPay\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">是</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"打印订单\">\n              <el-radio-group v-model=\"form.autoPrint\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">自动打印</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">手动打印</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注信息\">\n              <el-input v-model=\"form.description\" type=\"textarea\" rows=\"5\" placeholder=\"请输入备注信息\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio key=\"A\" label=\"A\" value=\"A\">启用</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">禁用</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n        <el-button @click=\"cancel\">取消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改对话框 end-->\n\n    <!-- 打印设置对话框 start-->\n    <el-dialog title=\"芯烨云打印设置\" :visible.sync=\"openSetting\" class=\"common-dialog\" width=\"700px\" append-to-body>\n      <el-form ref=\"settingForm\" :model=\"settingForm\" :rules=\"settingRules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"开发者ID\" prop=\"userName\">\n              <el-input v-model=\"settingForm.userName\" placeholder=\"请输入开发者ID\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"开发者密钥\" prop=\"userKey\">\n              <el-input v-model=\"settingForm.userKey\" placeholder=\"请输入开发者密钥\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"settingForm.enable\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">启用</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">禁用</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitSettingForm\">确定</el-button>\n        <el-button @click=\"cancelSetting\">取消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 打印设置对话框 end-->\n\n    <!-- 打印标签对话框 -->\n    <el-dialog title=\"打印标签\" :visible.sync=\"printLabelOpen\" class=\"common-dialog\" width=\"500px\" append-to-body>\n      <el-form ref=\"labelForm\" :model=\"labelForm\" :rules=\"labelRules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"名称\" prop=\"name\">\n              <el-input v-model=\"labelForm.name\" placeholder=\"请输入名称\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产日期\" prop=\"productDate\">\n              <el-date-picker\n                v-model=\"labelForm.productDate\"\n                type=\"date\"\n                style=\"width: 100%\"\n                placeholder=\"选择生产日期\"\n                value-format=\"yyyy-MM-dd\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"有效期(天)\" prop=\"expireDays\">\n              <el-input-number\n                v-model=\"labelForm.expireDays\"\n                :min=\"1\"\n                :max=\"999\"\n                style=\"width: 100%\"\n                placeholder=\"请输入有效期天数\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitLabelForm\">确定</el-button>\n        <el-button @click=\"cancelPrintLabel\">取消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 分类配置对话框 -->\n    <el-dialog title=\"打印机分类配置\" :visible.sync=\"categoryConfigOpen\" class=\"common-dialog\" width=\"600px\" append-to-body>\n      <div style=\"margin-bottom: 20px;\">\n        <el-alert\n          title=\"配置说明\"\n          description=\"为标签打印机配置对应的商品分类，打印时会优先使用配置了分类的打印机，未配置的使用通用打印机。\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon>\n        </el-alert>\n      </div>\n\n      <el-form label-width=\"120px\">\n        <el-form-item label=\"打印机\">\n          <el-input :value=\"currentPrinter.name + ' (' + currentPrinter.sn + ')'\" disabled />\n        </el-form-item>\n\n        <el-form-item label=\"关联分类\">\n          <div v-loading=\"categoryLoading\">\n            <el-checkbox-group v-model=\"selectedCategories\" class=\"category-checkbox-group\">\n              <el-checkbox\n                v-for=\"category in categoryList\"\n                :key=\"category.id\"\n                :label=\"category.id\"\n                class=\"category-checkbox\"\n              >\n                {{ category.name }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitCategoryConfig\" :loading=\"categorySubmitting\">保存配置</el-button>\n        <el-button @click=\"cancelCategoryConfig\">取消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPrinterList, updatePrinterStatus, getPrinterInfo, savePrinter, saveSetting, getSettingInfo, printExpiryLabel, getCategoryList, savePrinterCategories, getPrinterCategories } from \"@/api/printer\";\nexport default {\n  name: \"PrinterList\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 标题\n      title: \"\",\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 是否显示弹出层\n      open: false,\n      // 默认排序\n      defaultSort: {prop: 'sort', order: 'ascending'},\n      // 表单参数\n      form: { id: '', name: '', sn: '', beforePay: 'N', afterPay: 'Y', autoPrint: 'Y', storeId: 0, type: 'RECEIPT',  status: \"A\", sort: 0 },\n      // 店铺列表\n      storeList: [],\n      // 查询参数\n      queryParams: {\n        page: 1,\n        pageSize: 10,\n        name: '',\n        sn: '',\n        storeId: '',\n        status: ''\n      },\n      // 表单校验\n      rules: {\n        name: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '名称长度必须介于2 和 50 之间', trigger: 'blur' }\n        ],\n        sn: [\n          { required: true, message: \"编号不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '编号长度必须介于2 和 64 之间', trigger: 'blur' }\n        ],\n      },\n      // 打印设置\n      openSetting: false,\n      settingForm: { userName: '', userKey: '', enable: 'Y' },\n      // 表单校验\n      settingRules: {\n        userName: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '名称长度必须介于2 和 50 之间', trigger: 'blur' }\n        ],\n        userKey: [\n          { required: true, message: \"编号不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '编号长度必须介于2 和 64 之间', trigger: 'blur' }\n        ],\n      },\n      // 打印标签相关\n      printLabelOpen: false,\n      currentPrinterId: null,\n      labelForm: {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      },\n      labelRules: {\n        name: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" }\n        ],\n        productDate: [\n          { required: true, message: \"生产日期不能为空\", trigger: \"blur\" }\n        ],\n        expireDays: [\n          { required: true, message: \"有效期天数不能为空\", trigger: \"blur\" },\n          { type: 'number', min: 1, max: 999, message: '有效期天数必须在1-999之间', trigger: 'blur' }\n        ]\n      },\n      // 分类配置相关\n      showCategoryColumn: false,\n      categoryConfigOpen: false,\n      categoryLoading: false,\n      categorySubmitting: false,\n      categoryList: [],\n      selectedCategories: [],\n      currentPrinter: {},\n      printerCategoriesMap: {} // 存储每个打印机的分类关联\n    };\n  },\n  created() {\n    this.getList();\n    this.loadCategoryList();\n  },\n  methods: {\n    // 查询列表\n    getList() {\n      this.loading = true;\n      getPrinterList(this.queryParams).then( response => {\n          this.list = response.data.paginationResponse.content;\n          this.total = response.data.paginationResponse.totalElements;\n          this.storeList = response.data.storeList;\n          this.loading = false;\n          // 加载打印机分类关联数据\n          this.loadPrinterCategories();\n        }\n      );\n    },\n    // 搜索按钮操作\n    handleQuery() {\n      this.queryParams.page = 1;\n      this.getList();\n    },\n    // 打印设置\n    handleSetting() {\n      getSettingInfo().then(response => {\n        this.settingForm.userName = response.data.userName;\n        this.settingForm.userKey = response.data.userKey;\n        this.settingForm.enable = response.data.enable;\n        this.openSetting = true;\n      });\n    },\n    // 重置按钮操作\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\n      this.handleQuery();\n    },\n    // 状态修改\n    handleStatusChange(row) {\n      let text = row.status == \"A\" ? \"启用\" : \"禁用\";\n      this.$modal.confirm('确认要' + text + '\"' + row.title + '\"吗？').then(function() {\n        return updatePrinterStatus(row.id, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"N\" ? \"A\" : \"N\";\n      });\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.multiple = !selection.length\n    },\n    // 排序触发事件\n    handleSortChange(column, prop, order) {\n      this.queryParams.orderByColumn = column.prop;\n      this.queryParams.isAsc = column.order;\n      this.getList();\n    },\n    // 新增按钮操作\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"新增打印设备\";\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: \"\",\n        name: \"\",\n        sn: \"\",\n        storeId: \"\",\n        autoPrint: \"Y\",\n        type: 'RECEIPT',\n        beforePay: 'Y',\n        afterPay: 'Y',\n        description: \"\",\n        status: \"A\",\n      };\n      this.resetForm(\"form\");\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 提交按钮\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id) {\n              savePrinter(this.form).then(response => {\n                this.$modal.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n              });\n          } else {\n              savePrinter(this.form).then(response => {\n                this.$modal.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n              });\n          }\n        }\n      });\n    },\n    // 修改按钮操作\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getPrinterInfo(id).then(response => {\n        this.form = response.data.printerInfo;\n        this.open = true;\n        this.title = \"编辑打印机\";\n      });\n    },\n    // 删除按钮操作\n    handleDelete(row) {\n      const name = row.name\n      this.$modal.confirm('是否确认删除\"' + name + '\"打印机？').then(function() {\n        return updatePrinterStatus(row.id, 'D');\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      });\n    },\n    // 提交设置按钮\n    submitSettingForm: function() {\n      this.$refs[\"settingForm\"].validate(valid => {\n        if (valid) {\n            saveSetting(this.settingForm).then(response => {\n              this.$modal.msgSuccess(\"保存设置成功\");\n              this.openSetting = false;\n            });\n        }\n      });\n    },\n    // 取消设置按钮\n    cancelSetting() {\n      this.openSetting = false;\n    },\n    // 打印标签按钮操作\n    // 计算有效期日期\n    calculateExpireDate(productDate, days) {\n      const date = new Date(productDate);\n      date.setDate(date.getDate() + days);\n      return date.getFullYear() + '-' +\n             String(date.getMonth() + 1).padStart(2, '0') + '-' +\n             String(date.getDate()).padStart(2, '0');\n    },\n    // 打印标签按钮操作\n    handlePrintLabel(row) {\n      this.currentPrinterId = row.id;\n      this.labelForm = {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      };\n      this.printLabelOpen = true;\n    },\n    // 取消打印标签\n    cancelPrintLabel() {\n      this.printLabelOpen = false;\n      this.currentPrinterId = null;\n      this.labelForm = {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      };\n    },\n    // 提交打印标签\n    submitLabelForm() {\n      this.$refs[\"labelForm\"].validate(valid => {\n        if (valid) {\n          const expireDate = this.calculateExpireDate(this.labelForm.productDate, this.labelForm.expireDays);\n          const data = {\n            id: this.currentPrinterId,\n            name: this.labelForm.name,\n            productDate: this.labelForm.productDate,\n            expireDate: expireDate,\n            printerId: this.currentPrinterId\n          };\n          printExpiryLabel(data).then(response => {\n            this.$modal.msgSuccess(\"打印成功\");\n            this.printLabelOpen = false;\n            this.cancelPrintLabel();\n          });\n        }\n      });\n    },\n    // 加载商品分类列表\n    loadCategoryList() {\n      getCategoryList().then(response => {\n        this.categoryList = response.data || [];\n      }).catch(error => {\n        console.error('加载分类列表失败:', error);\n      });\n    },\n    // 加载打印机分类关联数据\n    loadPrinterCategories() {\n      this.printerCategoriesMap = {};\n      const labelPrinters = this.list.filter(printer => printer.type === 'LABEL');\n\n      labelPrinters.forEach(printer => {\n        getPrinterCategories(printer.id).then(response => {\n          this.$set(this.printerCategoriesMap, printer.id, response.data || []);\n        }).catch(error => {\n          console.error(`加载打印机${printer.id}分类关联失败:`, error);\n        });\n      });\n    },\n    // 获取打印机关联的分类名称\n    getPrinterCategoryNames(printerId) {\n      const printerCategories = this.printerCategoriesMap[printerId] || [];\n      return printerCategories.map(pc => {\n        const category = this.categoryList.find(c => c.id === pc.cateId);\n        return category ? category.name : `分类${pc.cateId}`;\n      });\n    },\n    // 打开分类配置对话框\n    handleCategoryConfig(row) {\n      this.currentPrinter = row;\n      this.categoryConfigOpen = true;\n\n      // 加载当前打印机的分类关联\n      this.categoryLoading = true;\n      getPrinterCategories(row.id).then(response => {\n        const printerCategories = response.data || [];\n        this.selectedCategories = printerCategories.map(pc => pc.cateId);\n        this.categoryLoading = false;\n      }).catch(error => {\n        console.error('加载打印机分类关联失败:', error);\n        this.categoryLoading = false;\n      });\n    },\n    // 提交分类配置\n    submitCategoryConfig() {\n      this.categorySubmitting = true;\n\n      const data = {\n        printerId: this.currentPrinter.id,\n        cateIds: this.selectedCategories\n      };\n\n      savePrinterCategories(data).then(response => {\n        this.$modal.msgSuccess(\"配置保存成功\");\n        this.categoryConfigOpen = false;\n        this.categorySubmitting = false;\n        // 重新加载分类关联数据\n        this.loadPrinterCategories();\n      }).catch(error => {\n        console.error('保存分类配置失败:', error);\n        this.categorySubmitting = false;\n      });\n    },\n    // 取消分类配置\n    cancelCategoryConfig() {\n      this.categoryConfigOpen = false;\n      this.selectedCategories = [];\n      this.currentPrinter = {};\n    },\n    // 跳转到分类配置管理页面\n    goToCategoryConfig() {\n      this.$router.push('/printer/category-config');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.category-checkbox-group {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 10px;\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #fafafa;\n}\n\n.category-checkbox {\n  margin-right: 0 !important;\n  margin-bottom: 8px;\n  padding: 8px;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  background: white;\n  transition: all 0.3s;\n}\n\n.category-checkbox:hover {\n  background: #f0f8ff;\n  border-color: #409eff;\n}\n\n.category-checkbox.is-checked {\n  background: #ecf5ff;\n  border-color: #409eff;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqYA,SAAAA,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,oBAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QAAAC,IAAA;QAAAC,KAAA;MAAA;MACA;MACAC,IAAA;QAAAC,EAAA;QAAAd,IAAA;QAAAe,EAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,OAAA;QAAAC,IAAA;QAAAC,MAAA;QAAAC,IAAA;MAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,IAAA;QACAC,QAAA;QACA1B,IAAA;QACAe,EAAA;QACAI,OAAA;QACAE,MAAA;MACA;MACA;MACAM,KAAA;QACA3B,IAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,EAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAG,WAAA;MACAC,WAAA;QAAAC,QAAA;QAAAC,OAAA;QAAAC,MAAA;MAAA;MACA;MACAC,YAAA;QACAH,QAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,OAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAS,cAAA;MACAC,gBAAA;MACAC,SAAA;QACAzC,IAAA;QACA0C,WAAA;QACAC,UAAA;MACA;MACAC,UAAA;QACA5C,IAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAY,WAAA,GACA;UAAAd,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAa,UAAA,GACA;UAAAf,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAV,IAAA;UAAAW,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAe,kBAAA;MACAC,kBAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,YAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,oBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAAvD,OAAA;MACAZ,cAAA,MAAAkC,WAAA,EAAAkC,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAjD,IAAA,GAAAmD,QAAA,CAAA1D,IAAA,CAAA2D,kBAAA,CAAAC,OAAA;QACAJ,KAAA,CAAAlD,KAAA,GAAAoD,QAAA,CAAA1D,IAAA,CAAA2D,kBAAA,CAAAE,aAAA;QACAL,KAAA,CAAAlC,SAAA,GAAAoC,QAAA,CAAA1D,IAAA,CAAAsB,SAAA;QACAkC,KAAA,CAAAvD,OAAA;QACA;QACAuD,KAAA,CAAAM,qBAAA;MACA,CACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAxC,WAAA,CAAAC,IAAA;MACA,KAAA6B,OAAA;IACA;IACA;IACAW,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACAvE,cAAA,GAAA+D,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAAhC,WAAA,CAAAC,QAAA,GAAAwB,QAAA,CAAA1D,IAAA,CAAAkC,QAAA;QACA+B,MAAA,CAAAhC,WAAA,CAAAE,OAAA,GAAAuB,QAAA,CAAA1D,IAAA,CAAAmC,OAAA;QACA8B,MAAA,CAAAhC,WAAA,CAAAG,MAAA,GAAAsB,QAAA,CAAA1D,IAAA,CAAAoC,MAAA;QACA6B,MAAA,CAAAjC,WAAA;MACA;IACA;IACA;IACAkC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAhD,IAAA,MAAAZ,WAAA,CAAAC,IAAA,OAAAD,WAAA,CAAAE,KAAA;MACA,KAAAoD,WAAA;IACA;IACA;IACAO,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAnD,MAAA;MACA,KAAAsD,MAAA,CAAAC,OAAA,SAAAF,IAAA,SAAAF,GAAA,CAAArE,KAAA,UAAAuD,IAAA;QACA,OAAAnE,mBAAA,CAAAiF,GAAA,CAAA1D,EAAA,EAAA0D,GAAA,CAAAnD,MAAA;MACA,GAAAqC,IAAA;QACAe,MAAA,CAAAE,MAAA,CAAAE,UAAA,CAAAH,IAAA;MACA,GAAAI,KAAA;QACAN,GAAA,CAAAnD,MAAA,GAAAmD,GAAA,CAAAnD,MAAA;MACA;IACA;IACA;IACA0D,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5E,GAAA,GAAA4E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAApE,EAAA;MAAA;MACA,KAAAT,QAAA,IAAA2E,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,MAAA,EAAA1E,IAAA,EAAAC,KAAA;MACA,KAAAY,WAAA,CAAA8D,aAAA,GAAAD,MAAA,CAAA1E,IAAA;MACA,KAAAa,WAAA,CAAA+D,KAAA,GAAAF,MAAA,CAAAzE,KAAA;MACA,KAAA0C,OAAA;IACA;IACA;IACAkC,SAAA,WAAAA,UAAA;MACA,KAAAC,KAAA;MACA,KAAAhF,IAAA;MACA,KAAAN,KAAA;IACA;IACA;IACAsF,KAAA,WAAAA,MAAA;MACA,KAAA5E,IAAA;QACAC,EAAA;QACAd,IAAA;QACAe,EAAA;QACAI,OAAA;QACAD,SAAA;QACAE,IAAA;QACAJ,SAAA;QACAC,QAAA;QACAyE,WAAA;QACArE,MAAA;MACA;MACA,KAAA+C,SAAA;IACA;IACA;IACAuB,MAAA,WAAAA,OAAA;MACA,KAAAlF,IAAA;MACA,KAAAgF,KAAA;IACA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAxB,KAAA,SAAAyB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAAhF,IAAA,CAAAC,EAAA;YACArB,WAAA,CAAAoG,MAAA,CAAAhF,IAAA,EAAA6C,IAAA,WAAAC,QAAA;cACAkC,MAAA,CAAAlB,MAAA,CAAAE,UAAA;cACAgB,MAAA,CAAApF,IAAA;cACAoF,MAAA,CAAAvC,OAAA;YACA;UACA;YACA7D,WAAA,CAAAoG,MAAA,CAAAhF,IAAA,EAAA6C,IAAA,WAAAC,QAAA;cACAkC,MAAA,CAAAlB,MAAA,CAAAE,UAAA;cACAgB,MAAA,CAAApF,IAAA;cACAoF,MAAA,CAAAvC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA0C,YAAA,WAAAA,aAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,KAAAR,KAAA;MACA,IAAA3E,EAAA,GAAA0D,GAAA,CAAA1D,EAAA,SAAAV,GAAA;MACAZ,cAAA,CAAAsB,EAAA,EAAA4C,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAApF,IAAA,GAAA8C,QAAA,CAAA1D,IAAA,CAAAiG,WAAA;QACAD,MAAA,CAAAxF,IAAA;QACAwF,MAAA,CAAA9F,KAAA;MACA;IACA;IACA;IACAgG,YAAA,WAAAA,aAAA3B,GAAA;MAAA,IAAA4B,MAAA;MACA,IAAApG,IAAA,GAAAwE,GAAA,CAAAxE,IAAA;MACA,KAAA2E,MAAA,CAAAC,OAAA,aAAA5E,IAAA,YAAA0D,IAAA;QACA,OAAAnE,mBAAA,CAAAiF,GAAA,CAAA1D,EAAA;MACA,GAAA4C,IAAA;QACA0C,MAAA,CAAA9C,OAAA;QACA8C,MAAA,CAAAzB,MAAA,CAAAE,UAAA;MACA;IACA;IACA;IACAwB,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAjC,KAAA,gBAAAyB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACArG,WAAA,CAAA4G,MAAA,CAAApE,WAAA,EAAAwB,IAAA,WAAAC,QAAA;YACA2C,MAAA,CAAA3B,MAAA,CAAAE,UAAA;YACAyB,MAAA,CAAArE,WAAA;UACA;QACA;MACA;IACA;IACA;IACAsE,aAAA,WAAAA,cAAA;MACA,KAAAtE,WAAA;IACA;IACA;IACA;IACAuE,mBAAA,WAAAA,oBAAA9D,WAAA,EAAA+D,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAjE,WAAA;MACAgE,IAAA,CAAAE,OAAA,CAAAF,IAAA,CAAAG,OAAA,KAAAJ,IAAA;MACA,OAAAC,IAAA,CAAAI,WAAA,WACAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA,iBACAF,MAAA,CAAAL,IAAA,CAAAG,OAAA,IAAAI,QAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAA1C,GAAA;MACA,KAAAhC,gBAAA,GAAAgC,GAAA,CAAA1D,EAAA;MACA,KAAA2B,SAAA;QACAzC,IAAA;QACA0C,WAAA;QACAC,UAAA;MACA;MACA,KAAAJ,cAAA;IACA;IACA;IACA4E,gBAAA,WAAAA,iBAAA;MACA,KAAA5E,cAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,SAAA;QACAzC,IAAA;QACA0C,WAAA;QACAC,UAAA;MACA;IACA;IACA;IACAyE,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAhD,KAAA,cAAAyB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAuB,UAAA,GAAAD,MAAA,CAAAb,mBAAA,CAAAa,MAAA,CAAA5E,SAAA,CAAAC,WAAA,EAAA2E,MAAA,CAAA5E,SAAA,CAAAE,UAAA;UACA,IAAA1C,IAAA;YACAa,EAAA,EAAAuG,MAAA,CAAA7E,gBAAA;YACAxC,IAAA,EAAAqH,MAAA,CAAA5E,SAAA,CAAAzC,IAAA;YACA0C,WAAA,EAAA2E,MAAA,CAAA5E,SAAA,CAAAC,WAAA;YACA4E,UAAA,EAAAA,UAAA;YACAC,SAAA,EAAAF,MAAA,CAAA7E;UACA;UACA5C,gBAAA,CAAAK,IAAA,EAAAyD,IAAA,WAAAC,QAAA;YACA0D,MAAA,CAAA1C,MAAA,CAAAE,UAAA;YACAwC,MAAA,CAAA9E,cAAA;YACA8E,MAAA,CAAAF,gBAAA;UACA;QACA;MACA;IACA;IACA;IACA5D,gBAAA,WAAAA,iBAAA;MAAA,IAAAiE,MAAA;MACA3H,eAAA,GAAA6D,IAAA,WAAAC,QAAA;QACA6D,MAAA,CAAAvE,YAAA,GAAAU,QAAA,CAAA1D,IAAA;MACA,GAAA6E,KAAA,WAAA2C,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IACA;IACA1D,qBAAA,WAAAA,sBAAA;MAAA,IAAA4D,OAAA;MACA,KAAAvE,oBAAA;MACA,IAAAwE,aAAA,QAAApH,IAAA,CAAAqH,MAAA,WAAAC,OAAA;QAAA,OAAAA,OAAA,CAAA1G,IAAA;MAAA;MAEAwG,aAAA,CAAAG,OAAA,WAAAD,OAAA;QACA/H,oBAAA,CAAA+H,OAAA,CAAAhH,EAAA,EAAA4C,IAAA,WAAAC,QAAA;UACAgE,OAAA,CAAAK,IAAA,CAAAL,OAAA,CAAAvE,oBAAA,EAAA0E,OAAA,CAAAhH,EAAA,EAAA6C,QAAA,CAAA1D,IAAA;QACA,GAAA6E,KAAA,WAAA2C,KAAA;UACAC,OAAA,CAAAD,KAAA,kCAAAQ,MAAA,CAAAH,OAAA,CAAAhH,EAAA,4CAAA2G,KAAA;QACA;MACA;IACA;IACA;IACAS,uBAAA,WAAAA,wBAAAX,SAAA;MAAA,IAAAY,OAAA;MACA,IAAAC,iBAAA,QAAAhF,oBAAA,CAAAmE,SAAA;MACA,OAAAa,iBAAA,CAAAnD,GAAA,WAAAoD,EAAA;QACA,IAAAC,QAAA,GAAAH,OAAA,CAAAlF,YAAA,CAAAsF,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA1H,EAAA,KAAAuH,EAAA,CAAAI,MAAA;QAAA;QACA,OAAAH,QAAA,GAAAA,QAAA,CAAAtI,IAAA,kBAAAiI,MAAA,CAAAI,EAAA,CAAAI,MAAA;MACA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAAlE,GAAA;MAAA,IAAAmE,OAAA;MACA,KAAAxF,cAAA,GAAAqB,GAAA;MACA,KAAA1B,kBAAA;;MAEA;MACA,KAAAC,eAAA;MACAhD,oBAAA,CAAAyE,GAAA,CAAA1D,EAAA,EAAA4C,IAAA,WAAAC,QAAA;QACA,IAAAyE,iBAAA,GAAAzE,QAAA,CAAA1D,IAAA;QACA0I,OAAA,CAAAzF,kBAAA,GAAAkF,iBAAA,CAAAnD,GAAA,WAAAoD,EAAA;UAAA,OAAAA,EAAA,CAAAI,MAAA;QAAA;QACAE,OAAA,CAAA5F,eAAA;MACA,GAAA+B,KAAA,WAAA2C,KAAA;QACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;QACAkB,OAAA,CAAA5F,eAAA;MACA;IACA;IACA;IACA6F,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAA7F,kBAAA;MAEA,IAAA/C,IAAA;QACAsH,SAAA,OAAApE,cAAA,CAAArC,EAAA;QACAgI,OAAA,OAAA5F;MACA;MAEApD,qBAAA,CAAAG,IAAA,EAAAyD,IAAA,WAAAC,QAAA;QACAkF,OAAA,CAAAlE,MAAA,CAAAE,UAAA;QACAgE,OAAA,CAAA/F,kBAAA;QACA+F,OAAA,CAAA7F,kBAAA;QACA;QACA6F,OAAA,CAAA9E,qBAAA;MACA,GAAAe,KAAA,WAAA2C,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAoB,OAAA,CAAA7F,kBAAA;MACA;IACA;IACA;IACA+F,oBAAA,WAAAA,qBAAA;MACA,KAAAjG,kBAAA;MACA,KAAAI,kBAAA;MACA,KAAAC,cAAA;IACA;IACA;IACA6F,kBAAA,WAAAA,mBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}