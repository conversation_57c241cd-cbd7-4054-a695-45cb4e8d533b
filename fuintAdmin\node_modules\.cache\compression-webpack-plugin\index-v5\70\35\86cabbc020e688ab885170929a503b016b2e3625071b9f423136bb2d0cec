
eb5a8ab8fde8999701cb5da2e7dc8c44af32b32a	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"index.html\",\"contentHash\":\"1fb11f3e3c412e310f08ad2406acc889\"}","integrity":"sha512-9tHlW7WnUB7t81hZJNEpnDV3cTSRI3a0d6PhUsqL5Dr5KqFvbcRjlXIUW0F/UW8+uqpuIh6aT15J84jK5sJKXA==","time":1754920241784,"size":4814}