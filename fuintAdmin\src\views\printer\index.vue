<template>
  <div class="app-container">
    <el-form :model="queryParams" class="main-search" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入打印机名称"
          clearable
          style="width: 200px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编号" prop="sn">
        <el-input
          v-model="queryParams.sn"
          placeholder="请输入打印机编号"
          clearable
          style="width: 200px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属店铺" prop="store">
        <el-select
          v-model="queryParams.storeId"
          placeholder="所属店铺"
          clearable
          style="width: 180px"
        >
          <el-option v-for="storeInfo in storeList" :key="storeInfo.id" :label="storeInfo.name" :value="storeInfo.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          clearable
          style="width: 100px"
        >
          <el-option key="A" label="启用" value="A"/>
          <el-option key="N" label="禁用" value="N"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['printer:index']"
        >新增云打印设备</el-button>

        <!-- <el-button type="primary" icon="el-icon-setting" size="mini" v-hasPermi="['printer:setting']" @click="handleSetting">设置芯烨账号</el-button> -->
      </el-form-item>
    </el-form>

    <el-table ref="tables" v-loading="loading" :data="list" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
      <el-table-column label="ID" prop="id" width="55"/>
      <el-table-column label="设备名称" align="center" prop="name" />
      <el-table-column label="设备编号" align="center" prop="sn" />
      <el-table-column label="类型" align="center" prop="type" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === 'LABEL' ? 'success' : 'primary'" size="small">
            {{ scope.row.type === 'LABEL' ? '标签' : '小票' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所属店铺" align="center" prop="store">
        <template slot-scope="scope">
          <span v-if="scope.row.storeId && scope.row.storeId > 0">
              <span>{{ getName(storeList, scope.row.storeId) }}</span>
          </span>
          <span v-else>公共所有</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="A"
            inactive-value="N"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-hasPermi="['printer:index']"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            v-hasPermi="['printer:index']"
            @click="handleDelete(scope.row)"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-printer"
            v-hasPermi="['printer:index']"
            v-if="scope.row.type === 'LABEL'"
            @click="handlePrintLabel(scope.row)"
          >打印标签</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            v-hasPermi="['printer:index']"
            v-if="scope.row.type === 'LABEL'"
            @click="handleCategoryConfig(scope.row)"
          >分类配置</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 start-->
    <el-dialog :title="title" :visible.sync="open" class="common-dialog" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入打印机名称" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备编号" prop="sn">
              <el-input v-model="form.sn" placeholder="请输入打印机编号" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属店铺" prop="storeId">
              <el-select
                v-model="form.storeId"
                style="width: 260px"
                placeholder="所属店铺，空则为公共所有"
              >
                <el-option :key="0" label="公共所有" v-if="!this.$store.getters.storeId" :value="0"/>
                <el-option v-for="storeInfo in storeList" :key="storeInfo.id" :label="storeInfo.name" :value="storeInfo.id"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="打印机类型">
              <el-radio-group v-model="form.type">
                <el-radio key="RECEIPT" label="RECEIPT" value="RECEIPT">小票打印机</el-radio>
                <el-radio key="LABEL" label="LABEL" value="LABEL">标签打印机</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="支付前打印">
              <el-radio-group v-model="form.beforePay">
                <el-radio key="Y" label="Y" value="Y">是</el-radio>
                <el-radio key="N" label="N" value="N">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="支付后打印">
              <el-radio-group v-model="form.afterPay">
                <el-radio key="Y" label="Y" value="Y">是</el-radio>
                <el-radio key="N" label="N" value="N">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="打印订单">
              <el-radio-group v-model="form.autoPrint">
                <el-radio key="Y" label="Y" value="Y">自动打印</el-radio>
                <el-radio key="N" label="N" value="N">手动打印</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息">
              <el-input v-model="form.description" type="textarea" rows="5" placeholder="请输入备注信息"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio key="A" label="A" value="A">启用</el-radio>
                <el-radio key="N" label="N" value="N">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改对话框 end-->

    <!-- 打印设置对话框 start-->
    <el-dialog title="芯烨云打印设置" :visible.sync="openSetting" class="common-dialog" width="700px" append-to-body>
      <el-form ref="settingForm" :model="settingForm" :rules="settingRules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="开发者ID" prop="userName">
              <el-input v-model="settingForm.userName" placeholder="请输入开发者ID" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="开发者密钥" prop="userKey">
              <el-input v-model="settingForm.userKey" placeholder="请输入开发者密钥" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-radio-group v-model="settingForm.enable">
                <el-radio key="Y" label="Y" value="Y">启用</el-radio>
                <el-radio key="N" label="N" value="N">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSettingForm">确定</el-button>
        <el-button @click="cancelSetting">取消</el-button>
      </div>
    </el-dialog>
    <!-- 打印设置对话框 end-->

    <!-- 打印标签对话框 -->
    <el-dialog title="打印标签" :visible.sync="printLabelOpen" class="common-dialog" width="500px" append-to-body>
      <el-form ref="labelForm" :model="labelForm" :rules="labelRules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="名称" prop="name">
              <el-input v-model="labelForm.name" placeholder="请输入名称" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="生产日期" prop="productDate">
              <el-date-picker
                v-model="labelForm.productDate"
                type="date"
                style="width: 100%"
                placeholder="选择生产日期"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="有效期(天)" prop="expireDays">
              <el-input-number
                v-model="labelForm.expireDays"
                :min="1"
                :max="999"
                style="width: 100%"
                placeholder="请输入有效期天数">
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitLabelForm">确定</el-button>
        <el-button @click="cancelPrintLabel">取消</el-button>
      </div>
    </el-dialog>

    <!-- 分类配置对话框 -->
    <el-dialog title="打印机分类配置" :visible.sync="categoryConfigOpen" class="common-dialog" width="600px" append-to-body>
      <div style="margin-bottom: 20px;">
        <el-alert
          title="配置说明"
          description="为标签打印机配置对应的商品分类，打印时会优先使用配置了分类的打印机，未配置的使用通用打印机。"
          type="info"
          :closable="false"
          show-icon>
        </el-alert>
      </div>

      <el-form label-width="120px">
        <el-form-item label="打印机">
          <el-input :value="currentPrinter.name + ' (' + currentPrinter.sn + ')'" disabled />
        </el-form-item>

        <el-form-item label="关联分类">
          <div v-loading="categoryLoading">
            <el-checkbox-group v-model="selectedCategories" class="category-checkbox-group">
              <el-checkbox
                v-for="category in categoryList"
                :key="category.id"
                :label="category.id"
                class="category-checkbox"
              >
                {{ category.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCategoryConfig" :loading="categorySubmitting">保存配置</el-button>
        <el-button @click="cancelCategoryConfig">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPrinterList, updatePrinterStatus, getPrinterInfo, savePrinter, saveSetting, getSettingInfo, printExpiryLabel, getCategoryList, savePrinterCategories, getPrinterCategories } from "@/api/printer";
export default {
  name: "PrinterList",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 标题
      title: "",
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 默认排序
      defaultSort: {prop: 'sort', order: 'ascending'},
      // 表单参数
      form: { id: '', name: '', sn: '', beforePay: 'N', afterPay: 'Y', autoPrint: 'Y', storeId: 0, type: 'RECEIPT',  status: "A", sort: 0 },
      // 店铺列表
      storeList: [],
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        name: '',
        sn: '',
        storeId: '',
        status: ''
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" },
          { min: 2, max: 200, message: '名称长度必须介于2 和 50 之间', trigger: 'blur' }
        ],
        sn: [
          { required: true, message: "编号不能为空", trigger: "blur" },
          { min: 2, max: 200, message: '编号长度必须介于2 和 64 之间', trigger: 'blur' }
        ],
      },
      // 打印设置
      openSetting: false,
      settingForm: { userName: '', userKey: '', enable: 'Y' },
      // 表单校验
      settingRules: {
        userName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
          { min: 2, max: 200, message: '名称长度必须介于2 和 50 之间', trigger: 'blur' }
        ],
        userKey: [
          { required: true, message: "编号不能为空", trigger: "blur" },
          { min: 2, max: 200, message: '编号长度必须介于2 和 64 之间', trigger: 'blur' }
        ],
      },
      // 打印标签相关
      printLabelOpen: false,
      currentPrinterId: null,
      labelForm: {
        name: '',
        productDate: '',
        expireDays: 30
      },
      labelRules: {
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        productDate: [
          { required: true, message: "生产日期不能为空", trigger: "blur" }
        ],
        expireDays: [
          { required: true, message: "有效期天数不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 999, message: '有效期天数必须在1-999之间', trigger: 'blur' }
        ]
      },
      // 分类配置相关
      categoryConfigOpen: false,
      categoryLoading: false,
      categorySubmitting: false,
      categoryList: [],
      selectedCategories: [],
      currentPrinter: {},
      printerCategoriesMap: {} // 存储每个打印机的分类关联
    };
  },
  created() {
    this.getList();
    this.loadCategoryList();
  },
  methods: {
    // 查询列表
    getList() {
      this.loading = true;
      getPrinterList(this.queryParams).then( response => {
          this.list = response.data.paginationResponse.content;
          this.total = response.data.paginationResponse.totalElements;
          this.storeList = response.data.storeList;
          this.loading = false;
          // 加载打印机分类关联数据
          this.loadPrinterCategories();
        }
      );
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    // 打印设置
    handleSetting() {
      getSettingInfo().then(response => {
        this.settingForm.userName = response.data.userName;
        this.settingForm.userKey = response.data.userKey;
        this.settingForm.enable = response.data.enable;
        this.openSetting = true;
      });
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status == "A" ? "启用" : "禁用";
      this.$modal.confirm('确认要' + text + '"' + row.title + '"吗？').then(function() {
        return updatePrinterStatus(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "N" ? "A" : "N";
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 排序触发事件
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    // 新增按钮操作
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增打印设备";
    },
    // 表单重置
    reset() {
      this.form = {
        id: "",
        name: "",
        sn: "",
        storeId: "",
        autoPrint: "Y",
        type: 'RECEIPT',
        beforePay: 'Y',
        afterPay: 'Y',
        description: "",
        status: "A",
      };
      this.resetForm("form");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 提交按钮
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id) {
              savePrinter(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
          } else {
              savePrinter(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
          }
        }
      });
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPrinterInfo(id).then(response => {
        this.form = response.data.printerInfo;
        this.open = true;
        this.title = "编辑打印机";
      });
    },
    // 删除按钮操作
    handleDelete(row) {
      const name = row.name
      this.$modal.confirm('是否确认删除"' + name + '"打印机？').then(function() {
        return updatePrinterStatus(row.id, 'D');
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    // 提交设置按钮
    submitSettingForm: function() {
      this.$refs["settingForm"].validate(valid => {
        if (valid) {
            saveSetting(this.settingForm).then(response => {
              this.$modal.msgSuccess("保存设置成功");
              this.openSetting = false;
            });
        }
      });
    },
    // 取消设置按钮
    cancelSetting() {
      this.openSetting = false;
    },
    // 打印标签按钮操作
    // 计算有效期日期
    calculateExpireDate(productDate, days) {
      const date = new Date(productDate);
      date.setDate(date.getDate() + days);
      return date.getFullYear() + '-' +
             String(date.getMonth() + 1).padStart(2, '0') + '-' +
             String(date.getDate()).padStart(2, '0');
    },
    // 打印标签按钮操作
    handlePrintLabel(row) {
      this.currentPrinterId = row.id;
      this.labelForm = {
        name: '',
        productDate: '',
        expireDays: 30
      };
      this.printLabelOpen = true;
    },
    // 取消打印标签
    cancelPrintLabel() {
      this.printLabelOpen = false;
      this.currentPrinterId = null;
      this.labelForm = {
        name: '',
        productDate: '',
        expireDays: 30
      };
    },
    // 提交打印标签
    submitLabelForm() {
      this.$refs["labelForm"].validate(valid => {
        if (valid) {
          const expireDate = this.calculateExpireDate(this.labelForm.productDate, this.labelForm.expireDays);
          const data = {
            id: this.currentPrinterId,
            name: this.labelForm.name,
            productDate: this.labelForm.productDate,
            expireDate: expireDate,
            printerId: this.currentPrinterId
          };
          printExpiryLabel(data).then(response => {
            this.$modal.msgSuccess("打印成功");
            this.printLabelOpen = false;
            this.cancelPrintLabel();
          });
        }
      });
    },
    // 加载商品分类列表
        loadCategoryList(storeId) {
          getCategoryList({ storeId }).then(response => {
            this.categoryList = response.data || [];
          }).catch(error => {
            console.error('加载分类列表失败:', error);
          });
        },
    // 加载打印机分类关联数据
    loadPrinterCategories() {
      this.printerCategoriesMap = {};
      const labelPrinters = this.list.filter(printer => printer.type === 'LABEL');

      labelPrinters.forEach(printer => {
        getPrinterCategories(printer.id).then(response => {
          this.$set(this.printerCategoriesMap, printer.id, response.data || []);
        }).catch(error => {
          console.error(`加载打印机${printer.id}分类关联失败:`, error);
        });
      });
    },
    // 获取打印机关联的分类名称
    getPrinterCategoryNames(printerId) {
      const printerCategories = this.printerCategoriesMap[printerId] || [];
      return printerCategories.map(pc => {
        const category = this.categoryList.find(c => c.id === pc.cateId);
        return category ? category.name : `分类${pc.cateId}`;
      });
    },
    // 打开分类配置对话框
        handleCategoryConfig(row) {
          this.currentPrinter = row;
          this.categoryConfigOpen = true;

          // 加载当前打印机的分类关联
          this.categoryLoading = true;
          getPrinterCategories(row.id).then(response => {
            const printerCategories = response.data || [];
            this.selectedCategories = printerCategories.map(pc => pc.cateId);
            this.categoryLoading = false;
          }).catch(error => {
            console.error('加载打印机分类关联失败:', error);
            this.categoryLoading = false;
          });

          // 重新加载该店铺的商品分类
          this.loadCategoryList(row.storeId);
        },
    // 提交分类配置
    submitCategoryConfig() {
      this.categorySubmitting = true;

      const data = {
        printerId: this.currentPrinter.id,
        cateIds: this.selectedCategories
      };

      savePrinterCategories(data).then(response => {
        this.$modal.msgSuccess("配置保存成功");
        this.categoryConfigOpen = false;
        this.categorySubmitting = false;
        // 重新加载分类关联数据
        this.loadPrinterCategories();
      }).catch(error => {
        console.error('保存分类配置失败:', error);
        this.categorySubmitting = false;
      });
    },
    // 取消分类配置
    cancelCategoryConfig() {
      this.categoryConfigOpen = false;
      this.selectedCategories = [];
      this.currentPrinter = {};
    },
  }
};
</script>

<style scoped>
.category-checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.category-checkbox {
  margin-right: 0 !important;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: white;
  transition: all 0.3s;
}

.category-checkbox:hover {
  background: #f0f8ff;
  border-color: #409eff;
}

.category-checkbox.is-checked {
  background: #ecf5ff;
  border-color: #409eff;
}
</style>
