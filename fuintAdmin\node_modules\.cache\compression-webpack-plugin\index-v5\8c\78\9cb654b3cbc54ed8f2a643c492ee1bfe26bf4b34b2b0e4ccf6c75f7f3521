
c68337c6e1566f097a58a26b10145132ffcb0155	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"96696dc0e41a579c3168d0a7e3119c16\"}","integrity":"sha512-HlDZSU5vXm4twRzS9X3+yqqjXyIe/dSwFidmpSDkFSmMLL5rLnJImsA08VIPADv0Saa0ZyuaGogS3LiKETfB6Q==","time":1754920241900,"size":2883433}