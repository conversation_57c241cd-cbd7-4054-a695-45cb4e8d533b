
0c6771ba4d24efca46826f4a07e36b5cdb59e6b8	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"e018b08241f1e461133e65bbcc7d4d24\"}","integrity":"sha512-okGI2QYuz9iXcQ2Tmecd8kX8dMffffd2NDZW7iPQHMx/YaD1dDftxvHlSEK/JdaISZ+H0ekqz/32ULrIWivLvg==","time":1754921484132,"size":2882382}