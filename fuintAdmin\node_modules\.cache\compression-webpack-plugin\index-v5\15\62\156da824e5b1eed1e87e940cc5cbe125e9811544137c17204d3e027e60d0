
a203483c33dd99747a47f8ea06d8dd17e2204772	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.b46032b1b3b1d7eedc4c.hot-update.js\",\"contentHash\":\"7aceacd96cadd8dc54a8df9dd6439b14\"}","integrity":"sha512-7nc/reDllk+fKEN9rYVoUTjuWpULYkZ2f/XZXEKK+bMXBwtTzFPSXDRmGntaItZrnY2MAjTl02f0rhav85nuAQ==","time":1754920991776,"size":18967}