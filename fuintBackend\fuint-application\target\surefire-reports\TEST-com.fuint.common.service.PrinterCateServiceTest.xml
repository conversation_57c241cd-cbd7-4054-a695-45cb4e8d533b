<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.fuint.common.service.PrinterCateServiceTest" time="0.45" tests="7" errors="4" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\test-classes;D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\classes;C:\Users\<USER>\.m2\repository\com\huifu\bspay\sdk\dg-java-sdk\3.0.2\dg-java-sdk-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.70\fastjson-1.2.70.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.2\httpclient-4.5.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.2\httpmime-4.5.2.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.9.1\okhttp-4.9.1.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\2.8.0\okio-2.8.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.5.32\kotlin-stdlib-common-1.5.32.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.5.32\kotlin-stdlib-1.5.32.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-framework\target\fuint-framework-1.0.0.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-utils\target\fuint-utils-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.3.0\core-3.3.0.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\javase\3.3.0\javase-3.3.0.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.48\jcommander-1.48.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.3.1\jai-imageio-core-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.58\bcprov-jdk15on-1.58.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\3.14\poi-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\3.14\poi-ooxml-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\3.14\poi-ooxml-schemas-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\2.6.0\xmlbeans-2.6.0.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.03\curvesapi-1.03.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-repository\target\fuint-repository-1.0.0.jar;C:\Users\<USER>\.m2\repository\io\sentry\sentry-logback\1.2.0\sentry-logback-1.2.0.jar;C:\Users\<USER>\.m2\repository\io\sentry\sentry\1.2.0\sentry-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.12.6\jackson-core-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\ws\spring-ws-core\3.1.3\spring-ws-core-3.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\ws\spring-xml\3.1.3\spring-xml-3.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.18\spring-beans-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.18\spring-oxm-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.18\spring-web-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.18\spring-webmvc-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.18\spring-expression-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.18\spring-core-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.18\spring-jcl-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.5.12\spring-boot-starter-security-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.5.5\spring-security-config-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.5.5\spring-security-core-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.5.5\spring-security-crypto-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.5.5\spring-security-web-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\commons-httpclient\commons-httpclient\3.1\commons-httpclient-3.1.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.0.4\commons-logging-1.0.4.jar;C:\Users\<USER>\.m2\repository\nl\bitwalker\UserAgentUtils\1.2.4\UserAgentUtils-1.2.4.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\20.0\guava-20.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.1.0\mybatis-plus-boot-starter-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.5.12\spring-boot-autoconfigure-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.5.12\spring-boot-starter-jdbc-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.18\spring-jdbc-5.3.18.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.1.0\mybatis-plus-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.1.0\mybatis-plus-extension-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.1.0\mybatis-plus-core-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.1.0\mybatis-plus-annotation-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.0\mybatis-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.0\mybatis-spring-2.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.2.5\pagehelper-spring-boot-starter-1.2.5.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\1.3.2\mybatis-spring-boot-starter-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\1.3.2\mybatis-spring-boot-autoconfigure-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.2.5\pagehelper-spring-boot-autoconfigure-1.2.5.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\5.1.4\pagehelper-5.1.4.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\1.0\jsqlparser-1.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.9.0\mockito-core-3.9.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;C:\Users\<USER>\.m2\repository\com\github\axet\kaptcha\0.0.9\kaptcha-0.0.9.jar;C:\Users\<USER>\.m2\repository\com\jhlabs\filters\2.0.235\filters-2.0.235.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.5.12\spring-boot-starter-test-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.5.12\spring-boot-test-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.5.12\spring-boot-test-autoconfigure-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.5.0\json-path-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.19.0\assertj-core-3.19.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.7.2\junit-jupiter-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.7.2\junit-jupiter-api-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.7.2\junit-platform-commons-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.7.2\junit-jupiter-params-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.7.2\junit-jupiter-engine-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.7.2\junit-platform-engine-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.9.0\mockito-junit-jupiter-3.9.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.3.18\spring-test-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.24.0-GA\javassist-3.24.0-GA.jar;C:\Users\<USER>\.m2\repository\com\aliyun\oss\aliyun-sdk-oss\3.10.2\aliyun-sdk-oss-3.10.2.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jettison\jettison\1.1\jettison-1.1.jar;C:\Users\<USER>\.m2\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ram\3.0.0\aliyun-java-sdk-ram-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-sts\3.0.0\aliyun-java-sdk-sts-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ecs\4.2.0\aliyun-java-sdk-ecs-4.2.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-kms\2.7.0\aliyun-java-sdk-kms-2.7.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-core\4.4.6\aliyun-java-sdk-core-4.4.6.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\jacoco\org.jacoco.agent\0.8.3\org.jacoco.agent-0.8.3-runtime.jar;C:\Users\<USER>\.m2\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Users\<USER>\.m2\repository\com\alibaba\transmittable-thread-local\2.2.0\transmittable-thread-local-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-WxPay\2.9.6\IJPay-WxPay-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-Core\2.9.6\IJPay-Core-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\github\xkzhangsan\xk-time\3.2.4\xk-time-3.2.4.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-crypto\5.8.11\hutool-crypto-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-core\5.8.11\hutool-core-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-http\5.8.11\hutool-http-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-json\5.8.11\hutool-json-5.8.11.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-AliPay\2.9.6\IJPay-AliPay-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\alipay\sdk\alipay-sdk-java\4.35.37.ALL\alipay-sdk-java-4.35.37.ALL.jar;C:\Users\<USER>\.m2\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.0.b2\xml-apis-1.0.b2.jar;C:\Users\<USER>\.m2\repository\org\apache\velocity\velocity-engine-core\2.3\velocity-engine-core-2.3.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.13.0\commons-io-2.13.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.5.12\spring-boot-starter-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.5.12\spring-boot-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.5.12\spring-boot-starter-logging-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.28\snakeyaml-1.28.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.5.12\spring-boot-starter-web-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.5.12\spring-boot-starter-json-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.12.6.1\jackson-databind-2.12.6.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.12.6\jackson-datatype-jdk8-2.12.6.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.12.6\jackson-datatype-jsr310-2.12.6.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.12.6\jackson-module-parameter-names-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jetty\2.5.12\spring-boot-starter-jetty-2.5.12.jar;C:\Users\<USER>\.m2\repository\jakarta\servlet\jakarta.servlet-api\4.0.4\jakarta.servlet-api-4.0.4.jar;C:\Users\<USER>\.m2\repository\jakarta\websocket\jakarta.websocket-api\1.1.2\jakarta.websocket-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.60\tomcat-embed-el-9.0.60.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlets\9.4.45.v20220203\jetty-servlets-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-continuation\9.4.45.v20220203\jetty-continuation-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-http\9.4.45.v20220203\jetty-http-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util\9.4.45.v20220203\jetty-util-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-io\9.4.45.v20220203\jetty-io-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-webapp\9.4.45.v20220203\jetty-webapp-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-xml\9.4.45.v20220203\jetty-xml-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlet\9.4.45.v20220203\jetty-servlet-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-security\9.4.45.v20220203\jetty-security-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-server\9.4.45.v20220203\jetty-server-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util-ajax\9.4.45.v20220203\jetty-util-ajax-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-server\9.4.45.v20220203\websocket-server-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-common\9.4.45.v20220203\websocket-common-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-api\9.4.45.v20220203\websocket-api-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-client\9.4.45.v20220203\websocket-client-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-client\9.4.45.v20220203\jetty-client-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-servlet\9.4.45.v20220203\websocket-servlet-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\javax-websocket-server-impl\9.4.45.v20220203\javax-websocket-server-impl-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-annotations\9.4.45.v20220203\jetty-annotations-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-plus\9.4.45.v20220203\jetty-plus-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.2\asm-commons-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.2\asm-tree-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-analysis\9.2\asm-analysis-9.2.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\javax-websocket-client-impl\9.4.45.v20220203\javax-websocket-client-impl-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.5.12\spring-boot-actuator-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-generator\3.1.0\mybatis-plus-generator-3.1.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.21\swagger-annotations-1.5.21.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.21\swagger-models-1.5.21.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.12.6\jackson-annotations-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.18\spring-context-support-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.18\spring-context-5.3.18.jar;C:\Users\<USER>\.m2\repository\net\sf\ehcache\ehcache\2.10.2\ehcache-2.10.2.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\4.0.2\kryo-4.0.2.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\reflectasm\1.11.3\reflectasm-1.11.3.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\minlog\1.3.0\minlog-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.18\spring-aop-5.3.18.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.5.12\spring-boot-starter-data-redis-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.5.10\spring-data-redis-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.5.10\spring-data-keyvalue-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.5.10\spring-data-commons-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.18\spring-tx-5.3.18.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.8.RELEASE\lettuce-core-6.1.8.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.75.Final\netty-common-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.75.Final\netty-handler-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.75.Final\netty-resolver-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.75.Final\netty-buffer-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.75.Final\netty-codec-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.75.Final\netty-transport-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.16\reactor-core-3.4.16.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\session\spring-session-data-redis\2.5.5\spring-session-data-redis-2.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\session\spring-session-core\2.5.5\spring-session-core-2.5.5.jar;C:\Users\<USER>\.m2\repository\com\github\liyiorg\weixin-popular\2.8.0\weixin-popular-2.8.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.1.12\jaxb-impl-2.1.12.jar;C:\Users\<USER>\.m2\repository\com\vdurmont\emoji-java\3.1.1\emoji-java-3.1.1.jar;C:\Users\<USER>\.m2\repository\org\json\json\20140107\json-20140107.jar;C:\Users\<USER>\.m2\repository\org\tuckey\urlrewritefilter\4.0.3\urlrewritefilter-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 11"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\scoop\apps\OracleJDK8\current\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire2644371510761120621\surefirebooter1416207632114688668.jar C:\Users\<USER>\AppData\Local\Temp\surefire2644371510761120621 2025-08-11T21-46-24_756-jvmRun1 surefire1610464489176024432tmp surefire_0990885862063378314tmp"/>
    <property name="surefire.test.class.path" value="D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\test-classes;D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\classes;C:\Users\<USER>\.m2\repository\com\huifu\bspay\sdk\dg-java-sdk\3.0.2\dg-java-sdk-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.70\fastjson-1.2.70.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.2\httpclient-4.5.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.2\httpmime-4.5.2.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.9.1\okhttp-4.9.1.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\2.8.0\okio-2.8.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.5.32\kotlin-stdlib-common-1.5.32.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.5.32\kotlin-stdlib-1.5.32.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-framework\target\fuint-framework-1.0.0.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-utils\target\fuint-utils-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.3.0\core-3.3.0.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\javase\3.3.0\javase-3.3.0.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.48\jcommander-1.48.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.3.1\jai-imageio-core-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.58\bcprov-jdk15on-1.58.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\3.14\poi-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\3.14\poi-ooxml-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\3.14\poi-ooxml-schemas-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\2.6.0\xmlbeans-2.6.0.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.03\curvesapi-1.03.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-repository\target\fuint-repository-1.0.0.jar;C:\Users\<USER>\.m2\repository\io\sentry\sentry-logback\1.2.0\sentry-logback-1.2.0.jar;C:\Users\<USER>\.m2\repository\io\sentry\sentry\1.2.0\sentry-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.12.6\jackson-core-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\ws\spring-ws-core\3.1.3\spring-ws-core-3.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\ws\spring-xml\3.1.3\spring-xml-3.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.18\spring-beans-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.18\spring-oxm-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.18\spring-web-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.18\spring-webmvc-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.18\spring-expression-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.18\spring-core-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.18\spring-jcl-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.5.12\spring-boot-starter-security-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.5.5\spring-security-config-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.5.5\spring-security-core-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.5.5\spring-security-crypto-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.5.5\spring-security-web-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\commons-httpclient\commons-httpclient\3.1\commons-httpclient-3.1.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.0.4\commons-logging-1.0.4.jar;C:\Users\<USER>\.m2\repository\nl\bitwalker\UserAgentUtils\1.2.4\UserAgentUtils-1.2.4.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\20.0\guava-20.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.1.0\mybatis-plus-boot-starter-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.5.12\spring-boot-autoconfigure-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.5.12\spring-boot-starter-jdbc-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.18\spring-jdbc-5.3.18.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.1.0\mybatis-plus-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.1.0\mybatis-plus-extension-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.1.0\mybatis-plus-core-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.1.0\mybatis-plus-annotation-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.0\mybatis-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.0\mybatis-spring-2.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.2.5\pagehelper-spring-boot-starter-1.2.5.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\1.3.2\mybatis-spring-boot-starter-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\1.3.2\mybatis-spring-boot-autoconfigure-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.2.5\pagehelper-spring-boot-autoconfigure-1.2.5.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\5.1.4\pagehelper-5.1.4.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\1.0\jsqlparser-1.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.9.0\mockito-core-3.9.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;C:\Users\<USER>\.m2\repository\com\github\axet\kaptcha\0.0.9\kaptcha-0.0.9.jar;C:\Users\<USER>\.m2\repository\com\jhlabs\filters\2.0.235\filters-2.0.235.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.5.12\spring-boot-starter-test-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.5.12\spring-boot-test-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.5.12\spring-boot-test-autoconfigure-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.5.0\json-path-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.19.0\assertj-core-3.19.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.7.2\junit-jupiter-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.7.2\junit-jupiter-api-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.7.2\junit-platform-commons-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.7.2\junit-jupiter-params-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.7.2\junit-jupiter-engine-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.7.2\junit-platform-engine-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.9.0\mockito-junit-jupiter-3.9.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.3.18\spring-test-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.24.0-GA\javassist-3.24.0-GA.jar;C:\Users\<USER>\.m2\repository\com\aliyun\oss\aliyun-sdk-oss\3.10.2\aliyun-sdk-oss-3.10.2.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jettison\jettison\1.1\jettison-1.1.jar;C:\Users\<USER>\.m2\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ram\3.0.0\aliyun-java-sdk-ram-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-sts\3.0.0\aliyun-java-sdk-sts-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ecs\4.2.0\aliyun-java-sdk-ecs-4.2.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-kms\2.7.0\aliyun-java-sdk-kms-2.7.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-core\4.4.6\aliyun-java-sdk-core-4.4.6.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\jacoco\org.jacoco.agent\0.8.3\org.jacoco.agent-0.8.3-runtime.jar;C:\Users\<USER>\.m2\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Users\<USER>\.m2\repository\com\alibaba\transmittable-thread-local\2.2.0\transmittable-thread-local-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-WxPay\2.9.6\IJPay-WxPay-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-Core\2.9.6\IJPay-Core-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\github\xkzhangsan\xk-time\3.2.4\xk-time-3.2.4.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-crypto\5.8.11\hutool-crypto-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-core\5.8.11\hutool-core-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-http\5.8.11\hutool-http-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-json\5.8.11\hutool-json-5.8.11.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-AliPay\2.9.6\IJPay-AliPay-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\alipay\sdk\alipay-sdk-java\4.35.37.ALL\alipay-sdk-java-4.35.37.ALL.jar;C:\Users\<USER>\.m2\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.0.b2\xml-apis-1.0.b2.jar;C:\Users\<USER>\.m2\repository\org\apache\velocity\velocity-engine-core\2.3\velocity-engine-core-2.3.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.13.0\commons-io-2.13.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.5.12\spring-boot-starter-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.5.12\spring-boot-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.5.12\spring-boot-starter-logging-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.28\snakeyaml-1.28.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.5.12\spring-boot-starter-web-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.5.12\spring-boot-starter-json-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.12.6.1\jackson-databind-2.12.6.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.12.6\jackson-datatype-jdk8-2.12.6.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.12.6\jackson-datatype-jsr310-2.12.6.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.12.6\jackson-module-parameter-names-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jetty\2.5.12\spring-boot-starter-jetty-2.5.12.jar;C:\Users\<USER>\.m2\repository\jakarta\servlet\jakarta.servlet-api\4.0.4\jakarta.servlet-api-4.0.4.jar;C:\Users\<USER>\.m2\repository\jakarta\websocket\jakarta.websocket-api\1.1.2\jakarta.websocket-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.60\tomcat-embed-el-9.0.60.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlets\9.4.45.v20220203\jetty-servlets-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-continuation\9.4.45.v20220203\jetty-continuation-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-http\9.4.45.v20220203\jetty-http-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util\9.4.45.v20220203\jetty-util-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-io\9.4.45.v20220203\jetty-io-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-webapp\9.4.45.v20220203\jetty-webapp-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-xml\9.4.45.v20220203\jetty-xml-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlet\9.4.45.v20220203\jetty-servlet-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-security\9.4.45.v20220203\jetty-security-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-server\9.4.45.v20220203\jetty-server-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util-ajax\9.4.45.v20220203\jetty-util-ajax-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-server\9.4.45.v20220203\websocket-server-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-common\9.4.45.v20220203\websocket-common-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-api\9.4.45.v20220203\websocket-api-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-client\9.4.45.v20220203\websocket-client-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-client\9.4.45.v20220203\jetty-client-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-servlet\9.4.45.v20220203\websocket-servlet-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\javax-websocket-server-impl\9.4.45.v20220203\javax-websocket-server-impl-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-annotations\9.4.45.v20220203\jetty-annotations-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-plus\9.4.45.v20220203\jetty-plus-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.2\asm-commons-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.2\asm-tree-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-analysis\9.2\asm-analysis-9.2.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\javax-websocket-client-impl\9.4.45.v20220203\javax-websocket-client-impl-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.5.12\spring-boot-actuator-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-generator\3.1.0\mybatis-plus-generator-3.1.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.21\swagger-annotations-1.5.21.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.21\swagger-models-1.5.21.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.12.6\jackson-annotations-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.18\spring-context-support-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.18\spring-context-5.3.18.jar;C:\Users\<USER>\.m2\repository\net\sf\ehcache\ehcache\2.10.2\ehcache-2.10.2.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\4.0.2\kryo-4.0.2.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\reflectasm\1.11.3\reflectasm-1.11.3.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\minlog\1.3.0\minlog-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.18\spring-aop-5.3.18.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.5.12\spring-boot-starter-data-redis-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.5.10\spring-data-redis-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.5.10\spring-data-keyvalue-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.5.10\spring-data-commons-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.18\spring-tx-5.3.18.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.8.RELEASE\lettuce-core-6.1.8.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.75.Final\netty-common-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.75.Final\netty-handler-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.75.Final\netty-resolver-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.75.Final\netty-buffer-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.75.Final\netty-codec-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.75.Final\netty-transport-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.16\reactor-core-3.4.16.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\session\spring-session-data-redis\2.5.5\spring-session-data-redis-2.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\session\spring-session-core\2.5.5\spring-session-core-2.5.5.jar;C:\Users\<USER>\.m2\repository\com\github\liyiorg\weixin-popular\2.8.0\weixin-popular-2.8.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.1.12\jaxb-impl-2.1.12.jar;C:\Users\<USER>\.m2\repository\com\vdurmont\emoji-java\3.1.1\emoji-java-3.1.1.jar;C:\Users\<USER>\.m2\repository\org\json\json\20140107\json-20140107.jar;C:\Users\<USER>\.m2\repository\org\tuckey\urlrewritefilter\4.0.3\urlrewritefilter-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\scoop\apps\OracleJDK8\current\jre"/>
    <property name="basedir" value="D:\workspace\fuintFoodSystem\fuintBackend\fuint-application"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire2644371510761120621\surefirebooter1416207632114688668.jar"/>
    <property name="sun.boot.class.path" value="D:\scoop\apps\OracleJDK8\current\jre\lib\resources.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\rt.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\jsse.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\jce.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\charsets.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\jfr.jar;D:\scoop\apps\OracleJDK8\current\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_421-b09"/>
    <property name="user.name" value="HomePC"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\scoop\apps\OracleJDK8\current\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_421"/>
    <property name="user.dir" value="D:\workspace\fuintFoodSystem\fuintBackend\fuint-application"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\scoop\apps\OracleJDK8\current\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\scoop\apps\MiKTeX\current\texmfs\install\miktex\bin\x64;D:\scoop\apps\python\current\Scripts;D:\scoop\apps\python\current;D:\scoop\apps\openssl\current\bin;D:\scoop\apps\yarn\current\global\node_modules\.bin;D:\scoop\apps\yarn\current\bin;D:\scoop\apps\OracleJDK8\current\bin;D:\scoop\apps\openjdk21\current\bin;D:\scoop\apps\nodejs-lts\current\bin;D:\scoop\apps\nodejs-lts\current;D:\scoop\apps\maven\current\bin;D:\scoop\apps\ImageMagick\current;D:\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\scoop\shims;D:\soft\VSCode\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.421-b09"/>
    <property name="java.specification.maintenance.version" value="5"/>
    <property name="java.ext.dirs" value="D:\scoop\apps\OracleJDK8\current\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testQueryPrinterCateListByParams" classname="com.fuint.common.service.PrinterCateServiceTest" time="0.315"/>
  <testcase name="testAddPrinterCateWithNullPrinterId" classname="com.fuint.common.service.PrinterCateServiceTest" time="0.002"/>
  <testcase name="testGetPrinterIdsByCateId" classname="com.fuint.common.service.PrinterCateServiceTest" time="0.001"/>
  <testcase name="testAddPrinterCate" classname="com.fuint.common.service.PrinterCateServiceTest" time="0.001">
    <error type="java.lang.NullPointerException">java.lang.NullPointerException
	at com.fuint.common.service.PrinterCateServiceTest.testAddPrinterCate(PrinterCateServiceTest.java:83)
</error>
  </testcase>
  <testcase name="testDeleteByPrinterId" classname="com.fuint.common.service.PrinterCateServiceTest" time="0.126">
    <error message="&#10;Misplaced or misused argument matcher detected here:&#10;&#10;-&gt; at com.fuint.common.service.PrinterCateServiceTest.testAddPrinterCate(PrinterCateServiceTest.java:83)&#10;&#10;You cannot use argument matchers outside of verification or stubbing.&#10;Examples of correct usage of argument matchers:&#10;    when(mock.get(anyInt())).thenReturn(null);&#10;    doThrow(new RuntimeException()).when(mock).someVoidMethod(anyObject());&#10;    verify(mock).someMethod(contains(&quot;foo&quot;))&#10;&#10;This message may appear after an NullPointerException if the last matcher is returning an object &#10;like any() but the stubbed method signature expect a primitive argument, in this case,&#10;use primitive alternatives.&#10;    when(mock.get(any())); // bad use, will raise NPE&#10;    when(mock.get(anyInt())); // correct usage use&#10;&#10;Also, this error might show up because you use argument matchers with methods that cannot be mocked.&#10;Following methods *cannot* be stubbed/verified: final/private/equals()/hashCode().&#10;Mocking methods declared on non-public parent classes is not supported.&#10;" type="org.mockito.exceptions.misusing.InvalidUseOfMatchersException">org.mockito.exceptions.misusing.InvalidUseOfMatchersException: 

Misplaced or misused argument matcher detected here:

-> at com.fuint.common.service.PrinterCateServiceTest.testAddPrinterCate(PrinterCateServiceTest.java:83)

You cannot use argument matchers outside of verification or stubbing.
Examples of correct usage of argument matchers:
    when(mock.get(anyInt())).thenReturn(null);
    doThrow(new RuntimeException()).when(mock).someVoidMethod(anyObject());
    verify(mock).someMethod(contains("foo"))

This message may appear after an NullPointerException if the last matcher is returning an object 
like any() but the stubbed method signature expect a primitive argument, in this case,
use primitive alternatives.
    when(mock.get(any())); // bad use, will raise NPE
    when(mock.get(anyInt())); // correct usage use

Also, this error might show up because you use argument matchers with methods that cannot be mocked.
Following methods *cannot* be stubbed/verified: final/private/equals()/hashCode().
Mocking methods declared on non-public parent classes is not supported.

</error>
  </testcase>
  <testcase name="testSavePrinterCateRelations" classname="com.fuint.common.service.PrinterCateServiceTest" time="0.001">
    <error type="java.lang.NullPointerException">java.lang.NullPointerException
	at com.fuint.common.service.PrinterCateServiceTest.testSavePrinterCateRelations(PrinterCateServiceTest.java:166)
</error>
  </testcase>
  <testcase name="testDeleteByCateId" classname="com.fuint.common.service.PrinterCateServiceTest" time="0.001">
    <error message="&#10;Misplaced or misused argument matcher detected here:&#10;&#10;-&gt; at com.fuint.common.service.PrinterCateServiceTest.testSavePrinterCateRelations(PrinterCateServiceTest.java:166)&#10;&#10;You cannot use argument matchers outside of verification or stubbing.&#10;Examples of correct usage of argument matchers:&#10;    when(mock.get(anyInt())).thenReturn(null);&#10;    doThrow(new RuntimeException()).when(mock).someVoidMethod(anyObject());&#10;    verify(mock).someMethod(contains(&quot;foo&quot;))&#10;&#10;This message may appear after an NullPointerException if the last matcher is returning an object &#10;like any() but the stubbed method signature expect a primitive argument, in this case,&#10;use primitive alternatives.&#10;    when(mock.get(any())); // bad use, will raise NPE&#10;    when(mock.get(anyInt())); // correct usage use&#10;&#10;Also, this error might show up because you use argument matchers with methods that cannot be mocked.&#10;Following methods *cannot* be stubbed/verified: final/private/equals()/hashCode().&#10;Mocking methods declared on non-public parent classes is not supported.&#10;" type="org.mockito.exceptions.misusing.InvalidUseOfMatchersException">org.mockito.exceptions.misusing.InvalidUseOfMatchersException: 

Misplaced or misused argument matcher detected here:

-> at com.fuint.common.service.PrinterCateServiceTest.testSavePrinterCateRelations(PrinterCateServiceTest.java:166)

You cannot use argument matchers outside of verification or stubbing.
Examples of correct usage of argument matchers:
    when(mock.get(anyInt())).thenReturn(null);
    doThrow(new RuntimeException()).when(mock).someVoidMethod(anyObject());
    verify(mock).someMethod(contains("foo"))

This message may appear after an NullPointerException if the last matcher is returning an object 
like any() but the stubbed method signature expect a primitive argument, in this case,
use primitive alternatives.
    when(mock.get(any())); // bad use, will raise NPE
    when(mock.get(anyInt())); // correct usage use

Also, this error might show up because you use argument matchers with methods that cannot be mocked.
Following methods *cannot* be stubbed/verified: final/private/equals()/hashCode().
Mocking methods declared on non-public parent classes is not supported.

</error>
  </testcase>
</testsuite>