
09427c75e386c6fb63d0c3258b8061a21f89da94	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.856d1b56720d1199924d.hot-update.js\",\"contentHash\":\"e89a0816cbf40119541b70e84d13ea08\"}","integrity":"sha512-559y+5xrrc6s7FtVeswtxSPrDgjNHC75OBYpFKm+QqgpElUzNcGKy8CztzAPj6XRxS7E4QiSQkyp7lRq1qr3tQ==","time":1754921686840,"size":52953}