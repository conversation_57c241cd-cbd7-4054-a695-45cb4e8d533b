{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigList.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigList.vue", "mtime": 1754839801250}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEdvb2RzVGltZUNvbmZpZ3MsIHNhdmVHb29kc1RpbWVDb25maWcsIHVwZGF0ZUdvb2RzVGltZUNvbmZpZywgZGVsZXRlR29vZHNUaW1lQ29uZmlnIH0gZnJvbSAnQC9hcGkvZ29vZHNUaW1lQ29uZmlnJwppbXBvcnQgVGltZUNvbmZpZ0Zvcm0gZnJvbSAnLi9UaW1lQ29uZmlnRm9ybScKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnVGltZUNvbmZpZ0xpc3QnLAogIGNvbXBvbmVudHM6IHsKICAgIFRpbWVDb25maWdGb3JtCiAgfSwKICBwcm9wczogewogICAgZ29vZHNJZDogewogICAgICB0eXBlOiBbU3RyaW5nLCBOdW1iZXJdLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBjb25maWdMaXN0OiBbXSwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGRpYWxvZ1RpdGxlOiAnJywKICAgICAgY3VycmVudENvbmZpZzoge30sCiAgICAgIGlzRWRpdDogZmFsc2UKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBnb29kc0lkOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCkgewogICAgICAgICAgdGhpcy5sb2FkQ29uZmlncygpCiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGxvYWRDb25maWdzKCkgewogICAgICBpZiAoIXRoaXMuZ29vZHNJZCkgcmV0dXJuCgogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIGdldEdvb2RzVGltZUNvbmZpZ3ModGhpcy5nb29kc0lkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmNvbmZpZ0xpc3QgPSByZXNwb25zZS5kYXRhIHx8IFtdCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaXtumXtOautemFjee9ruWksei0pTonLCBlcnJvcikKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3ml7bpl7TmrrXphY3nva7lpLHotKU6ICcgKyAoZXJyb3IubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfSkKICAgIH0sCgogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLmlzRWRpdCA9IGZhbHNlCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAn5paw5aKe5pe26Ze05q616YWN572uJwogICAgICB0aGlzLmN1cnJlbnRDb25maWcgPSB7CiAgICAgICAgZ29vZHNJZDogdGhpcy5nb29kc0lkLAogICAgICAgIGNvbmZpZ1R5cGU6ICdEQUlMWScsCiAgICAgICAgd2Vla0RheXM6IFtdLAogICAgICAgIGN1c3RvbURhdGVzOiBbXSwKICAgICAgICB0aW1lUmFuZ2VzOiBbeyBzdGFydFRpbWU6ICcnLCBlbmRUaW1lOiAnJyB9XSwKICAgICAgICBzdGF0dXM6ICdBJwogICAgICB9CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKCiAgICAgIC8vIOaPkOekuueUqOaIt+WPr+S7peW8gOWni+mFjee9rgogICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ivt+mFjee9ruaXtumXtOauteS/oeaBrycpCiAgICB9LAoKICAgIGhhbmRsZUVkaXQocm93KSB7CiAgICAgIHRoaXMuaXNFZGl0ID0gdHJ1ZQogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gJ+e8lui+keaXtumXtOautemFjee9ricKICAgICAgdGhpcy5jdXJyZW50Q29uZmlnID0gewogICAgICAgIC4uLnJvdywKICAgICAgICB3ZWVrRGF5czogcm93LmNvbmZpZ1R5cGUgPT09ICdXRUVLTFknID8gSlNPTi5wYXJzZShyb3cuY29uZmlnVmFsdWUgfHwgJ1tdJykgOiBbXSwKICAgICAgICBjdXN0b21EYXRlczogcm93LmNvbmZpZ1R5cGUgPT09ICdDVVNUT00nID8gSlNPTi5wYXJzZShyb3cuY29uZmlnVmFsdWUgfHwgJ1tdJykgOiBbXSwKICAgICAgICB0aW1lUmFuZ2VzOiBKU09OLnBhcnNlKHJvdy50aW1lUmFuZ2VzIHx8ICdbXScpCiAgICAgIH0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQoKICAgICAgLy8g5o+Q56S655So5oi35Y+v5Lul5byA5aeL57yW6L6RCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn6K+357yW6L6R5pe26Ze05q616YWN572u5L+h5oGvJykKICAgIH0sCgogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTliKDpmaTor6Xml7bpl7TmrrXphY3nva7lkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBkZWxldGVHb29kc1RpbWVDb25maWcocm93LmlkKS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykKICAgICAgICAgIHRoaXMubG9hZENvbmZpZ3MoKQoKICAgICAgICAgIC8vIOaPkOekuueUqOaIt+mFjee9ruW3suWIoOmZpAogICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfml7bpl7TmrrXphY3nva7lt7LliKDpmaQnKQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOWksei0pTonLCBlcnJvcikKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWksei0pTogJyArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSkKICAgICAgICB9KQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g55So5oi35Y+W5raI5Yig6ZmkCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7Llj5bmtojliKDpmaTmk43kvZwnKQogICAgICB9KQogICAgfSwKCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIGNvbnN0IHN0YXR1c1RleHQgPSByb3cuc3RhdHVzID09PSAnQScgPyAn5ZCv55SoJyA6ICfnpoHnlKgnCiAgICAgIHVwZGF0ZUdvb2RzVGltZUNvbmZpZyh7CiAgICAgICAgaWQ6IHJvdy5pZCwKICAgICAgICBzdGF0dXM6IHJvdy5zdGF0dXMKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGAke3N0YXR1c1RleHR95oiQ5YqfYCkKCiAgICAgICAgLy8g5o+Q56S655So5oi354q25oCB5bey5pu05pawCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKGDml7bpl7TmrrXphY3nva7lt7Ike3N0YXR1c1RleHR9YCkKICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+eKtuaAgeabtOaWsOWksei0pTonLCBlcnJvcikKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGAke3N0YXR1c1RleHR95aSx6LSlOiBgICsgKGVycm9yLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKQogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAnQScgPyAnTicgOiAnQScKICAgICAgfSkKICAgIH0sCgogICAgaGFuZGxlU2F2ZSgpIHsKICAgICAgdGhpcy4kcmVmcy50aW1lQ29uZmlnRm9ybS52YWxpZGF0ZSgpLnRoZW4oZm9ybURhdGEgPT4gewogICAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgICAuLi5mb3JtRGF0YSwKICAgICAgICAgIGNvbmZpZ1ZhbHVlOiBmb3JtRGF0YS5jb25maWdUeXBlID09PSAnV0VFS0xZJwogICAgICAgICAgICA/IEpTT04uc3RyaW5naWZ5KGZvcm1EYXRhLndlZWtEYXlzKQogICAgICAgICAgICA6IGZvcm1EYXRhLmNvbmZpZ1R5cGUgPT09ICdDVVNUT00nCiAgICAgICAgICAgICAgPyBKU09OLnN0cmluZ2lmeShmb3JtRGF0YS5jdXN0b21EYXRlcykKICAgICAgICAgICAgICA6ICcnLAogICAgICAgICAgdGltZVJhbmdlczogSlNPTi5zdHJpbmdpZnkoZm9ybURhdGEudGltZVJhbmdlcykKICAgICAgICB9CgogICAgICAgIGNvbnN0IHNhdmVQcm9taXNlID0gdGhpcy5pc0VkaXQKICAgICAgICAgID8gdXBkYXRlR29vZHNUaW1lQ29uZmlnKGRhdGEpCiAgICAgICAgICA6IHNhdmVHb29kc1RpbWVDb25maWcoZGF0YSkKCiAgICAgICAgc2F2ZVByb21pc2UudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgICAgdGhpcy5sb2FkQ29uZmlncygpCgogICAgICAgICAgLy8g5o+Q56S655So5oi36YWN572u5bey5pu05pawCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+aXtumXtOautemFjee9ruW3suabtOaWsCcpCiAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5aSx6LSlOicsIGVycm9yKQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5aSx6LSlOiAnICsgKGVycm9yLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKQogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAvLyDooajljZXpqozor4HlpLHotKUKICAgICAgfSkKICAgIH0sCgogICAgaGFuZGxlRGlhbG9nQ2xvc2UoKSB7CiAgICAgIHRoaXMuJHJlZnMudGltZUNvbmZpZ0Zvcm0gJiYgdGhpcy4kcmVmcy50aW1lQ29uZmlnRm9ybS5yZXNldEZvcm0oKQogICAgfSwKCiAgICBnZXRDb25maWdUeXBlVGFnKHR5cGUpIHsKICAgICAgY29uc3QgbWFwID0gewogICAgICAgIERBSUxZOiAnc3VjY2VzcycsCiAgICAgICAgV0VFS0xZOiAnd2FybmluZycsCiAgICAgICAgQ1VTVE9NOiAnaW5mbycKICAgICAgfQogICAgICByZXR1cm4gbWFwW3R5cGVdIHx8ICdkZWZhdWx0JwogICAgfSwKCiAgICBnZXRDb25maWdUeXBlVGV4dCh0eXBlKSB7CiAgICAgIGNvbnN0IG1hcCA9IHsKICAgICAgICBEQUlMWTogJ+avj+aXpemAmueUqCcsCiAgICAgICAgV0VFS0xZOiAn5oyJ5ZGo6K6+572uJywKICAgICAgICBDVVNUT006ICfoh6rlrprkuYnml6XmnJ8nCiAgICAgIH0KICAgICAgcmV0dXJuIG1hcFt0eXBlXSB8fCB0eXBlCiAgICB9LAoKICAgIGZvcm1hdFdlZWtEYXlzKHdlZWtEYXlzU3RyKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3Qgd2Vla0RheXMgPSBKU09OLnBhcnNlKHdlZWtEYXlzU3RyIHx8ICdbXScpCiAgICAgICAgY29uc3Qgd2Vla01hcCA9IHsKICAgICAgICAgIDE6ICflkajkuIAnLAogICAgICAgICAgMjogJ+WRqOS6jCcsCiAgICAgICAgICAzOiAn5ZGo5LiJJywKICAgICAgICAgIDQ6ICflkajlm5snLAogICAgICAgICAgNTogJ+WRqOS6lCcsCiAgICAgICAgICA2OiAn5ZGo5YWtJywKICAgICAgICAgIDc6ICflkajml6UnCiAgICAgICAgfQogICAgICAgIHJldHVybiB3ZWVrRGF5cy5tYXAoZGF5ID0+IHdlZWtNYXBbZGF5XSkuam9pbign44CBJykKICAgICAgfSBjYXRjaCB7CiAgICAgICAgcmV0dXJuIHdlZWtEYXlzU3RyCiAgICAgIH0KICAgIH0sCgogICAgZm9ybWF0Q3VzdG9tRGF0ZXMoZGF0ZXNTdHIpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBkYXRlcyA9IEpTT04ucGFyc2UoZGF0ZXNTdHIgfHwgJ1tdJykKICAgICAgICByZXR1cm4gZGF0ZXMuam9pbign44CBJykKICAgICAgfSBjYXRjaCB7CiAgICAgICAgcmV0dXJuIGRhdGVzU3RyCiAgICAgIH0KICAgIH0sCgogICAgcGFyc2VUaW1lUmFuZ2VzKHRpbWVSYW5nZXNTdHIpIHsKICAgICAgdHJ5IHsKICAgICAgICByZXR1cm4gSlNPTi5wYXJzZSh0aW1lUmFuZ2VzU3RyIHx8ICdbXScpCiAgICAgIH0gY2F0Y2ggewogICAgICAgIHJldHVybiBbXQogICAgICB9CiAgICB9LAoKICAgIHBhcnNlVGltZSh0aW1lKSB7CiAgICAgIGlmICghdGltZSkgcmV0dXJuICcnCiAgICAgIHJldHVybiB0aGlzLiRtb21lbnQodGltZSkuZm9ybWF0KCdZWVlZLU1NLUREIEhIOm1tOnNzJykKICAgIH0sCgogICAgLy8g5qC85byP5YyW5pe26Ze05q615pi+56S6CiAgICBmb3JtYXRUaW1lUmFuZ2Uoc3RhcnRUaW1lLCBlbmRUaW1lKSB7CiAgICAgIGlmICghc3RhcnRUaW1lIHx8ICFlbmRUaW1lKSByZXR1cm4gJycKICAgICAgcmV0dXJuIGAke3N0YXJ0VGltZX0gLSAke2VuZFRpbWV9YAogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["TimeConfigList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8FA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TimeConfigList.vue", "sourceRoot": "src/views/goods/components", "sourcesContent": ["<template>\n  <div class=\"time-config-list\">\n    <div class=\"header-actions\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增时间段配置</el-button>\n      <div class=\"form-tip\">提示：可以为商品设置多个时间段配置，如每日通用、按周设置或自定义日期</div>\n    </div>\n\n    <el-table :data=\"configList\" v-loading=\"loading\" style=\"width: 100%\">\n      <el-table-column prop=\"configType\" label=\"配置类型\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getConfigTypeTag(scope.row.configType)\">\n            {{ getConfigTypeText(scope.row.configType) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"configValue\" label=\"配置值\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div v-if=\"scope.row.configType === 'DAILY'\">\n            <span>每日通用</span>\n          </div>\n          <div v-else-if=\"scope.row.configType === 'WEEKLY'\">\n            <span>{{ formatWeekDays(scope.row.configValue) }}</span>\n          </div>\n          <div v-else-if=\"scope.row.configType === 'CUSTOM'\">\n            <span>{{ formatCustomDates(scope.row.configValue) }}</span>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"timeRanges\" label=\"时间段\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div v-for=\"(range, index) in parseTimeRanges(scope.row.timeRanges)\" :key=\"index\">\n            <el-tag size=\"mini\">{{ formatTimeRange(range.startTime, range.endTime) }}</el-tag>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"A\"\n            inactive-value=\"N\"\n            @change=\"handleStatusChange(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\">\n        <template slot-scope=\"scope\">\n          {{ parseTime(scope.row.createTime) }}\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"mini\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEdit(scope.row)\"\n          >编辑</el-button>\n          <el-button\n            type=\"text\"\n            size=\"mini\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      width=\"600px\"\n      @close=\"handleDialogClose\"\n    >\n      <TimeConfigForm\n        ref=\"timeConfigForm\"\n        v-model=\"currentConfig\"\n        :goods-id=\"goodsId\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleSave\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getGoodsTimeConfigs, saveGoodsTimeConfig, updateGoodsTimeConfig, deleteGoodsTimeConfig } from '@/api/goodsTimeConfig'\nimport TimeConfigForm from './TimeConfigForm'\n\nexport default {\n  name: 'TimeConfigList',\n  components: {\n    TimeConfigForm\n  },\n  props: {\n    goodsId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      configList: [],\n      dialogVisible: false,\n      dialogTitle: '',\n      currentConfig: {},\n      isEdit: false\n    }\n  },\n  watch: {\n    goodsId: {\n      handler(newVal) {\n        if (newVal) {\n          this.loadConfigs()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    loadConfigs() {\n      if (!this.goodsId) return\n\n      this.loading = true\n      getGoodsTimeConfigs(this.goodsId).then(response => {\n        this.configList = response.data || []\n        this.loading = false\n      }).catch(error => {\n        console.error('加载时间段配置失败:', error)\n        this.$message.error('加载时间段配置失败: ' + (error.message || '未知错误'))\n        this.loading = false\n      })\n    },\n\n    handleAdd() {\n      this.isEdit = false\n      this.dialogTitle = '新增时间段配置'\n      this.currentConfig = {\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      }\n      this.dialogVisible = true\n\n      // 提示用户可以开始配置\n      this.$message.info('请配置时间段信息')\n    },\n\n    handleEdit(row) {\n      this.isEdit = true\n      this.dialogTitle = '编辑时间段配置'\n      this.currentConfig = {\n        ...row,\n        weekDays: row.configType === 'WEEKLY' ? JSON.parse(row.configValue || '[]') : [],\n        customDates: row.configType === 'CUSTOM' ? JSON.parse(row.configValue || '[]') : [],\n        timeRanges: JSON.parse(row.timeRanges || '[]')\n      }\n      this.dialogVisible = true\n\n      // 提示用户可以开始编辑\n      this.$message.info('请编辑时间段配置信息')\n    },\n\n    handleDelete(row) {\n      this.$confirm('确认删除该时间段配置吗？', '提示', {\n        type: 'warning'\n      }).then(() => {\n        deleteGoodsTimeConfig(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.loadConfigs()\n\n          // 提示用户配置已删除\n          this.$message.info('时间段配置已删除')\n        }).catch(error => {\n          console.error('删除失败:', error)\n          this.$message.error('删除失败: ' + (error.message || '未知错误'))\n        })\n      }).catch(() => {\n        // 用户取消删除\n        this.$message.info('已取消删除操作')\n      })\n    },\n\n    handleStatusChange(row) {\n      const statusText = row.status === 'A' ? '启用' : '禁用'\n      updateGoodsTimeConfig({\n        id: row.id,\n        status: row.status\n      }).then(() => {\n        this.$message.success(`${statusText}成功`)\n\n        // 提示用户状态已更新\n        this.$message.info(`时间段配置已${statusText}`)\n      }).catch(error => {\n        console.error('状态更新失败:', error)\n        this.$message.error(`${statusText}失败: ` + (error.message || '未知错误'))\n        row.status = row.status === 'A' ? 'N' : 'A'\n      })\n    },\n\n    handleSave() {\n      this.$refs.timeConfigForm.validate().then(formData => {\n        const data = {\n          ...formData,\n          configValue: formData.configType === 'WEEKLY'\n            ? JSON.stringify(formData.weekDays)\n            : formData.configType === 'CUSTOM'\n              ? JSON.stringify(formData.customDates)\n              : '',\n          timeRanges: JSON.stringify(formData.timeRanges)\n        }\n\n        const savePromise = this.isEdit\n          ? updateGoodsTimeConfig(data)\n          : saveGoodsTimeConfig(data)\n\n        savePromise.then(() => {\n          this.$message.success('保存成功')\n          this.dialogVisible = false\n          this.loadConfigs()\n\n          // 提示用户配置已更新\n          this.$message.info('时间段配置已更新')\n        }).catch(error => {\n          console.error('保存失败:', error)\n          this.$message.error('保存失败: ' + (error.message || '未知错误'))\n        })\n      }).catch(() => {\n        // 表单验证失败\n      })\n    },\n\n    handleDialogClose() {\n      this.$refs.timeConfigForm && this.$refs.timeConfigForm.resetForm()\n    },\n\n    getConfigTypeTag(type) {\n      const map = {\n        DAILY: 'success',\n        WEEKLY: 'warning',\n        CUSTOM: 'info'\n      }\n      return map[type] || 'default'\n    },\n\n    getConfigTypeText(type) {\n      const map = {\n        DAILY: '每日通用',\n        WEEKLY: '按周设置',\n        CUSTOM: '自定义日期'\n      }\n      return map[type] || type\n    },\n\n    formatWeekDays(weekDaysStr) {\n      try {\n        const weekDays = JSON.parse(weekDaysStr || '[]')\n        const weekMap = {\n          1: '周一',\n          2: '周二',\n          3: '周三',\n          4: '周四',\n          5: '周五',\n          6: '周六',\n          7: '周日'\n        }\n        return weekDays.map(day => weekMap[day]).join('、')\n      } catch {\n        return weekDaysStr\n      }\n    },\n\n    formatCustomDates(datesStr) {\n      try {\n        const dates = JSON.parse(datesStr || '[]')\n        return dates.join('、')\n      } catch {\n        return datesStr\n      }\n    },\n\n    parseTimeRanges(timeRangesStr) {\n      try {\n        return JSON.parse(timeRangesStr || '[]')\n      } catch {\n        return []\n      }\n    },\n\n    parseTime(time) {\n      if (!time) return ''\n      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss')\n    },\n\n    // 格式化时间段显示\n    formatTimeRange(startTime, endTime) {\n      if (!startTime || !endTime) return ''\n      return `${startTime} - ${endTime}`\n    }\n  }\n}\n</script>\n\n<style scoped>\n.time-config-list {\n  padding: 20px;\n}\n\n.header-actions {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}