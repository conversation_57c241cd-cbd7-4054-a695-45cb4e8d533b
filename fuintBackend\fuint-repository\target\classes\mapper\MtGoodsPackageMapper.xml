<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtGoodsPackageMapper">
    <select id="getPackageGoodsList" resultType="com.fuint.repository.model.MtGoodsPackage">
        select * from mt_package_item t where t.GOODS_ID = #{packageId} and t.STATUS = 'A' order by t.sort asc
    </select>
    
    <delete id="deletePackageGoods">
        delete from mt_package_item where GOODS_ID = #{packageId}
    </delete>
    
    <select id="getGoodsPackages" resultType="com.fuint.repository.model.MtGoodsPackage">
        select * from mt_package_item t where t.GOODS_ID = #{goodsId} and t.STATUS = 'A' order by t.sort asc
    </select>
</mapper>