
41ed58ede3cbb5da712b134e4f56d5c71f030990	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"531426ea934a1771f2deb83c2dcc71f0\"}","integrity":"sha512-pB9Atl+bEQmL4DkcMbANCQqbpt8oHWtk4USaNfrg9RRpAM8VhYGYMNCKZ7Wyzs7Oro7Uz3rWLKTcKmgc1G6G6A==","time":1754921686941,"size":2882840}