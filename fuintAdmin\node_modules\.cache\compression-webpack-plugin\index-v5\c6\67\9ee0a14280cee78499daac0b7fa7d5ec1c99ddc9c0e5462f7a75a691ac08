
2c5aee5bb52e006a2ca4caaccaf82f40c6391432	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"a04a8c9dda55417c2d62daa7ed9b01b7\"}","integrity":"sha512-Hr2Khd0co/0x7UKuia8PkoL+Ww3XoiWBGBxxxHKcvyGqjavMPtTsnE4lq5tEqloXjn97bcmbaNJb1gKhIKdWIA==","time":1754921993640,"size":2883333}