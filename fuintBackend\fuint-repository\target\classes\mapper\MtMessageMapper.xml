<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtMessageMapper">
    <select id="findNewMessage" resultType="com.fuint.repository.model.MtMessage">
        select * from mt_message o where o.USER_ID = #{userId} and o.STATUS = 'A' and o.TYPE = #{type} and o.IS_READ = 'N' order by o.ID desc
    </select>
    <select id="findNeedSendMessage" resultType="com.fuint.repository.model.MtMessage">
        select * from mt_message t where t.TYPE = #{type} and t.IS_SEND = 'N' order by t.SEND_TIME asc
    </select>
</mapper>
