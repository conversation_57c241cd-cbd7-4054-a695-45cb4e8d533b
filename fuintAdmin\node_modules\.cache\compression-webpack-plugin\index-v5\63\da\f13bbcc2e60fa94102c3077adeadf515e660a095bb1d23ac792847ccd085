
792c2f619c6c0722109052ff5d22d8c1f951458f	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.eefa6bb5b92e23e7b68c.hot-update.js\",\"contentHash\":\"3597b12551ce2078216df7d14a107774\"}","integrity":"sha512-4gwVxyWjhV0oCyakEh5qi5oG98T5ehCtdgRX4T4bQh410UPXbgHDmxQ7P3qpdIHL3e5+ENgpEqP0yie8olB7Sg==","time":1754921567965,"size":99883}