
656a7ec64ae600d1c5a29509c9cd391ebf3e9f01	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"185509b8a851eaa75b1cc99fb13c96f6\"}","integrity":"sha512-UhFabJDsWVGTBOY0N2DYgnf5O6l1Hsw2JIXqzaFhI2GlZ3QxkfhyfNLV5HQD7nQDNC6YVRu9qE+GSpbmSTMZcQ==","time":1754922009683,"size":6669348}