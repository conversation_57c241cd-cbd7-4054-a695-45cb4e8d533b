<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtCouponMapper">
    <select id="queryNumByGroupId" resultType="long">
        SELECT COUNT(0) FROM mt_coupon t where t.GROUP_ID = #{groupId} and t.STATUS != 'D'
    </select>

    <select id="queryByGroupId" resultType="com.fuint.repository.model.MtCoupon">
        select * from mt_coupon t where t.GROUP_ID = #{groupId}
    </select>
</mapper>
