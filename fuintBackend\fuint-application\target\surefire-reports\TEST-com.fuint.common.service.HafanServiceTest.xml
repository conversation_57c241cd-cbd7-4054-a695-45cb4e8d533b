<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.fuint.common.service.HafanServiceTest" time="26.783" tests="5" errors="4" skipped="0" failures="1">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\test-classes;D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\classes;C:\Users\<USER>\.m2\repository\com\huifu\bspay\sdk\dg-java-sdk\3.0.2\dg-java-sdk-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.70\fastjson-1.2.70.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.2\httpclient-4.5.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.2\httpmime-4.5.2.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.9.1\okhttp-4.9.1.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\2.8.0\okio-2.8.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.5.32\kotlin-stdlib-common-1.5.32.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.5.32\kotlin-stdlib-1.5.32.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-framework\target\fuint-framework-1.0.0.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-utils\target\fuint-utils-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.3.0\core-3.3.0.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\javase\3.3.0\javase-3.3.0.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.48\jcommander-1.48.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.3.1\jai-imageio-core-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.58\bcprov-jdk15on-1.58.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\3.14\poi-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\3.14\poi-ooxml-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\3.14\poi-ooxml-schemas-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\2.6.0\xmlbeans-2.6.0.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.03\curvesapi-1.03.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-repository\target\fuint-repository-1.0.0.jar;C:\Users\<USER>\.m2\repository\io\sentry\sentry-logback\1.2.0\sentry-logback-1.2.0.jar;C:\Users\<USER>\.m2\repository\io\sentry\sentry\1.2.0\sentry-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.12.6\jackson-core-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\ws\spring-ws-core\3.1.3\spring-ws-core-3.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\ws\spring-xml\3.1.3\spring-xml-3.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.18\spring-beans-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.18\spring-oxm-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.18\spring-web-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.18\spring-webmvc-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.18\spring-expression-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.18\spring-core-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.18\spring-jcl-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.5.12\spring-boot-starter-security-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.5.5\spring-security-config-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.5.5\spring-security-core-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.5.5\spring-security-crypto-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.5.5\spring-security-web-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\commons-httpclient\commons-httpclient\3.1\commons-httpclient-3.1.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.0.4\commons-logging-1.0.4.jar;C:\Users\<USER>\.m2\repository\nl\bitwalker\UserAgentUtils\1.2.4\UserAgentUtils-1.2.4.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\20.0\guava-20.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.1.0\mybatis-plus-boot-starter-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.5.12\spring-boot-autoconfigure-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.5.12\spring-boot-starter-jdbc-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.18\spring-jdbc-5.3.18.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.1.0\mybatis-plus-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.1.0\mybatis-plus-extension-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.1.0\mybatis-plus-core-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.1.0\mybatis-plus-annotation-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.0\mybatis-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.0\mybatis-spring-2.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.2.5\pagehelper-spring-boot-starter-1.2.5.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\1.3.2\mybatis-spring-boot-starter-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\1.3.2\mybatis-spring-boot-autoconfigure-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.2.5\pagehelper-spring-boot-autoconfigure-1.2.5.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\5.1.4\pagehelper-5.1.4.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\1.0\jsqlparser-1.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.9.0\mockito-core-3.9.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;C:\Users\<USER>\.m2\repository\com\github\axet\kaptcha\0.0.9\kaptcha-0.0.9.jar;C:\Users\<USER>\.m2\repository\com\jhlabs\filters\2.0.235\filters-2.0.235.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.5.12\spring-boot-starter-test-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.5.12\spring-boot-test-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.5.12\spring-boot-test-autoconfigure-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.5.0\json-path-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.19.0\assertj-core-3.19.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.7.2\junit-jupiter-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.7.2\junit-jupiter-api-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.7.2\junit-platform-commons-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.7.2\junit-jupiter-params-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.7.2\junit-jupiter-engine-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.7.2\junit-platform-engine-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.9.0\mockito-junit-jupiter-3.9.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.3.18\spring-test-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.24.0-GA\javassist-3.24.0-GA.jar;C:\Users\<USER>\.m2\repository\com\aliyun\oss\aliyun-sdk-oss\3.10.2\aliyun-sdk-oss-3.10.2.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jettison\jettison\1.1\jettison-1.1.jar;C:\Users\<USER>\.m2\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ram\3.0.0\aliyun-java-sdk-ram-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-sts\3.0.0\aliyun-java-sdk-sts-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ecs\4.2.0\aliyun-java-sdk-ecs-4.2.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-kms\2.7.0\aliyun-java-sdk-kms-2.7.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-core\4.4.6\aliyun-java-sdk-core-4.4.6.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\jacoco\org.jacoco.agent\0.8.3\org.jacoco.agent-0.8.3-runtime.jar;C:\Users\<USER>\.m2\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Users\<USER>\.m2\repository\com\alibaba\transmittable-thread-local\2.2.0\transmittable-thread-local-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-WxPay\2.9.6\IJPay-WxPay-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-Core\2.9.6\IJPay-Core-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\github\xkzhangsan\xk-time\3.2.4\xk-time-3.2.4.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-crypto\5.8.11\hutool-crypto-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-core\5.8.11\hutool-core-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-http\5.8.11\hutool-http-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-json\5.8.11\hutool-json-5.8.11.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-AliPay\2.9.6\IJPay-AliPay-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\alipay\sdk\alipay-sdk-java\4.35.37.ALL\alipay-sdk-java-4.35.37.ALL.jar;C:\Users\<USER>\.m2\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.0.b2\xml-apis-1.0.b2.jar;C:\Users\<USER>\.m2\repository\org\apache\velocity\velocity-engine-core\2.3\velocity-engine-core-2.3.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.13.0\commons-io-2.13.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.5.12\spring-boot-starter-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.5.12\spring-boot-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.5.12\spring-boot-starter-logging-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.28\snakeyaml-1.28.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.5.12\spring-boot-starter-web-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.5.12\spring-boot-starter-json-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.12.6.1\jackson-databind-2.12.6.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.12.6\jackson-datatype-jdk8-2.12.6.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.12.6\jackson-datatype-jsr310-2.12.6.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.12.6\jackson-module-parameter-names-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jetty\2.5.12\spring-boot-starter-jetty-2.5.12.jar;C:\Users\<USER>\.m2\repository\jakarta\servlet\jakarta.servlet-api\4.0.4\jakarta.servlet-api-4.0.4.jar;C:\Users\<USER>\.m2\repository\jakarta\websocket\jakarta.websocket-api\1.1.2\jakarta.websocket-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.60\tomcat-embed-el-9.0.60.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlets\9.4.45.v20220203\jetty-servlets-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-continuation\9.4.45.v20220203\jetty-continuation-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-http\9.4.45.v20220203\jetty-http-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util\9.4.45.v20220203\jetty-util-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-io\9.4.45.v20220203\jetty-io-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-webapp\9.4.45.v20220203\jetty-webapp-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-xml\9.4.45.v20220203\jetty-xml-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlet\9.4.45.v20220203\jetty-servlet-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-security\9.4.45.v20220203\jetty-security-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-server\9.4.45.v20220203\jetty-server-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util-ajax\9.4.45.v20220203\jetty-util-ajax-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-server\9.4.45.v20220203\websocket-server-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-common\9.4.45.v20220203\websocket-common-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-api\9.4.45.v20220203\websocket-api-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-client\9.4.45.v20220203\websocket-client-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-client\9.4.45.v20220203\jetty-client-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-servlet\9.4.45.v20220203\websocket-servlet-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\javax-websocket-server-impl\9.4.45.v20220203\javax-websocket-server-impl-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-annotations\9.4.45.v20220203\jetty-annotations-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-plus\9.4.45.v20220203\jetty-plus-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.2\asm-commons-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.2\asm-tree-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-analysis\9.2\asm-analysis-9.2.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\javax-websocket-client-impl\9.4.45.v20220203\javax-websocket-client-impl-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.5.12\spring-boot-actuator-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-generator\3.1.0\mybatis-plus-generator-3.1.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.21\swagger-annotations-1.5.21.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.21\swagger-models-1.5.21.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.12.6\jackson-annotations-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.18\spring-context-support-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.18\spring-context-5.3.18.jar;C:\Users\<USER>\.m2\repository\net\sf\ehcache\ehcache\2.10.2\ehcache-2.10.2.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\4.0.2\kryo-4.0.2.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\reflectasm\1.11.3\reflectasm-1.11.3.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\minlog\1.3.0\minlog-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.18\spring-aop-5.3.18.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.5.12\spring-boot-starter-data-redis-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.5.10\spring-data-redis-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.5.10\spring-data-keyvalue-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.5.10\spring-data-commons-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.18\spring-tx-5.3.18.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.8.RELEASE\lettuce-core-6.1.8.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.75.Final\netty-common-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.75.Final\netty-handler-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.75.Final\netty-resolver-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.75.Final\netty-buffer-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.75.Final\netty-codec-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.75.Final\netty-transport-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.16\reactor-core-3.4.16.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\session\spring-session-data-redis\2.5.5\spring-session-data-redis-2.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\session\spring-session-core\2.5.5\spring-session-core-2.5.5.jar;C:\Users\<USER>\.m2\repository\com\github\liyiorg\weixin-popular\2.8.0\weixin-popular-2.8.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.1.12\jaxb-impl-2.1.12.jar;C:\Users\<USER>\.m2\repository\com\vdurmont\emoji-java\3.1.1\emoji-java-3.1.1.jar;C:\Users\<USER>\.m2\repository\org\json\json\20140107\json-20140107.jar;C:\Users\<USER>\.m2\repository\org\tuckey\urlrewritefilter\4.0.3\urlrewritefilter-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 11"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\scoop\apps\OracleJDK8\current\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire2644371510761120621\surefirebooter1416207632114688668.jar C:\Users\<USER>\AppData\Local\Temp\surefire2644371510761120621 2025-08-11T21-46-24_756-jvmRun1 surefire1610464489176024432tmp surefire_0990885862063378314tmp"/>
    <property name="surefire.test.class.path" value="D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\test-classes;D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\classes;C:\Users\<USER>\.m2\repository\com\huifu\bspay\sdk\dg-java-sdk\3.0.2\dg-java-sdk-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.70\fastjson-1.2.70.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.2\httpclient-4.5.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.2\httpmime-4.5.2.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.9.1\okhttp-4.9.1.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\2.8.0\okio-2.8.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.5.32\kotlin-stdlib-common-1.5.32.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.5.32\kotlin-stdlib-1.5.32.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-framework\target\fuint-framework-1.0.0.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-utils\target\fuint-utils-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.3.0\core-3.3.0.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\javase\3.3.0\javase-3.3.0.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.48\jcommander-1.48.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.3.1\jai-imageio-core-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.58\bcprov-jdk15on-1.58.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\3.14\poi-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\3.14\poi-ooxml-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\3.14\poi-ooxml-schemas-3.14.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\2.6.0\xmlbeans-2.6.0.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.03\curvesapi-1.03.jar;D:\workspace\fuintFoodSystem\fuintBackend\fuint-repository\target\fuint-repository-1.0.0.jar;C:\Users\<USER>\.m2\repository\io\sentry\sentry-logback\1.2.0\sentry-logback-1.2.0.jar;C:\Users\<USER>\.m2\repository\io\sentry\sentry\1.2.0\sentry-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.12.6\jackson-core-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\ws\spring-ws-core\3.1.3\spring-ws-core-3.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\ws\spring-xml\3.1.3\spring-xml-3.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.18\spring-beans-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.18\spring-oxm-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.18\spring-web-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.18\spring-webmvc-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.18\spring-expression-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.18\spring-core-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.18\spring-jcl-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.5.12\spring-boot-starter-security-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.5.5\spring-security-config-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.5.5\spring-security-core-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.5.5\spring-security-crypto-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.5.5\spring-security-web-5.5.5.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\commons-httpclient\commons-httpclient\3.1\commons-httpclient-3.1.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.0.4\commons-logging-1.0.4.jar;C:\Users\<USER>\.m2\repository\nl\bitwalker\UserAgentUtils\1.2.4\UserAgentUtils-1.2.4.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\20.0\guava-20.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.1.0\mybatis-plus-boot-starter-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.5.12\spring-boot-autoconfigure-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.5.12\spring-boot-starter-jdbc-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.18\spring-jdbc-5.3.18.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.1.0\mybatis-plus-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.1.0\mybatis-plus-extension-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.1.0\mybatis-plus-core-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.1.0\mybatis-plus-annotation-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.0\mybatis-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.0\mybatis-spring-2.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.2.5\pagehelper-spring-boot-starter-1.2.5.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\1.3.2\mybatis-spring-boot-starter-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\1.3.2\mybatis-spring-boot-autoconfigure-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.2.5\pagehelper-spring-boot-autoconfigure-1.2.5.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\5.1.4\pagehelper-5.1.4.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\1.0\jsqlparser-1.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\3.9.0\mockito-core-3.9.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;C:\Users\<USER>\.m2\repository\com\github\axet\kaptcha\0.0.9\kaptcha-0.0.9.jar;C:\Users\<USER>\.m2\repository\com\jhlabs\filters\2.0.235\filters-2.0.235.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.5.12\spring-boot-starter-test-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.5.12\spring-boot-test-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.5.12\spring-boot-test-autoconfigure-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.5.0\json-path-2.5.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.19.0\assertj-core-3.19.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.7.2\junit-jupiter-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.7.2\junit-jupiter-api-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.7.2\junit-platform-commons-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.7.2\junit-jupiter-params-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.7.2\junit-jupiter-engine-5.7.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.7.2\junit-platform-engine-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\3.9.0\mockito-junit-jupiter-3.9.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.3.18\spring-test-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.24.0-GA\javassist-3.24.0-GA.jar;C:\Users\<USER>\.m2\repository\com\aliyun\oss\aliyun-sdk-oss\3.10.2\aliyun-sdk-oss-3.10.2.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jettison\jettison\1.1\jettison-1.1.jar;C:\Users\<USER>\.m2\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ram\3.0.0\aliyun-java-sdk-ram-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-sts\3.0.0\aliyun-java-sdk-sts-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-ecs\4.2.0\aliyun-java-sdk-ecs-4.2.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-kms\2.7.0\aliyun-java-sdk-kms-2.7.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\aliyun-java-sdk-core\4.4.6\aliyun-java-sdk-core-4.4.6.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\jacoco\org.jacoco.agent\0.8.3\org.jacoco.agent-0.8.3-runtime.jar;C:\Users\<USER>\.m2\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Users\<USER>\.m2\repository\com\alibaba\transmittable-thread-local\2.2.0\transmittable-thread-local-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-WxPay\2.9.6\IJPay-WxPay-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-Core\2.9.6\IJPay-Core-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\github\xkzhangsan\xk-time\3.2.4\xk-time-3.2.4.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-crypto\5.8.11\hutool-crypto-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-core\5.8.11\hutool-core-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-http\5.8.11\hutool-http-5.8.11.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-json\5.8.11\hutool-json-5.8.11.jar;C:\Users\<USER>\.m2\repository\com\github\javen205\IJPay-AliPay\2.9.6\IJPay-AliPay-2.9.6.jar;C:\Users\<USER>\.m2\repository\com\alipay\sdk\alipay-sdk-java\4.35.37.ALL\alipay-sdk-java-4.35.37.ALL.jar;C:\Users\<USER>\.m2\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.0.b2\xml-apis-1.0.b2.jar;C:\Users\<USER>\.m2\repository\org\apache\velocity\velocity-engine-core\2.3\velocity-engine-core-2.3.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.13.0\commons-io-2.13.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.5.12\spring-boot-starter-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.5.12\spring-boot-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.5.12\spring-boot-starter-logging-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.28\snakeyaml-1.28.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.5.12\spring-boot-starter-web-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.5.12\spring-boot-starter-json-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.12.6.1\jackson-databind-2.12.6.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.12.6\jackson-datatype-jdk8-2.12.6.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.12.6\jackson-datatype-jsr310-2.12.6.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.12.6\jackson-module-parameter-names-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jetty\2.5.12\spring-boot-starter-jetty-2.5.12.jar;C:\Users\<USER>\.m2\repository\jakarta\servlet\jakarta.servlet-api\4.0.4\jakarta.servlet-api-4.0.4.jar;C:\Users\<USER>\.m2\repository\jakarta\websocket\jakarta.websocket-api\1.1.2\jakarta.websocket-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.60\tomcat-embed-el-9.0.60.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlets\9.4.45.v20220203\jetty-servlets-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-continuation\9.4.45.v20220203\jetty-continuation-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-http\9.4.45.v20220203\jetty-http-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util\9.4.45.v20220203\jetty-util-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-io\9.4.45.v20220203\jetty-io-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-webapp\9.4.45.v20220203\jetty-webapp-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-xml\9.4.45.v20220203\jetty-xml-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlet\9.4.45.v20220203\jetty-servlet-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-security\9.4.45.v20220203\jetty-security-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-server\9.4.45.v20220203\jetty-server-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util-ajax\9.4.45.v20220203\jetty-util-ajax-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-server\9.4.45.v20220203\websocket-server-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-common\9.4.45.v20220203\websocket-common-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-api\9.4.45.v20220203\websocket-api-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-client\9.4.45.v20220203\websocket-client-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-client\9.4.45.v20220203\jetty-client-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\websocket-servlet\9.4.45.v20220203\websocket-servlet-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\javax-websocket-server-impl\9.4.45.v20220203\javax-websocket-server-impl-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-annotations\9.4.45.v20220203\jetty-annotations-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-plus\9.4.45.v20220203\jetty-plus-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.2\asm-commons-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.2\asm-tree-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-analysis\9.2\asm-analysis-9.2.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\websocket\javax-websocket-client-impl\9.4.45.v20220203\javax-websocket-client-impl-9.4.45.v20220203.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.5.12\spring-boot-actuator-2.5.12.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-generator\3.1.0\mybatis-plus-generator-3.1.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.21\swagger-annotations-1.5.21.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.21\swagger-models-1.5.21.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.12.6\jackson-annotations-2.12.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.18\spring-context-support-5.3.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.18\spring-context-5.3.18.jar;C:\Users\<USER>\.m2\repository\net\sf\ehcache\ehcache\2.10.2\ehcache-2.10.2.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\4.0.2\kryo-4.0.2.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\reflectasm\1.11.3\reflectasm-1.11.3.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\com\esotericsoftware\minlog\1.3.0\minlog-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.18\spring-aop-5.3.18.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.5.12\spring-boot-starter-data-redis-2.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.5.10\spring-data-redis-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.5.10\spring-data-keyvalue-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.5.10\spring-data-commons-2.5.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.18\spring-tx-5.3.18.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.8.RELEASE\lettuce-core-6.1.8.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.75.Final\netty-common-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.75.Final\netty-handler-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.75.Final\netty-resolver-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.75.Final\netty-buffer-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.75.Final\netty-codec-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.75.Final\netty-transport-4.1.75.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.16\reactor-core-3.4.16.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\session\spring-session-data-redis\2.5.5\spring-session-data-redis-2.5.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\session\spring-session-core\2.5.5\spring-session-core-2.5.5.jar;C:\Users\<USER>\.m2\repository\com\github\liyiorg\weixin-popular\2.8.0\weixin-popular-2.8.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.1.12\jaxb-impl-2.1.12.jar;C:\Users\<USER>\.m2\repository\com\vdurmont\emoji-java\3.1.1\emoji-java-3.1.1.jar;C:\Users\<USER>\.m2\repository\org\json\json\20140107\json-20140107.jar;C:\Users\<USER>\.m2\repository\org\tuckey\urlrewritefilter\4.0.3\urlrewritefilter-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\scoop\apps\OracleJDK8\current\jre"/>
    <property name="basedir" value="D:\workspace\fuintFoodSystem\fuintBackend\fuint-application"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire2644371510761120621\surefirebooter1416207632114688668.jar"/>
    <property name="sun.boot.class.path" value="D:\scoop\apps\OracleJDK8\current\jre\lib\resources.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\rt.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\jsse.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\jce.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\charsets.jar;D:\scoop\apps\OracleJDK8\current\jre\lib\jfr.jar;D:\scoop\apps\OracleJDK8\current\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_421-b09"/>
    <property name="user.name" value="HomePC"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\scoop\apps\OracleJDK8\current\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_421"/>
    <property name="user.dir" value="D:\workspace\fuintFoodSystem\fuintBackend\fuint-application"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\scoop\apps\OracleJDK8\current\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\scoop\apps\MiKTeX\current\texmfs\install\miktex\bin\x64;D:\scoop\apps\python\current\Scripts;D:\scoop\apps\python\current;D:\scoop\apps\openssl\current\bin;D:\scoop\apps\yarn\current\global\node_modules\.bin;D:\scoop\apps\yarn\current\bin;D:\scoop\apps\OracleJDK8\current\bin;D:\scoop\apps\openjdk21\current\bin;D:\scoop\apps\nodejs-lts\current\bin;D:\scoop\apps\nodejs-lts\current;D:\scoop\apps\maven\current\bin;D:\scoop\apps\ImageMagick\current;D:\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\scoop\shims;D:\soft\VSCode\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.421-b09"/>
    <property name="java.specification.maintenance.version" value="5"/>
    <property name="java.ext.dirs" value="D:\scoop\apps\OracleJDK8\current\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testCreateOrder" classname="com.fuint.common.service.HafanServiceTest" time="4.437">
    <error message="Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect" type="org.apache.http.conn.HttpHostConnectException">org.apache.http.conn.HttpHostConnectException: Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at com.fuint.common.service.HafanServiceTest.testCreateOrder(HafanServiceTest.java:39)
Caused by: java.net.ConnectException: Connection refused: connect
	at com.fuint.common.service.HafanServiceTest.testCreateOrder(HafanServiceTest.java:39)
</error>
    <system-out><![CDATA[21:46:35.866 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating CacheAwareContextLoaderDelegate from class [org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate]
21:46:35.874 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating BootstrapContext using constructor [public org.springframework.test.context.support.DefaultBootstrapContext(java.lang.Class,org.springframework.test.context.CacheAwareContextLoaderDelegate)]
21:46:35.897 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating TestContextBootstrapper for test class [com.fuint.common.service.HafanServiceTest] from class [org.springframework.boot.test.context.SpringBootTestContextBootstrapper]
21:46:35.907 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.fuint.common.service.HafanServiceTest], using SpringBootContextLoader
21:46:35.910 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.fuint.common.service.HafanServiceTest]: class path resource [com/fuint/common/service/HafanServiceTest-context.xml] does not exist
21:46:35.910 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.fuint.common.service.HafanServiceTest]: class path resource [com/fuint/common/service/HafanServiceTestContext.groovy] does not exist
21:46:35.910 [main] INFO org.springframework.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.fuint.common.service.HafanServiceTest]: no resource found for suffixes {-context.xml, Context.groovy}.
21:46:35.911 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.fuint.common.service.HafanServiceTest]: HafanServiceTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
21:46:35.954 [main] DEBUG org.springframework.test.context.support.ActiveProfilesUtils - Could not find an 'annotation declaring class' for annotation type [org.springframework.test.context.ActiveProfiles] and class [com.fuint.common.service.HafanServiceTest]
21:46:36.011 [main] DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider - Identified candidate component class: file [D:\workspace\fuintFoodSystem\fuintBackend\fuint-application\target\classes\com\fuint\fuintApplication.class]
21:46:36.012 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.fuint.fuintApplication for test class com.fuint.common.service.HafanServiceTest
21:46:36.081 [main] DEBUG org.springframework.boot.test.context.SpringBootTestContextBootstrapper - @TestExecutionListeners is not present for class [com.fuint.common.service.HafanServiceTest]: using defaults.
21:46:36.081 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
21:46:36.096 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@57ac5227, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ba302e0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@e98770d, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@1ae67cad, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@2f6e28bc, org.springframework.test.context.support.DirtiesContextTestExecutionListener@7c098bb3, org.springframework.test.context.transaction.TransactionalTestExecutionListener@31e4bb20, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@18cebaa5, org.springframework.test.context.event.EventPublishingTestExecutionListener@463b4ac8, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@765f05af, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@62f68dff, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@f001896, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@13f17eb4, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@1d0d6318, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@4bc28c33]
21:46:36.099 [main] DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener - Before test class: context [DefaultTestContext@45905bff testClass = HafanServiceTest, testInstance = [null], testMethod = [null], testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@2a2c13a8 testClass = HafanServiceTest, locations = '{}', classes = '{class com.fuint.fuintApplication}', contextInitializerClasses = '[]', activeProfiles = '{}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@34b9f960, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@4bbf6d0e, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@2205a05d, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@560348e6, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@4dd6fd0a, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@30ee2816], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true]], class annotated with @DirtiesContext [false] with mode [null].

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.5.12)

08-11 21:46:36.409 [main] WARN  o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer - 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/C:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class
	jar:file:/C:/Users/<USER>/.m2/repository/org/json/json/20140107/json-20140107.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

08-11 21:46:36.426 [main] INFO  c.f.common.service.HafanServiceTest - Starting HafanServiceTest using Java 1.8.0_421 on HomePC with PID 14796 (started by HomePC in D:\workspace\fuintFoodSystem\fuintBackend\fuint-application)
08-11 21:46:36.427 [main] INFO  c.f.common.service.HafanServiceTest - The following 1 profile is active: "dev"
08-11 21:46:37.245 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08-11 21:46:37.248 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
08-11 21:46:37.285 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
08-11 21:46:37.761 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.ws.config.annotation.DelegatingWsConfiguration' of type [org.springframework.ws.config.annotation.DelegatingWsConfiguration$$EnhancerBySpringCGLIB$$3fcdd8bc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08-11 21:46:37.790 [main] INFO  o.s.w.s.a.s.AnnotationActionEndpointMapping - Supporting [WS-Addressing August 2004, WS-Addressing 1.0]
08-11 21:46:38.044 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@5f45bc8e' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08-11 21:46:38.056 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.1.0 
Registered plugin: 'AbstractSqlParserHandler(sqlParserList=null, sqlParserFilter=null)'
Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor@764db16c'
08-11 21:46:39.202 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
08-11 21:46:39.354 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtAddressMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtArticleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtBalanceMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtBannerMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtBookItemMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCartMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCommissionCashMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCommissionLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCommissionRelationMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCommissionRuleItemMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCommissionRuleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtConfirmLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCouponGoodsMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCouponGroupMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtCouponMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtGiveItemMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtGiveMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtGoodsCateMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtGoodsMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtGoodsPackageMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtGoodsSkuMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtGoodsSpecMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtGoodsTimeConfigMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtMerchantMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtMessageMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtOpenGiftItemMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtOpenGiftMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtOrderAddressMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtOrderGoodsMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtOrderMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtPointMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtPrinterCateMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtPrinterMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtRefundMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtRegionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtSendLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtSettingMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtSettlementMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtSettlementOrderMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtSmsSendedLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtSmsTemplateMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtStaffMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtStoreMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtTableMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtUserActionMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtUserCouponMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtUserGradeMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtUserGroupMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtUserMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/MtVerifyCodeMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/TAccountDutyMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/TAccountMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/TActionLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/TDutyMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/TDutySourceMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/TGenCodeMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/TPlatformMapper.xml]'
Parsed mapper file: 'URL [jar:file:/D:/workspace/fuintFoodSystem/fuintBackend/fuint-repository/target/fuint-repository-1.0.0.jar!/mapper/TSourceMapper.xml]'
08-11 21:46:41.674 [main] INFO  com.fuint.common.web.SystemInit - Executing system initialization
08-11 21:46:41.674 [main] INFO  com.fuint.common.web.SystemInit - Initial system config
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ee68eb2] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@43856881 wrapping com.mysql.cj.jdbc.ConnectionImpl@77e5c765] will not be managed by Spring
==>  Preparing: SELECT ID,merchant_id,store_id,type,name,value,description,create_time,update_time,operator,status FROM mt_setting WHERE type = ? 
==> Parameters: system(String)
<==      Total: 0
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ee68eb2]
08-11 21:46:41.775 [main] INFO  com.fuint.common.web.SystemInit - Completed system initialization
08-11 21:46:42.826 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
08-11 21:46:43.049 [main] INFO  org.eclipse.jetty.util.log - Logging initialized @7618ms to org.eclipse.jetty.util.log.Slf4jLog
08-11 21:46:43.815 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
08-11 21:46:44.031 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
08-11 21:46:44.053 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
08-11 21:46:44.085 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
08-11 21:46:44.215 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
08-11 21:46:44.218 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_1
08-11 21:46:44.219 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_2
08-11 21:46:44.222 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_1
08-11 21:46:44.227 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_3
08-11 21:46:44.231 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_2
08-11 21:46:44.232 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_4
08-11 21:46:44.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_1
08-11 21:46:44.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_2
08-11 21:46:44.237 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_3
08-11 21:46:44.238 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_5
08-11 21:46:44.240 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_2
08-11 21:46:44.241 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_3
08-11 21:46:44.243 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_4
08-11 21:46:44.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_6
08-11 21:46:44.245 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_3
08-11 21:46:44.246 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_4
08-11 21:46:44.247 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_5
08-11 21:46:44.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_7
08-11 21:46:44.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_4
08-11 21:46:44.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_5
08-11 21:46:44.267 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_6
08-11 21:46:44.268 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_8
08-11 21:46:44.269 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_6
08-11 21:46:44.279 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_7
08-11 21:46:44.280 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_9
08-11 21:46:44.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
08-11 21:46:44.289 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_8
08-11 21:46:44.290 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_10
08-11 21:46:44.292 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
08-11 21:46:44.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_9
08-11 21:46:44.296 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_11
08-11 21:46:44.305 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_5
08-11 21:46:44.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_7
08-11 21:46:44.310 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_12
08-11 21:46:44.312 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_1
08-11 21:46:44.313 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_10
08-11 21:46:44.314 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_13
08-11 21:46:44.326 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_2
08-11 21:46:44.328 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_11
08-11 21:46:44.328 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_14
08-11 21:46:44.332 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_3
08-11 21:46:44.333 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_8
08-11 21:46:44.341 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_12
08-11 21:46:44.342 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_15
08-11 21:46:44.348 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_13
08-11 21:46:44.348 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_16
08-11 21:46:44.348 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_6
08-11 21:46:44.349 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_9
08-11 21:46:44.352 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_17
08-11 21:46:44.354 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_3
08-11 21:46:44.356 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_14
08-11 21:46:44.356 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_18
08-11 21:46:44.357 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_7
08-11 21:46:44.359 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_10
08-11 21:46:44.362 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_19
08-11 21:46:44.362 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_4
08-11 21:46:44.363 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
08-11 21:46:44.370 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_4
08-11 21:46:44.372 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_15
08-11 21:46:44.373 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_20
08-11 21:46:44.373 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: resetPwdUsingPOST_1
08-11 21:46:44.374 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_5
08-11 21:46:44.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveSettingUsingPOST_1
08-11 21:46:44.376 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: settingUsingGET_1
08-11 21:46:44.376 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_11
08-11 21:46:44.378 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_5
08-11 21:46:44.379 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_16
08-11 21:46:44.379 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_21
08-11 21:46:44.381 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_6
08-11 21:46:44.382 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_12
08-11 21:46:44.384 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_22
08-11 21:46:44.384 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingHEAD_1
08-11 21:46:44.385 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_1
08-11 21:46:44.385 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPUT_1
08-11 21:46:44.385 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPATCH_1
08-11 21:46:44.386 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingDELETE_1
08-11 21:46:44.386 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingOPTIONS_1
08-11 21:46:44.386 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingTRACE_1
08-11 21:46:44.386 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_8
08-11 21:46:44.388 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_13
08-11 21:46:44.391 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_6
08-11 21:46:44.394 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_17
08-11 21:46:44.394 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_23
08-11 21:46:44.394 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_14
08-11 21:46:44.396 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_7
08-11 21:46:44.397 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingGET_1
08-11 21:46:44.397 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_18
08-11 21:46:44.404 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_2
08-11 21:46:44.405 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_7
08-11 21:46:44.406 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveSettingUsingPOST_2
08-11 21:46:44.406 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: settingUsingGET_2
08-11 21:46:44.407 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_15
08-11 21:46:44.410 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_24
08-11 21:46:44.412 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: doRechargeUsingPOST_1
08-11 21:46:44.412 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_25
08-11 21:46:44.413 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveSettingUsingPOST_3
08-11 21:46:44.413 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: settingUsingGET_3
08-11 21:46:44.415 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_26
08-11 21:46:44.416 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_8
08-11 21:46:44.419 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getCategoriesUsingGET_1
08-11 21:46:44.419 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getPrinterCategoriesUsingGET_1
08-11 21:46:44.420 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_19
08-11 21:46:44.420 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_27
08-11 21:46:44.421 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_9
08-11 21:46:44.422 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveSettingUsingPOST_4
08-11 21:46:44.422 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: settingUsingGET_4
08-11 21:46:44.424 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_16
08-11 21:46:44.426 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_20
08-11 21:46:44.426 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_28
08-11 21:46:44.427 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_9
08-11 21:46:44.427 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_29
08-11 21:46:44.429 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: doConfirmUsingPOST_1
08-11 21:46:44.432 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_21
08-11 21:46:44.433 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_30
08-11 21:46:44.434 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_31
08-11 21:46:44.434 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveSettingUsingPOST_5
08-11 21:46:44.434 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: settingUsingGET_5
08-11 21:46:44.435 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_8
08-11 21:46:44.436 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_22
08-11 21:46:44.436 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_32
08-11 21:46:44.439 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_10
08-11 21:46:44.440 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_9
08-11 21:46:44.441 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_23
08-11 21:46:44.441 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_33
08-11 21:46:44.442 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
08-11 21:46:44.443 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_34
08-11 21:46:44.444 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_11
08-11 21:46:44.445 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_17
08-11 21:46:44.448 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
08-11 21:46:44.449 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_24
08-11 21:46:44.450 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_35
08-11 21:46:44.451 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_10
08-11 21:46:44.452 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_36
08-11 21:46:44.452 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_12
08-11 21:46:44.454 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_18
08-11 21:46:44.456 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_25
08-11 21:46:44.456 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_37
08-11 21:46:44.458 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_13
08-11 21:46:44.460 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_26
08-11 21:46:44.460 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_38
08-11 21:46:44.460 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_14
08-11 21:46:44.461 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_19
08-11 21:46:44.462 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_10
08-11 21:46:44.463 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_39
08-11 21:46:44.465 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_11
08-11 21:46:44.466 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_27
08-11 21:46:44.467 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_40
08-11 21:46:44.467 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveHandlerUsingPOST_15
08-11 21:46:44.467 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_20
08-11 21:46:44.469 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_41
08-11 21:46:44.470 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_11
08-11 21:46:44.473 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingPOST_1
08-11 21:46:44.476 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_3
08-11 21:46:44.478 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: doRechargeUsingPOST_2
08-11 21:46:44.479 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_4
08-11 21:46:44.480 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: settingUsingGET_6
08-11 21:46:44.484 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingPOST_2
08-11 21:46:44.485 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_5
08-11 21:46:44.488 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: checkCodeUsingPOST_1
08-11 21:46:44.489 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getCodeUsingGET_1
08-11 21:46:44.494 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_6
08-11 21:46:44.496 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_12
08-11 21:46:44.500 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: doConfirmUsingPOST_2
08-11 21:46:44.502 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingPOST_3
08-11 21:46:44.504 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_7
08-11 21:46:44.510 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: cateListUsingGET_1
08-11 21:46:44.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingPOST_4
08-11 21:46:44.512 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_42
08-11 21:46:44.513 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_43
08-11 21:46:44.516 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_44
08-11 21:46:44.517 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: cancelUsingGET_1
08-11 21:46:44.517 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
08-11 21:46:44.518 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_8
08-11 21:46:44.520 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: doPayUsingGET_1
08-11 21:46:44.524 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: doGiveUsingPOST_1
08-11 21:46:44.524 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_45
08-11 21:46:44.526 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_2
08-11 21:46:44.526 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_46
08-11 21:46:44.527 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: submitUsingPOST_1
08-11 21:46:44.533 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: submitUsingPOST_2
08-11 21:46:44.536 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_9
08-11 21:46:44.541 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_3
08-11 21:46:44.541 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_10
08-11 21:46:44.542 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_28
08-11 21:46:44.544 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: settingUsingGET_7
08-11 21:46:44.546 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_4
08-11 21:46:44.547 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_29
08-11 21:46:44.548 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: infoUsingGET_30
08-11 21:46:44.551 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_11
08-11 21:46:44.552 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: cancelUsingPOST_1
08-11 21:46:44.552 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingPOST_5
08-11 21:46:44.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_12
08-11 21:46:44.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_47
08-11 21:46:45.943 [main] INFO  c.f.common.service.HafanServiceTest - Started HafanServiceTest in 9.811 seconds (JVM running for 10.511)
08-11 21:46:50.381 [main] ERROR com.fuint.common.util.HttpUtil - HTTP请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:158)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:353)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:380)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:184)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:88)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:184)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:82)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:107)
	at com.fuint.common.util.HttpUtil.doPost(HttpUtil.java:45)
	at com.fuint.common.service.impl.HafanServiceImpl.sendRequest(HafanServiceImpl.java:78)
	at com.fuint.common.service.impl.HafanServiceImpl.createOrder(HafanServiceImpl.java:96)
	at com.fuint.common.service.HafanServiceTest.testCreateOrder(HafanServiceTest.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:688)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:210)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:206)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:131)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:65)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:74)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:141)
	... 78 common frames omitted
]]></system-out>
  </testcase>
  <testcase name="testFullOrderProcess" classname="com.fuint.common.service.HafanServiceTest" time="4.069">
    <error message="Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect" type="org.apache.http.conn.HttpHostConnectException">org.apache.http.conn.HttpHostConnectException: Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at com.fuint.common.service.HafanServiceTest.testFullOrderProcess(HafanServiceTest.java:89)
Caused by: java.net.ConnectException: Connection refused: connect
	at com.fuint.common.service.HafanServiceTest.testFullOrderProcess(HafanServiceTest.java:89)
</error>
    <system-out><![CDATA[08-11 21:46:54.466 [main] ERROR com.fuint.common.util.HttpUtil - HTTP请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:158)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:353)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:380)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:184)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:88)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:184)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:82)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:107)
	at com.fuint.common.util.HttpUtil.doPost(HttpUtil.java:45)
	at com.fuint.common.service.impl.HafanServiceImpl.sendRequest(HafanServiceImpl.java:78)
	at com.fuint.common.service.impl.HafanServiceImpl.createOrder(HafanServiceImpl.java:96)
	at com.fuint.common.service.HafanServiceTest.testFullOrderProcess(HafanServiceTest.java:89)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:688)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:210)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:206)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:131)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:65)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:74)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:141)
	... 78 common frames omitted
]]></system-out>
  </testcase>
  <testcase name="testGetUserInfo" classname="com.fuint.common.service.HafanServiceTest" time="4.053">
    <error message="Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect" type="org.apache.http.conn.HttpHostConnectException">org.apache.http.conn.HttpHostConnectException: Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at com.fuint.common.service.HafanServiceTest.testGetUserInfo(HafanServiceTest.java:28)
Caused by: java.net.ConnectException: Connection refused: connect
	at com.fuint.common.service.HafanServiceTest.testGetUserInfo(HafanServiceTest.java:28)
</error>
    <system-out><![CDATA[08-11 21:46:58.520 [main] ERROR com.fuint.common.util.HttpUtil - HTTP请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:158)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:353)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:380)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:184)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:88)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:184)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:82)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:107)
	at com.fuint.common.util.HttpUtil.doPost(HttpUtil.java:45)
	at com.fuint.common.service.impl.HafanServiceImpl.sendRequest(HafanServiceImpl.java:78)
	at com.fuint.common.service.impl.HafanServiceImpl.getUserInfo(HafanServiceImpl.java:122)
	at com.fuint.common.service.HafanServiceTest.testGetUserInfo(HafanServiceTest.java:28)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:688)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:210)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:206)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:131)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:65)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:74)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:141)
	... 78 common frames omitted
]]></system-out>
  </testcase>
  <testcase name="testCancelOrder" classname="com.fuint.common.service.HafanServiceTest" time="0.011">
    <failure message="订单号不应为空 ==&gt; expected: not &lt;null&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: 订单号不应为空 ==> expected: not <null>
	at com.fuint.common.service.HafanServiceTest.testCancelOrder(HafanServiceTest.java:60)
]]></failure>
  </testcase>
  <testcase name="testCompleteOrder" classname="com.fuint.common.service.HafanServiceTest" time="4.076">
    <error message="Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect" type="org.apache.http.conn.HttpHostConnectException">org.apache.http.conn.HttpHostConnectException: Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at com.fuint.common.service.HafanServiceTest.testCompleteOrder(HafanServiceTest.java:74)
Caused by: java.net.ConnectException: Connection refused: connect
	at com.fuint.common.service.HafanServiceTest.testCompleteOrder(HafanServiceTest.java:74)
</error>
    <system-out><![CDATA[08-11 21:47:00.013 [pool-1-thread-1] INFO  c.f.module.schedule.CouponExpireJob - -------------------------com.fuint.module.schedule.CouponExpireJob------------------------------------
08-11 21:47:00.013 [pool-1-thread-1] INFO  c.f.module.schedule.CouponExpireJob - methodName = dealCoupon
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3168b4a5]
JDBC Connection [HikariProxyConnection@1604495478 wrapping com.mysql.cj.jdbc.ConnectionImpl@77e5c765] will be managed by Spring
==>  Preparing: SELECT * FROM mt_user_coupon t WHERE t.STATUS = ? AND t.EXPIRE_TIME < ? AND t.EXPIRE_TIME > ? AND t.USED_TIME IS NULL ORDER BY t.UPDATE_TIME ASC 
==> Parameters: A(String), 2025-08-11 21:47:00(String), 2024-08-11 21:47:00(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3168b4a5]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3168b4a5] from current transaction
==>  Preparing: SELECT * FROM mt_user_coupon t WHERE t.STATUS = ? AND t.EXPIRE_TIME < ? AND t.EXPIRE_TIME > ? AND t.USED_TIME IS NULL ORDER BY t.UPDATE_TIME ASC 
==> Parameters: A(String), 2025-08-14 21:47:00(String), 2025-08-11 21:47:00(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3168b4a5]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3168b4a5]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3168b4a5]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3168b4a5]
08-11 21:47:00.123 [pool-1-thread-1] INFO  com.fuint.module.schedule.MessageJob - -------------------------com.fuint.module.schedule.MessageJob------------------------------------
08-11 21:47:00.123 [pool-1-thread-1] INFO  com.fuint.module.schedule.MessageJob - methodName = dealMessage
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76a3ecf7]
JDBC Connection [HikariProxyConnection@929907592 wrapping com.mysql.cj.jdbc.ConnectionImpl@77e5c765] will be managed by Spring
==>  Preparing: select * from mt_message t where t.TYPE = ? and t.IS_SEND = 'N' order by t.SEND_TIME asc 
==> Parameters: sub(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76a3ecf7]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76a3ecf7]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76a3ecf7]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76a3ecf7]
08-11 21:47:00.141 [pool-1-thread-1] INFO  c.f.module.schedule.OrderCancelJob - -------------------------com.fuint.module.schedule.OrderCancelJob------------------------------------
08-11 21:47:00.141 [pool-1-thread-1] INFO  c.f.module.schedule.OrderCancelJob - methodName = dealOrder
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
JDBC Connection [HikariProxyConnection@1869312952 wrapping com.mysql.cj.jdbc.ConnectionImpl@77e5c765] will be managed by Spring
==>  Preparing: SELECT ID,type,pay_type,order_mode,platform,order_sn,coupon_id,merchant_id,store_id,table_id,user_id,verify_code,is_visitor,amount,pay_amount,settle_status,use_point,point_amount,discount,delivery_fee,param,express_info,remark,create_time,update_time,status,pay_time,pay_status,staff_id,operator,commission_status,commission_user_id,hf_order,take_mode,external_order,invoice_url,reservation_time,is_reservation,reservation_status,version FROM mt_order WHERE status = ? 
==> Parameters: A(String)
<==    Columns: ID, type, pay_type, order_mode, platform, order_sn, coupon_id, merchant_id, store_id, table_id, user_id, verify_code, is_visitor, amount, pay_amount, settle_status, use_point, point_amount, discount, delivery_fee, param, express_info, remark, create_time, update_time, status, pay_time, pay_status, staff_id, operator, commission_status, commission_user_id, hf_order, take_mode, external_order, invoice_url, reservation_time, is_reservation, reservation_status, version
<==        Row: 682, goods, MICROPAY, oneself, PC, 202507292052058679390, 0, 2, 7, 0, 324, , Y, 14.00, 14.00, A, 0, 0.00, 0.00, 0.00, , , , 2025-07-29 20:52:06, 2025-07-29 20:52:06, A, null, A, 18, mu8600074, A, 0, <<BLOB>>, 堂食, null, null, null, N, A, 2
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155] from current transaction
==>  Preparing: SELECT ID,type,pay_type,order_mode,platform,order_sn,coupon_id,merchant_id,store_id,table_id,user_id,verify_code,is_visitor,amount,pay_amount,settle_status,use_point,point_amount,discount,delivery_fee,param,express_info,remark,create_time,update_time,status,pay_time,pay_status,staff_id,operator,commission_status,commission_user_id,hf_order,take_mode,external_order,invoice_url,reservation_time,is_reservation,reservation_status,version FROM mt_order WHERE ID=? 
==> Parameters: 682(Integer)
<==    Columns: ID, type, pay_type, order_mode, platform, order_sn, coupon_id, merchant_id, store_id, table_id, user_id, verify_code, is_visitor, amount, pay_amount, settle_status, use_point, point_amount, discount, delivery_fee, param, express_info, remark, create_time, update_time, status, pay_time, pay_status, staff_id, operator, commission_status, commission_user_id, hf_order, take_mode, external_order, invoice_url, reservation_time, is_reservation, reservation_status, version
<==        Row: 682, goods, MICROPAY, oneself, PC, 202507292052058679390, 0, 2, 7, 0, 324, , Y, 14.00, 14.00, A, 0, 0.00, 0.00, 0.00, , , , 2025-07-29 20:52:06, 2025-07-29 20:52:06, A, null, A, 18, mu8600074, A, 0, <<BLOB>>, 堂食, null, null, null, N, A, 2
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
08-11 21:47:00.213 [pool-1-thread-1] INFO  c.f.c.service.impl.OrderServiceImpl - orderService.cancelOrder orderId = 682, remark = 超时未支付取消
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155] from current transaction
==>  Preparing: UPDATE mt_order SET type=?, pay_type=?, order_mode=?, platform=?, order_sn=?, coupon_id=?, merchant_id=?, store_id=?, table_id=?, user_id=?, verify_code=?, is_visitor=?, amount=?, pay_amount=?, settle_status=?, use_point=?, point_amount=?, discount=?, delivery_fee=?, param=?, express_info=?, remark=?, create_time=?, update_time=?, status=?, pay_status=?, staff_id=?, operator=?, commission_status=?, commission_user_id=?, take_mode=?, is_reservation=?, reservation_status=?, version=? WHERE ID=? AND version=? 
==> Parameters: goods(String), MICROPAY(String), oneself(String), PC(String), 202507292052058679390(String), 0(Integer), 2(Integer), 7(Integer), 0(Integer), 324(Integer), (String), Y(String), 14.00(BigDecimal), 14.00(BigDecimal), A(String), 0(Integer), 0.00(BigDecimal), 0.00(BigDecimal), 0.00(BigDecimal), (String), (String), 超时未支付取消(String), 2025-07-29 20:52:06.0(Timestamp), 2025-07-29 20:52:06.0(Timestamp), C(String), A(String), 18(Integer), mu8600074(String), A(String), 0(Integer), 堂食(String), N(String), A(String), 3(Integer), 682(Integer), 2(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155] from current transaction
==>  Preparing: select * from mt_confirm_log o where o.ORDER_ID = ? and status = 'A' 
==> Parameters: 682(Integer)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155] from current transaction
==>  Preparing: SELECT ID,order_id,goods_id,sku_id,price,discount,num,create_time,update_time,status FROM mt_order_goods WHERE ORDER_ID = ? 
==> Parameters: 682(Integer)
<==    Columns: ID, order_id, goods_id, sku_id, price, discount, num, create_time, update_time, status
<==        Row: 1085, 682, 674, 51438, 14.00, 0.00, 1, 2025-07-29 20:52:06, 2025-07-29 20:52:06, A
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155] from current transaction
==>  Preparing: SELECT ID,merchant_id,store_id,name,type,cate_id,goods_no,is_single_spec,logo,images,price,grade_price,line_price,stock,coupon_ids,service_time,weight,init_sale,sale_point,can_use_point,is_member_discount,sort,description,create_time,update_time,operator,status,produce_code,drink_maker,make_code,source_id,enable_time_config,time_config_type FROM mt_goods WHERE ID=? 
==> Parameters: 674(Integer)
<==    Columns: ID, merchant_id, store_id, name, type, cate_id, goods_no, is_single_spec, logo, images, price, grade_price, line_price, stock, coupon_ids, service_time, weight, init_sale, sale_point, can_use_point, is_member_discount, sort, description, create_time, update_time, operator, status, produce_code, drink_maker, make_code, source_id, enable_time_config, time_config_type
<==        Row: 674, 2, 7, 白柚汁, goods, 285, 182335611171098, N, /static/uploadImages/20250725/0677e90696d24927b0fc58164c4fabd5.jpg, ["/static/uploadImages/20250725/0677e90696d24927b0fc58164c4fabd5.jpg"], 16.00, 13.00, 0.00, 9992, , 0, 0.00, 7, 含有丰富的维生素C，能增强免疫力、促进胶原蛋白合成、抗氧化，味道酸甜适中，口感清爽。, N, N, 0, <<BLOB>>, 2025-03-27 11:20:05, 2025-07-25 15:25:14, admin123, A, null, 21, A78, null, N, 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155] from current transaction
==>  Preparing: UPDATE mt_goods SET merchant_id=?, store_id=?, name=?, type=?, cate_id=?, goods_no=?, is_single_spec=?, logo=?, images=?, price=?, grade_price=?, line_price=?, stock=?, coupon_ids=?, service_time=?, weight=?, init_sale=?, sale_point=?, can_use_point=?, is_member_discount=?, sort=?, create_time=?, update_time=?, operator=?, status=?, drink_maker=?, make_code=?, enable_time_config=?, time_config_type=? WHERE ID=? 
==> Parameters: 2(Integer), 7(Integer), 白柚汁(String), goods(String), 285(Integer), 182335611171098(String), N(String), /static/uploadImages/20250725/0677e90696d24927b0fc58164c4fabd5.jpg(String), ["/static/uploadImages/20250725/0677e90696d24927b0fc58164c4fabd5.jpg"](String), 16.00(BigDecimal), 13.00(BigDecimal), 0.00(BigDecimal), 9993(Integer), (String), 0(Integer), 0.00(BigDecimal), 7(Integer), 含有丰富的维生素C，能增强免疫力、促进胶原蛋白合成、抗氧化，味道酸甜适中，口感清爽。(String), N(String), N(String), 0(Integer), 2025-03-27 11:20:05.0(Timestamp), 2025-07-25 15:25:14.0(Timestamp), admin123(String), A(String), 21(String), A78(String), N(String), 1(String), 674(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155] from current transaction
==>  Preparing: SELECT ID,sku_no,logo,goods_id,spec_ids,stock,price,grade_price,line_price,weight,status,produce_code FROM mt_goods_sku WHERE ID=? 
==> Parameters: 51438(Integer)
<==    Columns: ID, sku_no, logo, goods_id, spec_ids, stock, price, grade_price, line_price, weight, status, produce_code
<==        Row: 51438, 1604418783764137, , 674, 2492-2984-2722-2723, 9999, 14.00, 11.00, 0.00, 0.00, A, =A78|C02,T03,W01
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155] from current transaction
==>  Preparing: UPDATE mt_goods_sku SET sku_no=?, logo=?, goods_id=?, spec_ids=?, stock=?, price=?, grade_price=?, line_price=?, weight=?, status=?, produce_code=? WHERE ID=? 
==> Parameters: 1604418783764137(String), (String), 674(Integer), 2492-2984-2722-2723(String), 10000(Integer), 14.00(BigDecimal), 11.00(BigDecimal), 0.00(BigDecimal), 0.00(BigDecimal), A(String), =A78|C02,T03,W01(String), 51438(Integer)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cd29155]
08-11 21:47:00.325 [pool-1-thread-1] INFO  c.f.m.schedule.ReservationPrintJob - -------------------------com.fuint.module.schedule.ReservationPrintJob------------------------------------
08-11 21:47:00.325 [pool-1-thread-1] INFO  c.f.m.schedule.ReservationPrintJob - methodName = processReservationOrders
08-11 21:47:00.325 [pool-1-thread-1] INFO  c.f.m.schedule.ReservationPrintJob - ReservationPrintJob开始执行...
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63964d8]
JDBC Connection [HikariProxyConnection@875833271 wrapping com.mysql.cj.jdbc.ConnectionImpl@77e5c765] will be managed by Spring
==>  Preparing: SELECT ID,type,pay_type,order_mode,platform,order_sn,coupon_id,merchant_id,store_id,table_id,user_id,verify_code,is_visitor,amount,pay_amount,settle_status,use_point,point_amount,discount,delivery_fee,param,express_info,remark,create_time,update_time,status,pay_time,pay_status,staff_id,operator,commission_status,commission_user_id,hf_order,take_mode,external_order,invoice_url,reservation_time,is_reservation,reservation_status,version FROM mt_order WHERE pay_status = ? AND is_reservation = ? AND reservation_status = ? 
==> Parameters: B(String), Y(String), A(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63964d8]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63964d8]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63964d8]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63964d8]
08-11 21:47:00.343 [pool-1-thread-1] INFO  c.f.module.schedule.CommissionJob - -------------------------com.fuint.module.schedule.CommissionJob------------------------------------
08-11 21:47:00.343 [pool-1-thread-1] INFO  c.f.module.schedule.CommissionJob - methodName = dealOrder
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fa1c042]
JDBC Connection [HikariProxyConnection@1847782594 wrapping com.mysql.cj.jdbc.ConnectionImpl@77e5c765] will be managed by Spring
==>  Preparing: SELECT t.* FROM `mt_order` t WHERE t.PAY_TIME <= ? AND t.STATUS NOT IN('C', 'G', 'H') AND t.PAY_STATUS = 'B' AND t.COMMISSION_STATUS = 'A' ORDER BY t.ID DESC LIMIT 10 
==> Parameters: 2025-08-18 21:47:00(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fa1c042]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fa1c042]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fa1c042]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fa1c042]
08-11 21:47:02.608 [main] ERROR com.fuint.common.util.HttpUtil - HTTP请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:1337 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:158)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:353)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:380)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:184)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:88)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:184)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:82)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:107)
	at com.fuint.common.util.HttpUtil.doPost(HttpUtil.java:45)
	at com.fuint.common.service.impl.HafanServiceImpl.sendRequest(HafanServiceImpl.java:78)
	at com.fuint.common.service.impl.HafanServiceImpl.createOrder(HafanServiceImpl.java:96)
	at com.fuint.common.service.HafanServiceTest.testCompleteOrder(HafanServiceTest.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:688)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:210)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:206)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:131)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:65)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:74)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:141)
	... 78 common frames omitted
]]></system-out>
  </testcase>
</testsuite>