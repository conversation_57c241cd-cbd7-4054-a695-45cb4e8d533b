
3b447e63a646a3fdaf160ae3d284be7a8247eaad	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.93be81c43dc1b2883c18.hot-update.js\",\"contentHash\":\"81c51393063fc661a4039f72899d819a\"}","integrity":"sha512-C9V2/iz0fwr1EHtXBLvy9zFIwqraczXkfGaTN6va2w9nw4OvLw2V6FPi/NOoQnrTbBvx7v5hT1O4UP0WBAuyUg==","time":1754921522627,"size":27043}