{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\goodsForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\goodsForm.vue", "mtime": 1754840036318}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJwppbXBvcnQgeyBzYXZlR29vZHMsIGdldEdvb2RzSW5mbywgZ2V0UGFja2FnZUl0ZW1zLCBzYXZlUGFja2FnZUl0ZW1zIH0gZnJvbSAiQC9hcGkvZ29vZHMiOwppbXBvcnQgU2t1IGZyb20gJy4uL2NvbXBvbmVudHMvU2t1JzsKaW1wb3J0IFBhY2thZ2VJdGVtTmV3IGZyb20gJy4uL2NvbXBvbmVudHMvUGFja2FnZUl0ZW1OZXcnOwppbXBvcnQgVGltZUNvbmZpZ0xpc3QgZnJvbSAnLi4vY29tcG9uZW50cy9UaW1lQ29uZmlnTGlzdCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiR29vZHNGb3JtIiwKICBjb21wb25lbnRzOiB7IFNrdSwgUGFja2FnZUl0ZW1OZXcsIFRpbWVDb25maWdMaXN0IH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHN0b3JlSWQ6IHRoaXMuJHN0b3JlLmdldHRlcnMuc3RvcmVJZCwKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBhY3RpdmVUYWI6ICdiYXNlJywKICAgICAgc3RvcmVPcHRpb25zOiBbXSwKICAgICAgY2F0ZU9wdGlvbnM6IFtdLAogICAgICB0eXBlT3B0aW9uczogW10sCiAgICAgIHNrdURhdGE6IHsgYXR0ckxpc3Q6IFtdLCBza3VMaXN0OiBbXSwgaW5pdFNrdUxpc3Q6IFtdIH0sCiAgICAgIC8vIOWll+mkkOWVhuWTgeaVsOaNrgogICAgICBwYWNrYWdlRGF0YTogW10sCiAgICAgIC8vIOWfuuehgOS/oeaBr+ihqOWNlQogICAgICBiYXNlRm9ybTogeyB0eXBlOiAnZ29vZHMnLCBnb29kc0lkOiAnJywgbmFtZTogJycsIHN0b3JlSWQ6IHRoaXMuJHN0b3JlLmdldHRlcnMuc3RvcmVJZCwgY2F0ZUlkOiAnJywgZ29vZHNObzogJycsIGltYWdlczogW10sIHN0YXR1czogIkEiLCBzb3J0OiAwIH0sCiAgICAgIC8vIOaJqeWxleS/oeaBr+ihqOWNlQogICAgICBleHRlbmRGb3JtOiB7IGdvb2RzSWQ6ICcnLCBjYW5Vc2VQb2ludDogJ1knLCBpc01lbWJlckRpc2NvdW50OiAnWScsIGlzU2luZ2xlU3BlYzogJ1knLCBwcm9kdWNlQ29kZTogJycsIHNlcnZpY2VUaW1lOiAwLCBjb3Vwb25JZHM6ICcnLCBzdG9jazogJycsIHByaWNlOiAnJywgZ3JhZGVQcmljZTogJycgLGxpbmVQcmljZTogJycsIHNhbGVQb2ludDogJycsIG1ha2VDb2RlOiAnJywgZHJpbmtNYWtlcjogJycsIGluaXRTYWxlOiAnJywgd2VpZ2h0OiAnJywgc2t1RGF0YTogbnVsbCB9LAogICAgICAvLyDor6bmg4Xkv6Hmga/ooajljZUKICAgICAgZGV0YWlsRm9ybTogeyBnb29kc0lkOiAnJywgZGVzY3JpcHRpb24gOiAnJyB9LAogICAgICAvLyDkuIrkvKDlnLDlnYAKICAgICAgdXBsb2FkQWN0aW9uOiBwcm9jZXNzLmVudi5WVUVfQVBQX1NFUlZFUl9VUkwgKyAnYmFja2VuZEFwaS9maWxlL3VwbG9hZCcsCiAgICAgIHVwbG9hZEhlYWRlcjogeyAnQWNjZXNzLVRva2VuJyA6IGdldFRva2VuKCkgfSwKICAgICAgLy8g5LiK5Lyg5Z+f5ZCNCiAgICAgIHVwbG9hZERvbWFpbjogJycsCiAgICAgIC8vIOS4iuS8oOaWh+S7tuWIl+ihqAogICAgICB1cGxvYWRGaWxlczogW10sCiAgICAgIC8vIOWfuuehgOS/oeaBr+ihqOWNleagoemqjAogICAgICBiYXNlUnVsZXM6IHsKICAgICAgICBuYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5ZWG5ZOB5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAzMCwgbWVzc2FnZTogJ+WVhuWTgeWQjeensOmVv+W6puW/hemhu+S7i+S6jjLlkowyMDAg5LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGdvb2RzTm86IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLllYblk4HmnaHnoIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDEwMCwgbWVzc2FnZTogJ+WVhuWTgeadoeeggemVv+W6puW/hemhu+S7i+S6jjLlkowxMDDkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgY2F0ZUlkOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5ZWG5ZOB5YiG57G7IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGltYWdlczogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+S4iuS8oOWVhuWTgeWbvueJhyIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgfSwKICAgICAgLy8g5omp5bGV5L+h5oGv6KGo5Y2V5qCh6aqMCiAgICAgIGV4dGVuZFJ1bGVzOiB7CiAgICAgICAgY291cG9uSWRzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Y2h5Yi4SUTkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiAxLCBtYXg6IDEwMDAsIG1lc3NhZ2U6ICfljaHliLhJROmVv+W6puW/hemhu+S7i+S6jjHlkowxMDDkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgY2FuVXNlUG9pbnQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6kiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgaXNNZW1iZXJEaXNjb3VudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqSIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBpc1NpbmdsZVNwZWM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6kiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcHJpY2U6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXllYblk4Hku7fmoLwiLCB0cmlnZ2VyOiAiYmx1ciIgfSwgeyBwYXR0ZXJuOiAvKF5bMS05XVxkKihcLlxkezEsMn0pPyQpfCheMChcLlxkezEsMn0pPyQpLywgbWVzc2FnZTogYOS7t+agvOW/hemhu+Wkp+S6jjBgLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgLyogcHJvZHVjZUNvZGU6IFsKICAgICAgICAgIHsgcGF0dGVybjogL149QSgwWzEtOV18WzEtOV1bMC05XSlcfEMoMFsxLTldfFsxLTldWzAtOV0pLFQoMFsxLTldfFsxLTldWzAtOV0pLFcoMFsxLTldfFsxLTldWzAtOV0pJC8sIG1lc3NhZ2U6IGDovpPlhaXmraPnoa7nmoTliLbkvZznoIFgLCB0cmlnZ2VyOiAnaW5wdXQnIH0KICAgICAgICBdLCAqLwogICAgICAgIHN0b2NrOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5bqT5a2Y5pWw6YePIiwgdHJpZ2dlcjogImJsdXIiIH0sIHsgcGF0dGVybjogL15bMC05XXsxLDEwfSQvLCBtZXNzYWdlOiBg5bqT5a2Y5pWw6YeP5b+F6aG75piv5q2j5pW05pWwYCwgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGluaXRTYWxlOiBbCiAgICAgICAgICB7IHBhdHRlcm46IC9eWzAtOV17MSwxMH0kLywgbWVzc2FnZTogYOWIneWni+mUgOmHj+W/hemhu+aYr+ato+aVtOaVsGAsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICB3ZWlnaHQ6IFsKICAgICAgICAgIHsgcGF0dGVybjogLyheWzEtOV1cZCooXC5cZHsxLDJ9KT8kKXwoXjAoXC5cZHsxLDJ9KT8kKS8sIG1lc3NhZ2U6IGDph43ph4/lv4XpobvlpKfkuo7nrYnkuo4wYCwgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICB9LAogICAgICBkZXRhaWxSdWxlczogewogICAgICAgIGRlc2NyaXB0aW9uOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5ZWG5ZOB6K+m5oOFIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIGNvbnN0IGdvb2RzSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5nb29kc0lkID8gdGhpcy4kcm91dGUucXVlcnkuZ29vZHNJZCA6ICcwJwogICAgdGhpcy5nZXRHb29kc0luZm8oZ29vZHNJZCk7CgogICAgLy8g5YWI5Y+q5Yqg6L295Z+656GA5L+h5oGv77yM562J57G75Z6L56Gu6K6k5ZCO5YaN5Yqg6L295aWX6aSQ5pWw5o2uCiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVUYWJDbGljaygpIHsKICAgICAgIC8vIGVtcHR5CiAgICB9LAoKICAgIC8vIOWkhOeQhuWVhuWTgeexu+Wei+WPmOWMlgogICAgaGFuZGxlVHlwZUNoYW5nZSh2YWx1ZSkgewogICAgICAvLyDlpoLmnpzkuI3mmK/lpZfppJDllYblk4HvvIzmuIXnqbrlpZfppJDmlbDmja4KICAgICAgaWYgKHZhbHVlICE9PSAncGFja2FnZScpIHsKICAgICAgICB0aGlzLnBhY2thZ2VEYXRhID0gW107CiAgICAgIH0gZWxzZSBpZiAodGhpcy5iYXNlRm9ybS5nb29kc0lkICYmIHRoaXMuYmFzZUZvcm0uZ29vZHNJZCAhPT0gJzAnKSB7CiAgICAgICAgLy8g5aaC5p6c5piv5aWX6aSQ5ZWG5ZOB77yM5Yqg6L295aWX6aSQ5pWw5o2uCiAgICAgICAgdGhpcy5nZXRQYWNrYWdlSXRlbXModGhpcy5iYXNlRm9ybS5nb29kc0lkKTsKICAgICAgfQogICAgfSwKICAgIC8vIOiOt+WPluWll+mkkOWVhuWTgemhueebrgogICAgZ2V0UGFja2FnZUl0ZW1zKGdvb2RzSWQpIHsKICAgICAgaWYgKGdvb2RzSWQgJiYgZ29vZHNJZCAhPT0gJzAnKSB7CiAgICAgICAgZ2V0UGFja2FnZUl0ZW1zKGdvb2RzSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5ncm91cHMpIHsKICAgICAgICAgICAgdGhpcy5wYWNrYWdlRGF0YSA9IHJlc3BvbnNlLmRhdGEuZ3JvdXBzOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAoKICAgIC8vIOWkhOeQhuWll+mkkOaVsOaNruWPmOWMlgogICAgaGFuZGxlUGFja2FnZUNoYW5nZShwYWNrYWdlSXRlbXMpIHsKICAgICAgdGhpcy5wYWNrYWdlRGF0YSA9IHBhY2thZ2VJdGVtczsKICAgIH0sCgogICAgZ2V0R29vZHNJbmZvKGdvb2RzSWQpIHsKICAgICAgY29uc3QgYXBwID0gdGhpczsKICAgICAgZ2V0R29vZHNJbmZvKGdvb2RzSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgY29uc3QgZ29vZHNJbmZvID0gcmVzcG9uc2UuZGF0YS5nb29kczsKICAgICAgICAgIGNvbnN0IGltYWdlUGF0aCA9IHJlc3BvbnNlLmRhdGEuaW1hZ2VQYXRoOwogICAgICAgICAgdGhpcy51cGxvYWREb21haW4gPSBpbWFnZVBhdGg7CiAgICAgICAgICBpZiAoZ29vZHNJbmZvKSB7CiAgICAgICAgICAgICAgLy8g5ZWG5ZOB5Z+656GA5L+h5oGvCiAgICAgICAgICAgICAgdGhpcy5iYXNlRm9ybS5nb29kc0lkID0gZ29vZHNJbmZvLmlkKycnOwogICAgICAgICAgICAgIHRoaXMuYmFzZUZvcm0udHlwZSA9IGdvb2RzSW5mby50eXBlOwogICAgICAgICAgICAgIHRoaXMuYmFzZUZvcm0ubmFtZSA9IGdvb2RzSW5mby5uYW1lOwogICAgICAgICAgICAgIHRoaXMuYmFzZUZvcm0uZ29vZHNObyA9IGdvb2RzSW5mby5nb29kc05vOwogICAgICAgICAgICAgIHRoaXMuYmFzZUZvcm0uY2F0ZUlkID0gZ29vZHNJbmZvLmNhdGVJZDsKICAgICAgICAgICAgICB0aGlzLmJhc2VGb3JtLnN0b3JlSWQgPSBnb29kc0luZm8uc3RvcmVJZDsKICAgICAgICAgICAgICB0aGlzLmJhc2VGb3JtLnNvcnQgPSBnb29kc0luZm8uc29ydDsKICAgICAgICAgICAgICB0aGlzLmJhc2VGb3JtLnN0YXR1cyA9IGdvb2RzSW5mby5zdGF0dXM7CgogICAgICAgICAgICAgIC8vIOWmguaenOaYr+Wll+mkkOWVhuWTge+8jOiOt+WPluWll+mkkOaVsOaNrgogICAgICAgICAgICAgIGlmIChnb29kc0luZm8udHlwZSA9PT0gJ3BhY2thZ2UnICYmIGdvb2RzSW5mby5pZCkgewogICAgICAgICAgICAgICAgICB0aGlzLmdldFBhY2thZ2VJdGVtcyhnb29kc0luZm8uaWQpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAvLyDllYblk4Hlm77niYcKICAgICAgICAgICAgICB0aGlzLmJhc2VGb3JtLmltYWdlcyA9IHJlc3BvbnNlLmRhdGEuaW1hZ2VzOwogICAgICAgICAgICAgIGNvbnN0IGltYWdlcyA9IHRoaXMuYmFzZUZvcm0uaW1hZ2VzOwogICAgICAgICAgICAgIGFwcC51cGxvYWRGaWxlcyA9IFtdOwogICAgICAgICAgICAgIGlmIChpbWFnZXMgJiYgaW1hZ2VzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICAgICAgaW1hZ2VzLmZvckVhY2goZnVuY3Rpb24odXJsKXsKICAgICAgICAgICAgICAgICAgICAgYXBwLnVwbG9hZEZpbGVzLnB1c2goeyB1cmw6IGltYWdlUGF0aCArIHVybCB9KQogICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgLy8g5omp5bGV5L+h5oGvCiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLmdvb2RzSWQgPSBnb29kc0luZm8uaWQ7CiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLmNhblVzZVBvaW50ID0gZ29vZHNJbmZvLmNhblVzZVBvaW50ID09IG51bGwgPyAnWScgOiBnb29kc0luZm8uY2FuVXNlUG9pbnQ7CiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLmlzTWVtYmVyRGlzY291bnQgPSBnb29kc0luZm8uaXNNZW1iZXJEaXNjb3VudCA9PSBudWxsID8gJ1knIDogZ29vZHNJbmZvLmlzTWVtYmVyRGlzY291bnQ7CiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLmlzU2luZ2xlU3BlYyA9IGdvb2RzSW5mby5pc1NpbmdsZVNwZWMgPT0gbnVsbCA/ICdZJyA6IGdvb2RzSW5mby5pc1NpbmdsZVNwZWM7CiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLnN0b2NrID0gZ29vZHNJbmZvLnN0b2NrOwogICAgICAgICAgICAgIHRoaXMuZXh0ZW5kRm9ybS5wcmljZSA9IGdvb2RzSW5mby5wcmljZTsKICAgICAgICAgICAgICB0aGlzLmV4dGVuZEZvcm0uZ3JhZGVQcmljZSA9IGdvb2RzSW5mby5ncmFkZVByaWNlID8/IGdvb2RzSW5mby5wcmljZTsKICAgICAgICAgICAgICB0aGlzLmV4dGVuZEZvcm0ubGluZVByaWNlID0gZ29vZHNJbmZvLmxpbmVQcmljZTsKICAgICAgICAgICAgICB0aGlzLmV4dGVuZEZvcm0uaW5pdFNhbGUgPSBnb29kc0luZm8uaW5pdFNhbGU7CiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLnNhbGVQb2ludCA9IGdvb2RzSW5mby5zYWxlUG9pbnQ7CiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLmRyaW5rTWFrZXIgPSBnb29kc0luZm8uZHJpbmtNYWtlcjsKICAgICAgICAgICAgICB0aGlzLmV4dGVuZEZvcm0ubWFrZUNvZGUgPSBnb29kc0luZm8ubWFrZUNvZGU7CiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLndlaWdodCA9IGdvb2RzSW5mby53ZWlnaHQ7CiAgICAgICAgICAgICAgdGhpcy5leHRlbmRGb3JtLnNlcnZpY2VUaW1lID0gZ29vZHNJbmZvLnNlcnZpY2VUaW1lOwogICAgICAgICAgICAgIHRoaXMuZXh0ZW5kRm9ybS5jb3Vwb25JZHMgPSBnb29kc0luZm8uY291cG9uSWRzOwogICAgICAgICAgICAgIHRoaXMuZXh0ZW5kRm9ybS5wcm9kdWNlQ29kZSA9IGdvb2RzSW5mby5wcm9kdWNlQ29kZTsKCiAgICAgICAgICAgICAgLy8g5aSa6KeE5qC8CiAgICAgICAgICAgICAgdGhpcy5za3VEYXRhLmF0dHJMaXN0ID0gcmVzcG9uc2UuZGF0YS5zcGVjRGF0YTsKICAgICAgICAgICAgICB0aGlzLnNrdURhdGEuc2t1TGlzdCA9IHJlc3BvbnNlLmRhdGEuc2t1RGF0YTsKICAgICAgICAgICAgICB0aGlzLnNrdURhdGEuaW5pdFNrdUxpc3QgPSByZXNwb25zZS5kYXRhLnNrdURhdGE7CgogICAgICAgICAgICAgIC8vIOWVhuWTgeivpuaDhQogICAgICAgICAgICAgIHRoaXMuZGV0YWlsRm9ybS5nb29kc0lkID0gZ29vZHNJbmZvLmlkOwogICAgICAgICAgICAgIHRoaXMuZGV0YWlsRm9ybS5kZXNjcmlwdGlvbiA9IGdvb2RzSW5mby5kZXNjcmlwdGlvbjsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuY2F0ZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhLmNhdGVMaXN0OwogICAgICAgICAgdGhpcy5zdG9yZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhLnN0b3JlTGlzdDsKICAgICAgICAgIHRoaXMudHlwZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhLnR5cGVMaXN0OwogICAgICB9KTsKICAgIH0sCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCd0YWdzVmlldy9kZWxWaWV3JywgdGhpcy4kcm91dGUpCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCB7IHBhdGg6ICcvZ29vZHMvZ29vZHMvaW5kZXgnIH0gKTsKICAgIH0sCiAgICBza3VDaGFuZ2Uoc2t1RGF0YSkgewogICAgICAgLy8gZW1wdHkKICAgIH0sCiAgICAvLyDmj5DkuqTmjInpkq4KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uKCkgewogICAgICBjb25zdCBhcHAgPSB0aGlzOwogICAgICBpZiAoYXBwLmFjdGl2ZVRhYiA9PSAnYmFzZScpIHsKICAgICAgICAgIGFwcC4kcmVmc1siYmFzZUZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgIHNhdmVHb29kcyhhcHAuYmFzZUZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgICAgICBhcHAuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/neWtmOaIkOWKn++8gSIpOwogICAgICAgICAgICAgICAgICBhcHAuZ2V0R29vZHNJbmZvKHJlc3BvbnNlLmRhdGEuZ29vZHNJbmZvLmlkKTsKCiAgICAgICAgICAgICAgICAgIC8vIOaPkOekuueUqOaIt+WfuuehgOS/oeaBr+W3suS/neWtmAogICAgICAgICAgICAgICAgICBhcHAuJG1lc3NhZ2UuaW5mbygn5ZWG5ZOB5Z+656GA5L+h5oGv5bey5L+d5a2YJykKICAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOWfuuehgOS/oeaBr+Wksei0pTonLCBlcnJvcikKICAgICAgICAgICAgICAgICBhcHAuJG1vZGFsLm1zZ0Vycm9yKCLkv53lrZjlpLHotKU6ICIgKyAoZXJyb3IubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpOwogICAgICAgICAgICAgICB9KTsKICAgICAgICAgICB9CiAgICAgICAgIH0pOwogICAgICB9IGVsc2UgaWYgKGFwcC5hY3RpdmVUYWIgPT0gJ2V4dGVuZCcpIHsKICAgICAgICAgIGlmICghYXBwLmV4dGVuZEZvcm0uZ29vZHNJZCkgewogICAgICAgICAgICAgIGFwcC4kbW9kYWwubXNnRXJyb3IoIuivt+WFiOaPkOS6pOWVhuWTgeWfuuehgOS/oeaBryIpOwogICAgICAgICAgICAgIGFwcC5hY3RpdmVUYWIgPSAnYmFzZSc7CiAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgICAgLy8g5aSa6KeE5qC85ZWG5ZOB6aqM6K+BCiAgICAgICAgICBpZiAoYXBwLnNrdURhdGEuc2t1TGlzdCAmJiBhcHAuc2t1RGF0YS5za3VMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBsZXQgaXNWYWxpZDAgPSB0cnVlOwogICAgICAgICAgICAgIGxldCBpc1ZhbGlkMSA9IHRydWU7CiAgICAgICAgICAgICAgbGV0IGlzVmFsaWQyID0gdHJ1ZTsKICAgICAgICAgICAgICBhcHAuc2t1RGF0YS5za3VMaXN0LmZvckVhY2goZnVuY3Rpb24oaXRlbSkgewogICAgICAgICAgICAgICAgIGlmICghaXRlbS5za3VObyB8fCBpdGVtLnNrdU5vLmxlbmd0aCA8IDEgKSB7CiAgICAgICAgICAgICAgICAgICAgIGlzVmFsaWQwID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgIGlmIChpdGVtLnN0b2NrIDwgMCkgewogICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkMSA9IGZhbHNlOwogICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICBpZiAoaXRlbS5wcmljZSA8IDApIHsKICAgICAgICAgICAgICAgICAgICAgaXNWYWxpZDIgPSBmYWxzZTsKICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICBpZiAoIWlzVmFsaWQxKSB7CiAgICAgICAgICAgICAgICAgIGFwcC4kbW9kYWwuYWxlcnQoIuWVhuWTgXNrdee8lueggemVv+W6pumcgOWkp+S6jjHvvIzor7fku5Tnu4bmoLjlr7nvvIEiKTsKICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAoIWlzVmFsaWQxKSB7CiAgICAgICAgICAgICAgICAgIGFwcC4kbW9kYWwuYWxlcnQoIuWVhuWTgeW6k+WtmOmhu+Wkp+S6juetieS6jjDvvIzor7fku5Tnu4bmoLjlr7nvvIEiKTsKICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAoIWlzVmFsaWQyKSB7CiAgICAgICAgICAgICAgICAgIGFwcC4kbW9kYWwuYWxlcnQoIuWVhuWTgeS7t+agvOmhu+Wkp+S6juetieS6jjDvvIzor7fku5Tnu4bmoLjlr7nvvIEiKTsKICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICBhcHAuZXh0ZW5kRm9ybS5za3VEYXRhID0gYXBwLnNrdURhdGEuc2t1TGlzdDsKICAgICAgICAgIGFwcC5leHRlbmRGb3JtLnNwZWNEYXRhID0gYXBwLnNrdURhdGEuYXR0ckxpc3Q7CgogICAgICAgICAgLy8g5L+d5a2Y5aWX6aSQ5L+h5oGvCiAgICAgICAgICBpZiAoYXBwLmJhc2VGb3JtLnR5cGUgPT09ICdwYWNrYWdlJyAmJiBhcHAuJHJlZnMucGFja2FnZUl0ZW0pIHsKICAgICAgICAgICAgY29uc3QgcGFja2FnZUdyb3VwcyA9IGFwcC4kcmVmcy5wYWNrYWdlSXRlbS5nZXRQYWNrYWdlRGF0YSgpOwogICAgICAgICAgICBpZiAocGFja2FnZUdyb3Vwcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgICAgICBhcHAuJG1vZGFsLm1zZ0Vycm9yKCLor7fmt7vliqDlpZfppJDliIbnu4QiKTsKICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOWFiOS/neWtmOWfuuacrOS/oeaBrwogICAgICAgICAgICBhcHAuJHJlZnNbImV4dGVuZEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgICBzYXZlR29vZHMoYXBwLmV4dGVuZEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgICAgICBjb25zdCBnb29kc0lkID0gcmVzcG9uc2UuZGF0YS5nb29kc0luZm8uaWQ7CiAgICAgICAgICAgICAgICAgIC8vIOWGjeS/neWtmOWll+mkkOmhueebrgogICAgICAgICAgICAgICAgICBzYXZlUGFja2FnZUl0ZW1zKHsKICAgICAgICAgICAgICAgICAgICBnb29kc0lkOiBnb29kc0lkLAogICAgICAgICAgICAgICAgICAgIGdyb3VwczogcGFja2FnZUdyb3VwcwogICAgICAgICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICBhcHAuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/neWtmOaIkOWKn++8gSIpOwogICAgICAgICAgICAgICAgICAgIGFwcC5nZXRHb29kc0luZm8oZ29vZHNJZCk7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOaZrumAmuWVhuWTgeS/neWtmAogICAgICAgICAgICBhcHAuJHJlZnNbImV4dGVuZEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgICBzYXZlR29vZHMoYXBwLmV4dGVuZEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgICAgIGFwcC4kbW9kYWwubXNnU3VjY2Vzcygi5L+d5a2Y5oiQ5Yqf77yBIik7CiAgICAgICAgICAgICAgICAgYXBwLmdldEdvb2RzSW5mbyhyZXNwb25zZS5kYXRhLmdvb2RzSW5mby5pZCk7CgogICAgICAgICAgICAgICAgIC8vIOaPkOekuueUqOaIt+aJqeWxleS/oeaBr+W3suS/neWtmAogICAgICAgICAgICAgICAgIGFwcC4kbWVzc2FnZS5pbmZvKCfllYblk4HmianlsZXkv6Hmga/lt7Lkv53lrZgnKQogICAgICAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5omp5bGV5L+h5oGv5aSx6LSlOicsIGVycm9yKQogICAgICAgICAgICAgICAgIGFwcC4kbW9kYWwubXNnRXJyb3IoIuS/neWtmOWksei0pTogIiArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSk7CiAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgICBpZiAoIWFwcC5kZXRhaWxGb3JtLmdvb2RzSWQpIHsKICAgICAgICAgICAgICBhcHAuJG1vZGFsLm1zZ0Vycm9yKCLor7flhYjmj5DkuqTllYblk4Hln7rnoYDkv6Hmga8iKTsKICAgICAgICAgICAgICBhcHAuYWN0aXZlVGFiID0gJ2Jhc2UnOwogICAgICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICAgIGFwcC4kcmVmc1siZGV0YWlsRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICAgICAgICAgc2F2ZUdvb2RzKGFwcC5kZXRhaWxGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICAgICAgICAgIGFwcC4kbW9kYWwubXNnU3VjY2Vzcygi5L+d5a2Y5oiQ5Yqf77yBIik7CiAgICAgICAgICAgICAgICAgICAgICBhcHAuZ2V0R29vZHNJbmZvKHJlc3BvbnNlLmRhdGEuZ29vZHNJbmZvLmlkKTsKCiAgICAgICAgICAgICAgICAgICAgICAvLyDmj5DnpLrnlKjmiLfor6bmg4Xkv6Hmga/lt7Lkv53lrZgKICAgICAgICAgICAgICAgICAgICAgIGFwcC4kbWVzc2FnZS5pbmZvKCfllYblk4Hor6bmg4Xkv6Hmga/lt7Lkv53lrZgnKQogICAgICAgICAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y6K+m5oOF5L+h5oGv5aSx6LSlOicsIGVycm9yKQogICAgICAgICAgICAgICAgICAgIGFwcC4kbW9kYWwubXNnRXJyb3IoIuS/neWtmOWksei0pTogIiArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSk7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5YqfCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKGZpbGUpIHsKICAgICAgdGhpcy5iYXNlRm9ybS5pbWFnZXMucHVzaChmaWxlLmRhdGEuZmlsZU5hbWUpCiAgICB9LAogICAgLy8g5paH5Lu25Yig6Zmk5aSE55CGCiAgICBoYW5kbGVSZW1vdmUoZmlsZSkgewogICAgICBjb25zdCBuZXdJbWFnZXMgPSBbXTsKICAgICAgaWYgKHRoaXMuYmFzZUZvcm0uaW1hZ2VzICYmIHRoaXMuYmFzZUZvcm0uaW1hZ2VzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMuYmFzZUZvcm0uaW1hZ2VzLmZvckVhY2goZnVuY3Rpb24oaXRlbSl7CiAgICAgICAgICAgICAgaWYgKGZpbGUudXJsLmluZGV4T2YoaXRlbSkgPT0gLTEpIHsKICAgICAgICAgICAgICAgICAgbmV3SW1hZ2VzLnB1c2goaXRlbSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgfQogICAgICB0aGlzLmJhc2VGb3JtLmltYWdlcyA9IG5ld0ltYWdlczsKICAgIH0sCiAgICAvLyDnlJ/miJDpmo/mnLrmnaHnoIEKICAgIGNyZWF0ZUdvb2RzU24oKSB7CiAgICAgIGxldCBzbiA9IChNYXRoLnJhbmRvbSgpICsgMSkgKiAxMDAwMDAwMDAwMDAwMDA7CiAgICAgIHRoaXMuYmFzZUZvcm0uZ29vZHNObyA9IHNuLnRvRml4ZWQoMCk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["goodsForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+TA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "goodsForm.vue", "sourceRoot": "src/views/goods/goods", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"main-panel\">\n      <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n        <el-tab-pane label=\"基础信息\" name=\"base\">\n          <div class=\"content\">\n            <el-form ref=\"baseForm\" :model=\"baseForm\" :rules=\"baseRules\" label-width=\"120px\">\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品类型\" prop=\"cateId\">\n                    <el-select class=\"input\" v-model=\"baseForm.type\" placeholder=\"请选择商品类型\" @change=\"handleTypeChange\">\n                      <el-option\n                        v-for=\"item in typeOptions\"\n                        :key=\"item.key\"\n                        :label=\"item.name\"\n                        :value=\"item.key\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品名称\" prop=\"name\">\n                    <el-input class=\"input\" v-model=\"baseForm.name\" placeholder=\"请输入商品名称\" maxlength=\"200\" />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品条码\" prop=\"goodsNo\">\n                    <el-input class=\"input\" v-model=\"baseForm.goodsNo\" placeholder=\"请输入商品条码，或使用扫码枪扫描\" maxlength=\"50\"/>\n                    <div class=\"create-sn\" @click=\"createGoodsSn()\">随机生成条码</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品分类\" prop=\"cateId\">\n                    <el-select class=\"input\" v-model=\"baseForm.cateId\" placeholder=\"请选择商品分类\">\n                      <el-option\n                        v-for=\"item in cateOptions\"\n                        :key=\"item.id\"\n                        :label=\"item.name\"\n                        :value=\"item.id\"\n                        :disabled=\"item.status !== 'A'\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属店铺\" prop=\"storeId\">\n                    <el-select class=\"input\" v-model=\"baseForm.storeId\" clearable placeholder=\"请选择所属店铺\">\n                      <el-option :key=\"0\" label=\"公共商品\" v-if=\"storeId == 0\" :value=\"0\"/>\n                      <el-option\n                        v-for=\"item in storeOptions\"\n                        :key=\"item.id\"\n                        :label=\"item.name\"\n                        :value=\"item.id\"\n                        :disabled=\"item.status !== 'A'\"\n                      ></el-option>\n                    </el-select>\n                    <div class=\"form-tips\">提示：未选择则属于公共商品</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品图片\" prop=\"images\">\n                    <el-upload class=\"form__head-icon-upload\"\n                               :action=\"uploadAction\"\n                               list-type=\"picture-card\"\n                               :file-list=\"uploadFiles\"\n                               :limit=\"10\"\n                               :auto-upload=\"true\"\n                               :headers=\"uploadHeader\"\n                               :on-success=\"handleUploadSuccess\"\n                               :on-remove=\"handleRemove\">\n                      <i class=\"el-icon-plus\"></i>\n                    </el-upload>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"显示排序\" prop=\"sort\">\n                    <el-input-number v-model=\"baseForm.sort\" :min=\"0\" />\n                    <div class=\"form-tips\">提示：数值越小，排行越靠前</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品状态\">\n                    <el-radio-group v-model=\"baseForm.status\">\n                      <el-radio key=\"A\" label=\"A\" value=\"A\">上架</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">下架</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"扩展信息\" name=\"extend\">\n          <div class=\"content\">\n            <el-form ref=\"extendForm\" :model=\"extendForm\" :rules=\"extendRules\" label-width=\"120px\">\n              <!-- <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"积分抵扣\" prop=\"canUsePoint\">\n                    <el-radio-group v-model=\"extendForm.canUsePoint\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">可用</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">不可用</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品卖点\" prop=\"salePoint\">\n                    <el-input class=\"input\" v-model=\"extendForm.salePoint\" placeholder=\"请输入商品卖点，几个字总结\" maxlength=\"50\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"baseForm.type == 'goods'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"饮料制作机\" prop=\"drinkMaker\">\n                    <el-select\n                      v-model=\"extendForm.drinkMaker\"\n                      placeholder=\"请选择饮料制作机\"\n                      clearable\n                    >\n                      <el-option label=\"21路\" value=\"21\"/>\n                      <el-option label=\"21+BLENDER\" value=\"21+BLENDER\"/>\n                      <el-option label=\"21+SODA\" value=\"21+SODA\"/>\n                      <el-option label=\"21+NESPRESSO\" value=\"21+NESPRESSO\"/>\n                      <el-option label=\"NESPRESSO\" value=\"NESPRESSO\"/>\n                      <el-option label=\"21+ICECREAM\" value=\"21+ICECREAM\"/>\n                      <el-option label=\"ICECREAM+NESPRESSO\" value=\"ICECREAM+NESPRESSO\"/>\n                      <el-option label=\"ICECREAM\" value=\"ICECREAM\"/>\n                      <el-option label=\"8路设备\" value=\"8\"/>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n              <el-row  v-if=\"baseForm.type == 'goods'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品制作编码\" prop=\"makeCode\">\n                    <el-input class=\"input\" v-model=\"extendForm.makeCode\" placeholder=\"请输入商品制作编码\" maxlength=\"5\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"会员折扣\" prop=\"isMemberDiscount\">\n                    <el-radio-group v-model=\"extendForm.isMemberDiscount\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">有折扣</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">无折扣</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row  v-if=\"baseForm.type != 'package'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"规格类型\" prop=\"isSingleSpec\">\n                    <el-radio-group v-model=\"extendForm.isSingleSpec\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">单规格</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">多规格</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'N'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品规格\" prop=\"goodsSpec\">\n                     <Sku ref=\"Sku\" :skuData=\"skuData\" :makeCode=\"extendForm.makeCode\" :goodsId=\"baseForm.goodsId\" :uploadDomain=\"uploadDomain\" @skuChange=\"skuChange\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n\n              <el-row v-if=\"baseForm.type == 'service'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"服务时长\" prop=\"serviceTime\">\n                    <el-input v-model=\"extendForm.serviceTime\" class=\"min-input\" placeholder=\"请输入服务时长，单位：分钟\" maxlength=\"50\"/>\n                    <div class=\"form-tips\">提示：输入数字，单位：分钟</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"baseForm.type == 'coupon'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"卡券ID\" prop=\"couponIds\">\n                    <el-input v-model=\"extendForm.couponIds\" class=\"input\" rows=\"2\" type=\"textarea\" placeholder=\"请输入购买的卡券ID，英文逗号分隔，如：1000,1001,1002\" maxlength=\"1000\"/>\n                    <div class=\"form-tips\">提示：购买的卡券ID，英文逗号分隔</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y' && baseForm.type == 'goods' \">\n                <el-col :span=\"24\">\n                  <el-form-item  prop=\"produceCode\">\n                    <template slot=\"label\">\n                      餐品制作码\n                      <el-tooltip placement=\"top\" effect=\"light\">\n                        <template slot=\"content\">\n                          <img src='@/assets/images/cup-qrcode-demo.jpg' style='width: 800px;'>\n                        </template>\n                        <i class=\"el-icon-info\"></i>\n                      </el-tooltip>\n                    </template>\n                    <el-input :trim=\"true\" v-model=\"extendForm.produceCode\" class=\"min-input\" placeholder=\"请输入餐品制作码\" maxlength=\"20\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"库存数量\" prop=\"stock\">\n                    <el-input v-model=\"extendForm.stock\" class=\"min-input\" placeholder=\"请输入库存数量\" maxlength=\"50\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品价格\" prop=\"price\">\n                    <el-input v-model=\"extendForm.price\" class=\"min-input\" placeholder=\"请输入商品价格\" maxlength=\"50\"/>\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"会员专属价格\" prop=\"gradePrice\">\n                    <el-input v-model=\"extendForm.gradePrice\" class=\"min-input\" placeholder=\"请输入会员专属价格\" maxlength=\"50\" />\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n               <el-row v-if=\"baseForm.type == 'package'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"套餐商品\" prop=\"packageItems\">\n                    <PackageItemNew\n                      ref=\"packageItem\"\n                      :goodsId=\"baseForm.goodsId\"\n                      :uploadDomain=\"uploadDomain\"\n                      :typeOptions=\"typeOptions\"\n                      :packageData=\"packageData\"\n                      @change=\"handlePackageChange\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <!-- <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"划线价格\" prop=\"linePrice\">\n                    <el-input v-model=\"extendForm.linePrice\" class=\"min-input\" placeholder=\"请输入商品划线，空则不显示\" maxlength=\"50\" />\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n              <!-- <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"初始销量\" prop=\"initSale\">\n                    <el-input v-model=\"extendForm.initSale\" class=\"min-input\" placeholder=\"请输入初始销量\" maxlength=\"10\"/>\n                    <div class=\"form-tips\">提示：输入数字，虚拟销量</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n              <!-- <el-row v-if=\"extendForm.isSingleSpec == 'Y' && baseForm.type == 'product'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品重量\" prop=\"weight\">\n                    <el-input v-model=\"extendForm.weight\" class=\"min-input\" placeholder=\"请输入商品重量\" maxlength=\"10\"/>\n                    <div class=\"form-tips\">提示：输入数字，单位kg</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"商品介绍\" name=\"detail\">\n          <div class=\"content\" style=\"width: 375px;margin-left: 80px;\">\n            <el-form ref=\"detailForm\" :model=\"detailForm\" :rules=\"detailRules\" label-width=\"120px\">\n               <editor v-model=\"detailForm.description\" :min-height=\"550\"/>\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"时间段配置\" name=\"timeConfig\" v-if=\"baseForm.goodsId && baseForm.goodsId !== '0'\">\n          <div class=\"time-config-tip\">\n            <el-alert\n              title=\"提示：可以为商品设置销售时间段，如每日通用、按周设置或自定义日期。设置后，商品只在指定时间段内可售。\"\n              type=\"info\"\n              show-icon\n              :closable=\"false\"\n            />\n          </div>\n          <TimeConfigList :goods-id=\"baseForm.goodsId\" />\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"footer\">\n         <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n         <el-button @click=\"cancel\">取消</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { saveGoods, getGoodsInfo, getPackageItems, savePackageItems } from \"@/api/goods\";\nimport Sku from '../components/Sku';\nimport PackageItemNew from '../components/PackageItemNew';\nimport TimeConfigList from '../components/TimeConfigList';\nexport default {\n  name: \"GoodsForm\",\n  components: { Sku, PackageItemNew, TimeConfigList },\n  data() {\n    return {\n      storeId: this.$store.getters.storeId,\n      // 遮罩层\n      loading: false,\n      activeTab: 'base',\n      storeOptions: [],\n      cateOptions: [],\n      typeOptions: [],\n      skuData: { attrList: [], skuList: [], initSkuList: [] },\n      // 套餐商品数据\n      packageData: [],\n      // 基础信息表单\n      baseForm: { type: 'goods', goodsId: '', name: '', storeId: this.$store.getters.storeId, cateId: '', goodsNo: '', images: [], status: \"A\", sort: 0 },\n      // 扩展信息表单\n      extendForm: { goodsId: '', canUsePoint: 'Y', isMemberDiscount: 'Y', isSingleSpec: 'Y', produceCode: '', serviceTime: 0, couponIds: '', stock: '', price: '', gradePrice: '' ,linePrice: '', salePoint: '', makeCode: '', drinkMaker: '', initSale: '', weight: '', skuData: null },\n      // 详情信息表单\n      detailForm: { goodsId: '', description : '' },\n      // 上传地址\n      uploadAction: process.env.VUE_APP_SERVER_URL + 'backendApi/file/upload',\n      uploadHeader: { 'Access-Token' : getToken() },\n      // 上传域名\n      uploadDomain: '',\n      // 上传文件列表\n      uploadFiles: [],\n      // 基础信息表单校验\n      baseRules: {\n        name: [\n          { required: true, message: \"商品名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 30, message: '商品名称长度必须介于2和200 之间', trigger: 'blur' }\n        ],\n        goodsNo: [\n          { required: true, message: \"商品条码不能为空\", trigger: \"blur\" },\n          { min: 2, max: 100, message: '商品条码长度必须介于2和100之间', trigger: 'blur' }\n        ],\n        cateId: [\n          { required: true, message: \"请选择商品分类\", trigger: \"blur\" }\n        ],\n        images: [\n          { required: true, message: \"请上传商品图片\", trigger: \"blur\" }\n        ],\n      },\n      // 扩展信息表单校验\n      extendRules: {\n        couponIds: [\n          { required: true, message: \"卡券ID不能为空\", trigger: \"blur\" },\n          { min: 1, max: 1000, message: '卡券ID长度必须介于1和100之间', trigger: 'blur' }\n        ],\n        canUsePoint: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        isMemberDiscount: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        isSingleSpec: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        price: [\n          { required: true, message: \"请输入商品价格\", trigger: \"blur\" }, { pattern: /(^[1-9]\\d*(\\.\\d{1,2})?$)|(^0(\\.\\d{1,2})?$)/, message: `价格必须大于0`, trigger: 'blur' }\n        ],\n        /* produceCode: [\n          { pattern: /^=A(0[1-9]|[1-9][0-9])\\|C(0[1-9]|[1-9][0-9]),T(0[1-9]|[1-9][0-9]),W(0[1-9]|[1-9][0-9])$/, message: `输入正确的制作码`, trigger: 'input' }\n        ], */\n        stock: [\n          { required: true, message: \"请输入库存数量\", trigger: \"blur\" }, { pattern: /^[0-9]{1,10}$/, message: `库存数量必须是正整数`, trigger: 'blur' }\n        ],\n        initSale: [\n          { pattern: /^[0-9]{1,10}$/, message: `初始销量必须是正整数`, trigger: 'blur' }\n        ],\n        weight: [\n          { pattern: /(^[1-9]\\d*(\\.\\d{1,2})?$)|(^0(\\.\\d{1,2})?$)/, message: `重量必须大于等于0`, trigger: 'blur' }\n        ],\n      },\n      detailRules: {\n        description: [\n          { required: true, message: \"请输入商品详情\", trigger: \"blur\" }\n        ],\n      }\n    };\n  },\n  created() {\n    const goodsId = this.$route.query.goodsId ? this.$route.query.goodsId : '0'\n    this.getGoodsInfo(goodsId);\n\n    // 先只加载基础信息，等类型确认后再加载套餐数据\n  },\n  methods: {\n    handleTabClick() {\n       // empty\n    },\n\n    // 处理商品类型变化\n    handleTypeChange(value) {\n      // 如果不是套餐商品，清空套餐数据\n      if (value !== 'package') {\n        this.packageData = [];\n      } else if (this.baseForm.goodsId && this.baseForm.goodsId !== '0') {\n        // 如果是套餐商品，加载套餐数据\n        this.getPackageItems(this.baseForm.goodsId);\n      }\n    },\n    // 获取套餐商品项目\n    getPackageItems(goodsId) {\n      if (goodsId && goodsId !== '0') {\n        getPackageItems(goodsId).then(response => {\n          if (response.data && response.data.groups) {\n            this.packageData = response.data.groups;\n          }\n        });\n      }\n    },\n\n    // 处理套餐数据变化\n    handlePackageChange(packageItems) {\n      this.packageData = packageItems;\n    },\n\n    getGoodsInfo(goodsId) {\n      const app = this;\n      getGoodsInfo(goodsId).then(response => {\n          const goodsInfo = response.data.goods;\n          const imagePath = response.data.imagePath;\n          this.uploadDomain = imagePath;\n          if (goodsInfo) {\n              // 商品基础信息\n              this.baseForm.goodsId = goodsInfo.id+'';\n              this.baseForm.type = goodsInfo.type;\n              this.baseForm.name = goodsInfo.name;\n              this.baseForm.goodsNo = goodsInfo.goodsNo;\n              this.baseForm.cateId = goodsInfo.cateId;\n              this.baseForm.storeId = goodsInfo.storeId;\n              this.baseForm.sort = goodsInfo.sort;\n              this.baseForm.status = goodsInfo.status;\n\n              // 如果是套餐商品，获取套餐数据\n              if (goodsInfo.type === 'package' && goodsInfo.id) {\n                  this.getPackageItems(goodsInfo.id);\n              }\n              // 商品图片\n              this.baseForm.images = response.data.images;\n              const images = this.baseForm.images;\n              app.uploadFiles = [];\n              if (images && images.length > 0) {\n                  images.forEach(function(url){\n                     app.uploadFiles.push({ url: imagePath + url })\n                  })\n              }\n\n              // 扩展信息\n              this.extendForm.goodsId = goodsInfo.id;\n              this.extendForm.canUsePoint = goodsInfo.canUsePoint == null ? 'Y' : goodsInfo.canUsePoint;\n              this.extendForm.isMemberDiscount = goodsInfo.isMemberDiscount == null ? 'Y' : goodsInfo.isMemberDiscount;\n              this.extendForm.isSingleSpec = goodsInfo.isSingleSpec == null ? 'Y' : goodsInfo.isSingleSpec;\n              this.extendForm.stock = goodsInfo.stock;\n              this.extendForm.price = goodsInfo.price;\n              this.extendForm.gradePrice = goodsInfo.gradePrice ?? goodsInfo.price;\n              this.extendForm.linePrice = goodsInfo.linePrice;\n              this.extendForm.initSale = goodsInfo.initSale;\n              this.extendForm.salePoint = goodsInfo.salePoint;\n              this.extendForm.drinkMaker = goodsInfo.drinkMaker;\n              this.extendForm.makeCode = goodsInfo.makeCode;\n              this.extendForm.weight = goodsInfo.weight;\n              this.extendForm.serviceTime = goodsInfo.serviceTime;\n              this.extendForm.couponIds = goodsInfo.couponIds;\n              this.extendForm.produceCode = goodsInfo.produceCode;\n\n              // 多规格\n              this.skuData.attrList = response.data.specData;\n              this.skuData.skuList = response.data.skuData;\n              this.skuData.initSkuList = response.data.skuData;\n\n              // 商品详情\n              this.detailForm.goodsId = goodsInfo.id;\n              this.detailForm.description = goodsInfo.description;\n          }\n          this.cateOptions = response.data.cateList;\n          this.storeOptions = response.data.storeList;\n          this.typeOptions = response.data.typeList;\n      });\n    },\n    cancel() {\n      this.$store.dispatch('tagsView/delView', this.$route)\n      this.$router.push( { path: '/goods/goods/index' } );\n    },\n    skuChange(skuData) {\n       // empty\n    },\n    // 提交按钮\n    submitForm: function() {\n      const app = this;\n      if (app.activeTab == 'base') {\n          app.$refs[\"baseForm\"].validate(valid => {\n           if (valid) {\n               saveGoods(app.baseForm).then(response => {\n                  app.$modal.msgSuccess(\"保存成功！\");\n                  app.getGoodsInfo(response.data.goodsInfo.id);\n\n                  // 提示用户基础信息已保存\n                  app.$message.info('商品基础信息已保存')\n               }).catch(error => {\n                 console.error('保存基础信息失败:', error)\n                 app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n               });\n           }\n         });\n      } else if (app.activeTab == 'extend') {\n          if (!app.extendForm.goodsId) {\n              app.$modal.msgError(\"请先提交商品基础信息\");\n              app.activeTab = 'base';\n              return false;\n          }\n          // 多规格商品验证\n          if (app.skuData.skuList && app.skuData.skuList.length > 0) {\n              let isValid0 = true;\n              let isValid1 = true;\n              let isValid2 = true;\n              app.skuData.skuList.forEach(function(item) {\n                 if (!item.skuNo || item.skuNo.length < 1 ) {\n                     isValid0 = false;\n                 }\n                 if (item.stock < 0) {\n                     isValid1 = false;\n                 }\n                 if (item.price < 0) {\n                     isValid2 = false;\n                 }\n              })\n              if (!isValid1) {\n                  app.$modal.alert(\"商品sku编码长度需大于1，请仔细核对！\");\n                  return false;\n              }\n              if (!isValid1) {\n                  app.$modal.alert(\"商品库存须大于等于0，请仔细核对！\");\n                  return false;\n              }\n              if (!isValid2) {\n                  app.$modal.alert(\"商品价格须大于等于0，请仔细核对！\");\n                  return false;\n              }\n          }\n\n          app.extendForm.skuData = app.skuData.skuList;\n          app.extendForm.specData = app.skuData.attrList;\n\n          // 保存套餐信息\n          if (app.baseForm.type === 'package' && app.$refs.packageItem) {\n            const packageGroups = app.$refs.packageItem.getPackageData();\n            if (packageGroups.length === 0) {\n              app.$modal.msgError(\"请添加套餐分组\");\n              return false;\n            }\n\n            // 先保存基本信息\n            app.$refs[\"extendForm\"].validate(valid => {\n              if (valid) {\n                saveGoods(app.extendForm).then(response => {\n                  const goodsId = response.data.goodsInfo.id;\n                  // 再保存套餐项目\n                  savePackageItems({\n                    goodsId: goodsId,\n                    groups: packageGroups\n                  }).then(() => {\n                    app.$modal.msgSuccess(\"保存成功！\");\n                    app.getGoodsInfo(goodsId);\n                  });\n                });\n              }\n            });\n          } else {\n            // 普通商品保存\n            app.$refs[\"extendForm\"].validate(valid => {\n              if (valid) {\n                saveGoods(app.extendForm).then(response => {\n                 app.$modal.msgSuccess(\"保存成功！\");\n                 app.getGoodsInfo(response.data.goodsInfo.id);\n\n                 // 提示用户扩展信息已保存\n                 app.$message.info('商品扩展信息已保存')\n               }).catch(error => {\n                 console.error('保存扩展信息失败:', error)\n                 app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n               });\n              }\n            });\n          }\n      } else {\n          if (!app.detailForm.goodsId) {\n              app.$modal.msgError(\"请先提交商品基础信息\");\n              app.activeTab = 'base';\n              return false;\n          }\n          app.$refs[\"detailForm\"].validate(valid => {\n              if (valid) {\n                  saveGoods(app.detailForm).then(response => {\n                      app.$modal.msgSuccess(\"保存成功！\");\n                      app.getGoodsInfo(response.data.goodsInfo.id);\n\n                      // 提示用户详情信息已保存\n                      app.$message.info('商品详情信息已保存')\n                  }).catch(error => {\n                    console.error('保存详情信息失败:', error)\n                    app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n                  });\n              }\n          });\n      }\n    },\n    // 文件上传成功\n    handleUploadSuccess(file) {\n      this.baseForm.images.push(file.data.fileName)\n    },\n    // 文件删除处理\n    handleRemove(file) {\n      const newImages = [];\n      if (this.baseForm.images && this.baseForm.images.length > 0) {\n          this.baseForm.images.forEach(function(item){\n              if (file.url.indexOf(item) == -1) {\n                  newImages.push(item);\n              }\n          })\n      }\n      this.baseForm.images = newImages;\n    },\n    // 生成随机条码\n    createGoodsSn() {\n      let sn = (Math.random() + 1) * 100000000000000;\n      this.baseForm.goodsNo = sn.toFixed(0);\n    }\n  }\n};\n</script>\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n   .main-panel {\n      padding-top: 5px;\n      .content {\n          margin-top: 30px;\n          margin-left: 20px;\n      }\n     .footer {\n        margin-top: 20px;\n     }\n     .create-sn {\n        font-size: 12px;\n        color: blue;\n        cursor: pointer;\n        width: 100px;\n     }\n     .time-config-tip {\n       padding: 10px 20px;\n     }\n   }\n</style>\n"]}]}