-------------------------------------------------------------------------------
Test set: com.fuint.common.service.PrinterCateServiceTest
-------------------------------------------------------------------------------
Tests run: 7, Failures: 0, Errors: 4, Skipped: 0, Time elapsed: 0.45 s <<< FAILURE! - in com.fuint.common.service.PrinterCateServiceTest
testAddPrinterCate  Time elapsed: 0.001 s  <<< ERROR!
java.lang.NullPointerException
	at com.fuint.common.service.PrinterCateServiceTest.testAddPrinterCate(PrinterCateServiceTest.java:83)

testDeleteByPrinterId  Time elapsed: 0.126 s  <<< ERROR!
org.mockito.exceptions.misusing.InvalidUseOfMatchersException: 

Misplaced or misused argument matcher detected here:

-> at com.fuint.common.service.PrinterCateServiceTest.testAddPrinterCate(PrinterCateServiceTest.java:83)

You cannot use argument matchers outside of verification or stubbing.
Examples of correct usage of argument matchers:
    when(mock.get(anyInt())).thenReturn(null);
    doThrow(new RuntimeException()).when(mock).someVoidMethod(anyObject());
    verify(mock).someMethod(contains("foo"))

This message may appear after an NullPointerException if the last matcher is returning an object 
like any() but the stubbed method signature expect a primitive argument, in this case,
use primitive alternatives.
    when(mock.get(any())); // bad use, will raise NPE
    when(mock.get(anyInt())); // correct usage use

Also, this error might show up because you use argument matchers with methods that cannot be mocked.
Following methods *cannot* be stubbed/verified: final/private/equals()/hashCode().
Mocking methods declared on non-public parent classes is not supported.


testSavePrinterCateRelations  Time elapsed: 0.001 s  <<< ERROR!
java.lang.NullPointerException
	at com.fuint.common.service.PrinterCateServiceTest.testSavePrinterCateRelations(PrinterCateServiceTest.java:166)

testDeleteByCateId  Time elapsed: 0.001 s  <<< ERROR!
org.mockito.exceptions.misusing.InvalidUseOfMatchersException: 

Misplaced or misused argument matcher detected here:

-> at com.fuint.common.service.PrinterCateServiceTest.testSavePrinterCateRelations(PrinterCateServiceTest.java:166)

You cannot use argument matchers outside of verification or stubbing.
Examples of correct usage of argument matchers:
    when(mock.get(anyInt())).thenReturn(null);
    doThrow(new RuntimeException()).when(mock).someVoidMethod(anyObject());
    verify(mock).someMethod(contains("foo"))

This message may appear after an NullPointerException if the last matcher is returning an object 
like any() but the stubbed method signature expect a primitive argument, in this case,
use primitive alternatives.
    when(mock.get(any())); // bad use, will raise NPE
    when(mock.get(anyInt())); // correct usage use

Also, this error might show up because you use argument matchers with methods that cannot be mocked.
Following methods *cannot* be stubbed/verified: final/private/equals()/hashCode().
Mocking methods declared on non-public parent classes is not supported.


