{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\api\\printer.js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\api\\printer.js", "mtime": 1754917591590}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\eslint-loader\\index.js", "mtime": 1734093918652}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "getPrinterList", "query", "url", "method", "params", "getPrinterInfo", "printerId", "updatePrinterStatus", "id", "status", "data", "savePrinter", "saveSetting", "getSettingInfo", "doPrint", "orderId", "printExpiryLabel", "getCategoryList", "savePrinterCategories", "getPrinterCategories", "concat"], "sources": ["D:/workspace/fuintFoodSystem/fuintAdmin/src/api/printer.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 分页查询打印机列表\nexport function getPrinterList(query) {\n  return request({\n      url: 'backendApi/printer/list',\n      method: 'get',\n      params: query\n  })\n}\n\n// 查询打印机信息\nexport function getPrinterInfo(printerId) {\n  return request({\n    url: 'backendApi/printer/info/' + printerId,\n    method: 'get'\n  })\n}\n\n// 更新打印机状态\nexport function updatePrinterStatus(id, status) {\n  const data = {\n    id,\n    status\n  }\n  return request({\n      url: 'backendApi/printer/updateStatus',\n      method: 'post',\n      data: data\n  })\n}\n\n// 保存打印机\nexport function savePrinter(data) {\n  return request({\n    url: 'backendApi/printer/save',\n    method: 'post',\n    data: data\n  })\n}\n\n// 保存打印设置\nexport function saveSetting(data) {\n  return request({\n    url: 'backendApi/printer/saveSetting',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取打印配置\nexport function getSettingInfo() {\n  return request({\n    url: 'backendApi/printer/setting',\n    method: 'get'\n  })\n}\n// 打印订单信息\nexport function doPrint(orderId) {\n  return request({\n    url: 'backendApi/printer/doPrint/' + orderId,\n    method: 'get'\n  })\n}\n\n// 打印标签\nexport function printExpiryLabel(data) {\n  return request({\n    url: 'backendApi/printer/printExpiryLabel',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取商品分类列表\nexport function getCategoryList() {\n  return request({\n    url: 'backendApi/printer/categories',\n    method: 'get'\n  })\n}\n\n// 保存打印机分类关联\nexport function savePrinterCategories(data) {\n  return request({\n    url: 'backendApi/printer/savePrinterCategories',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取打印机分类关联列表\nexport function getPrinterCategories(printerId) {\n  return request({\n    url: `backendApi/printer/printerCategories/${printerId}`,\n    method: 'get'\n  })\n}\n\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAOF,OAAO,CAAC;IACXG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACZ,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,cAAcA,CAACC,SAAS,EAAE;EACxC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B,GAAGI,SAAS;IAC3CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,mBAAmBA,CAACC,EAAE,EAAEC,MAAM,EAAE;EAC9C,IAAMC,IAAI,GAAG;IACXF,EAAE,EAAFA,EAAE;IACFC,MAAM,EAANA;EACF,CAAC;EACD,OAAOV,OAAO,CAAC;IACXG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,WAAWA,CAACF,IAAI,EAAE;EAChC,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,cAAcA,CAAA,EAAG;EAC/B,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASW,OAAOA,CAACC,OAAO,EAAE;EAC/B,OAAOhB,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B,GAAGa,OAAO;IAC5CZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,gBAAgBA,CAACN,IAAI,EAAE;EACrC,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,eAAeA,CAAA,EAAG;EAChC,OAAOlB,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASe,qBAAqBA,CAACR,IAAI,EAAE;EAC1C,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,oBAAoBA,CAACb,SAAS,EAAE;EAC9C,OAAOP,OAAO,CAAC;IACbG,GAAG,0CAAAkB,MAAA,CAA0Cd,SAAS,CAAE;IACxDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}