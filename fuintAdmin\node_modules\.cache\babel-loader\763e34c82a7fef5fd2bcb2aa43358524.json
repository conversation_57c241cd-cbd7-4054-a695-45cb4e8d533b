{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\goodsForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\goodsForm.vue", "mtime": 1754840036318}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getToken", "saveGoods", "getGoodsInfo", "getPackageItems", "savePackageItems", "S<PERSON>", "PackageItemNew", "TimeConfigList", "name", "components", "data", "storeId", "$store", "getters", "loading", "activeTab", "storeOptions", "cateOptions", "typeOptions", "sku<PERSON><PERSON>", "attrList", "skuList", "initSkuList", "packageData", "baseForm", "type", "goodsId", "cateId", "goodsNo", "images", "status", "sort", "extendForm", "canUsePoint", "isMemberDiscount", "isSingleSpec", "produceCode", "serviceTime", "couponIds", "stock", "price", "gradePrice", "linePrice", "salePoint", "makeCode", "drinkMaker", "initSale", "weight", "detailForm", "description", "uploadAction", "process", "env", "VUE_APP_SERVER_URL", "uploadHeader", "uploadDomain", "uploadFiles", "baseRules", "required", "message", "trigger", "min", "max", "extendRules", "pattern", "detailRules", "created", "$route", "query", "methods", "handleTabClick", "handleTypeChange", "value", "_this", "then", "response", "groups", "handlePackageChange", "packageItems", "_this2", "app", "goodsInfo", "goods", "imagePath", "_goodsInfo$gradePrice", "id", "length", "for<PERSON>ach", "url", "push", "specData", "cateList", "storeList", "typeList", "cancel", "dispatch", "$router", "path", "skuChange", "submitForm", "$refs", "validate", "valid", "$modal", "msgSuccess", "$message", "info", "catch", "error", "console", "msgError", "isValid0", "isValid1", "isValid2", "item", "skuNo", "alert", "packageItem", "packageGroups", "getPackageData", "handleUploadSuccess", "file", "fileName", "handleRemove", "newImages", "indexOf", "createGoodsSn", "sn", "Math", "random", "toFixed"], "sources": ["src/views/goods/goods/goodsForm.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"main-panel\">\n      <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n        <el-tab-pane label=\"基础信息\" name=\"base\">\n          <div class=\"content\">\n            <el-form ref=\"baseForm\" :model=\"baseForm\" :rules=\"baseRules\" label-width=\"120px\">\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品类型\" prop=\"cateId\">\n                    <el-select class=\"input\" v-model=\"baseForm.type\" placeholder=\"请选择商品类型\" @change=\"handleTypeChange\">\n                      <el-option\n                        v-for=\"item in typeOptions\"\n                        :key=\"item.key\"\n                        :label=\"item.name\"\n                        :value=\"item.key\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品名称\" prop=\"name\">\n                    <el-input class=\"input\" v-model=\"baseForm.name\" placeholder=\"请输入商品名称\" maxlength=\"200\" />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品条码\" prop=\"goodsNo\">\n                    <el-input class=\"input\" v-model=\"baseForm.goodsNo\" placeholder=\"请输入商品条码，或使用扫码枪扫描\" maxlength=\"50\"/>\n                    <div class=\"create-sn\" @click=\"createGoodsSn()\">随机生成条码</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品分类\" prop=\"cateId\">\n                    <el-select class=\"input\" v-model=\"baseForm.cateId\" placeholder=\"请选择商品分类\">\n                      <el-option\n                        v-for=\"item in cateOptions\"\n                        :key=\"item.id\"\n                        :label=\"item.name\"\n                        :value=\"item.id\"\n                        :disabled=\"item.status !== 'A'\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属店铺\" prop=\"storeId\">\n                    <el-select class=\"input\" v-model=\"baseForm.storeId\" clearable placeholder=\"请选择所属店铺\">\n                      <el-option :key=\"0\" label=\"公共商品\" v-if=\"storeId == 0\" :value=\"0\"/>\n                      <el-option\n                        v-for=\"item in storeOptions\"\n                        :key=\"item.id\"\n                        :label=\"item.name\"\n                        :value=\"item.id\"\n                        :disabled=\"item.status !== 'A'\"\n                      ></el-option>\n                    </el-select>\n                    <div class=\"form-tips\">提示：未选择则属于公共商品</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品图片\" prop=\"images\">\n                    <el-upload class=\"form__head-icon-upload\"\n                               :action=\"uploadAction\"\n                               list-type=\"picture-card\"\n                               :file-list=\"uploadFiles\"\n                               :limit=\"10\"\n                               :auto-upload=\"true\"\n                               :headers=\"uploadHeader\"\n                               :on-success=\"handleUploadSuccess\"\n                               :on-remove=\"handleRemove\">\n                      <i class=\"el-icon-plus\"></i>\n                    </el-upload>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"显示排序\" prop=\"sort\">\n                    <el-input-number v-model=\"baseForm.sort\" :min=\"0\" />\n                    <div class=\"form-tips\">提示：数值越小，排行越靠前</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品状态\">\n                    <el-radio-group v-model=\"baseForm.status\">\n                      <el-radio key=\"A\" label=\"A\" value=\"A\">上架</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">下架</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"扩展信息\" name=\"extend\">\n          <div class=\"content\">\n            <el-form ref=\"extendForm\" :model=\"extendForm\" :rules=\"extendRules\" label-width=\"120px\">\n              <!-- <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"积分抵扣\" prop=\"canUsePoint\">\n                    <el-radio-group v-model=\"extendForm.canUsePoint\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">可用</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">不可用</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品卖点\" prop=\"salePoint\">\n                    <el-input class=\"input\" v-model=\"extendForm.salePoint\" placeholder=\"请输入商品卖点，几个字总结\" maxlength=\"50\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"baseForm.type == 'goods'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"饮料制作机\" prop=\"drinkMaker\">\n                    <el-select\n                      v-model=\"extendForm.drinkMaker\"\n                      placeholder=\"请选择饮料制作机\"\n                      clearable\n                    >\n                      <el-option label=\"21路\" value=\"21\"/>\n                      <el-option label=\"21+BLENDER\" value=\"21+BLENDER\"/>\n                      <el-option label=\"21+SODA\" value=\"21+SODA\"/>\n                      <el-option label=\"21+NESPRESSO\" value=\"21+NESPRESSO\"/>\n                      <el-option label=\"NESPRESSO\" value=\"NESPRESSO\"/>\n                      <el-option label=\"21+ICECREAM\" value=\"21+ICECREAM\"/>\n                      <el-option label=\"ICECREAM+NESPRESSO\" value=\"ICECREAM+NESPRESSO\"/>\n                      <el-option label=\"ICECREAM\" value=\"ICECREAM\"/>\n                      <el-option label=\"8路设备\" value=\"8\"/>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n              <el-row  v-if=\"baseForm.type == 'goods'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品制作编码\" prop=\"makeCode\">\n                    <el-input class=\"input\" v-model=\"extendForm.makeCode\" placeholder=\"请输入商品制作编码\" maxlength=\"5\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"会员折扣\" prop=\"isMemberDiscount\">\n                    <el-radio-group v-model=\"extendForm.isMemberDiscount\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">有折扣</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">无折扣</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row  v-if=\"baseForm.type != 'package'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"规格类型\" prop=\"isSingleSpec\">\n                    <el-radio-group v-model=\"extendForm.isSingleSpec\">\n                      <el-radio key=\"Y\" label=\"Y\" value=\"Y\">单规格</el-radio>\n                      <el-radio key=\"N\" label=\"N\" value=\"N\">多规格</el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'N'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品规格\" prop=\"goodsSpec\">\n                     <Sku ref=\"Sku\" :skuData=\"skuData\" :makeCode=\"extendForm.makeCode\" :goodsId=\"baseForm.goodsId\" :uploadDomain=\"uploadDomain\" @skuChange=\"skuChange\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n\n\n              <el-row v-if=\"baseForm.type == 'service'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"服务时长\" prop=\"serviceTime\">\n                    <el-input v-model=\"extendForm.serviceTime\" class=\"min-input\" placeholder=\"请输入服务时长，单位：分钟\" maxlength=\"50\"/>\n                    <div class=\"form-tips\">提示：输入数字，单位：分钟</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"baseForm.type == 'coupon'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"卡券ID\" prop=\"couponIds\">\n                    <el-input v-model=\"extendForm.couponIds\" class=\"input\" rows=\"2\" type=\"textarea\" placeholder=\"请输入购买的卡券ID，英文逗号分隔，如：1000,1001,1002\" maxlength=\"1000\"/>\n                    <div class=\"form-tips\">提示：购买的卡券ID，英文逗号分隔</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y' && baseForm.type == 'goods' \">\n                <el-col :span=\"24\">\n                  <el-form-item  prop=\"produceCode\">\n                    <template slot=\"label\">\n                      餐品制作码\n                      <el-tooltip placement=\"top\" effect=\"light\">\n                        <template slot=\"content\">\n                          <img src='@/assets/images/cup-qrcode-demo.jpg' style='width: 800px;'>\n                        </template>\n                        <i class=\"el-icon-info\"></i>\n                      </el-tooltip>\n                    </template>\n                    <el-input :trim=\"true\" v-model=\"extendForm.produceCode\" class=\"min-input\" placeholder=\"请输入餐品制作码\" maxlength=\"20\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"库存数量\" prop=\"stock\">\n                    <el-input v-model=\"extendForm.stock\" class=\"min-input\" placeholder=\"请输入库存数量\" maxlength=\"50\"/>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品价格\" prop=\"price\">\n                    <el-input v-model=\"extendForm.price\" class=\"min-input\" placeholder=\"请输入商品价格\" maxlength=\"50\"/>\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"会员专属价格\" prop=\"gradePrice\">\n                    <el-input v-model=\"extendForm.gradePrice\" class=\"min-input\" placeholder=\"请输入会员专属价格\" maxlength=\"50\" />\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n               <el-row v-if=\"baseForm.type == 'package'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"套餐商品\" prop=\"packageItems\">\n                    <PackageItemNew\n                      ref=\"packageItem\"\n                      :goodsId=\"baseForm.goodsId\"\n                      :uploadDomain=\"uploadDomain\"\n                      :typeOptions=\"typeOptions\"\n                      :packageData=\"packageData\"\n                      @change=\"handlePackageChange\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <!-- <el-row v-if=\"extendForm.isSingleSpec == 'Y'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"划线价格\" prop=\"linePrice\">\n                    <el-input v-model=\"extendForm.linePrice\" class=\"min-input\" placeholder=\"请输入商品划线，空则不显示\" maxlength=\"50\" />\n                    <div class=\"form-tips\">单位：元</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n              <!-- <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"初始销量\" prop=\"initSale\">\n                    <el-input v-model=\"extendForm.initSale\" class=\"min-input\" placeholder=\"请输入初始销量\" maxlength=\"10\"/>\n                    <div class=\"form-tips\">提示：输入数字，虚拟销量</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n              <!-- <el-row v-if=\"extendForm.isSingleSpec == 'Y' && baseForm.type == 'product'\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"商品重量\" prop=\"weight\">\n                    <el-input v-model=\"extendForm.weight\" class=\"min-input\" placeholder=\"请输入商品重量\" maxlength=\"10\"/>\n                    <div class=\"form-tips\">提示：输入数字，单位kg</div>\n                  </el-form-item>\n                </el-col>\n              </el-row> -->\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"商品介绍\" name=\"detail\">\n          <div class=\"content\" style=\"width: 375px;margin-left: 80px;\">\n            <el-form ref=\"detailForm\" :model=\"detailForm\" :rules=\"detailRules\" label-width=\"120px\">\n               <editor v-model=\"detailForm.description\" :min-height=\"550\"/>\n            </el-form>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"时间段配置\" name=\"timeConfig\" v-if=\"baseForm.goodsId && baseForm.goodsId !== '0'\">\n          <div class=\"time-config-tip\">\n            <el-alert\n              title=\"提示：可以为商品设置销售时间段，如每日通用、按周设置或自定义日期。设置后，商品只在指定时间段内可售。\"\n              type=\"info\"\n              show-icon\n              :closable=\"false\"\n            />\n          </div>\n          <TimeConfigList :goods-id=\"baseForm.goodsId\" />\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"footer\">\n         <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n         <el-button @click=\"cancel\">取消</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { saveGoods, getGoodsInfo, getPackageItems, savePackageItems } from \"@/api/goods\";\nimport Sku from '../components/Sku';\nimport PackageItemNew from '../components/PackageItemNew';\nimport TimeConfigList from '../components/TimeConfigList';\nexport default {\n  name: \"GoodsForm\",\n  components: { Sku, PackageItemNew, TimeConfigList },\n  data() {\n    return {\n      storeId: this.$store.getters.storeId,\n      // 遮罩层\n      loading: false,\n      activeTab: 'base',\n      storeOptions: [],\n      cateOptions: [],\n      typeOptions: [],\n      skuData: { attrList: [], skuList: [], initSkuList: [] },\n      // 套餐商品数据\n      packageData: [],\n      // 基础信息表单\n      baseForm: { type: 'goods', goodsId: '', name: '', storeId: this.$store.getters.storeId, cateId: '', goodsNo: '', images: [], status: \"A\", sort: 0 },\n      // 扩展信息表单\n      extendForm: { goodsId: '', canUsePoint: 'Y', isMemberDiscount: 'Y', isSingleSpec: 'Y', produceCode: '', serviceTime: 0, couponIds: '', stock: '', price: '', gradePrice: '' ,linePrice: '', salePoint: '', makeCode: '', drinkMaker: '', initSale: '', weight: '', skuData: null },\n      // 详情信息表单\n      detailForm: { goodsId: '', description : '' },\n      // 上传地址\n      uploadAction: process.env.VUE_APP_SERVER_URL + 'backendApi/file/upload',\n      uploadHeader: { 'Access-Token' : getToken() },\n      // 上传域名\n      uploadDomain: '',\n      // 上传文件列表\n      uploadFiles: [],\n      // 基础信息表单校验\n      baseRules: {\n        name: [\n          { required: true, message: \"商品名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 30, message: '商品名称长度必须介于2和200 之间', trigger: 'blur' }\n        ],\n        goodsNo: [\n          { required: true, message: \"商品条码不能为空\", trigger: \"blur\" },\n          { min: 2, max: 100, message: '商品条码长度必须介于2和100之间', trigger: 'blur' }\n        ],\n        cateId: [\n          { required: true, message: \"请选择商品分类\", trigger: \"blur\" }\n        ],\n        images: [\n          { required: true, message: \"请上传商品图片\", trigger: \"blur\" }\n        ],\n      },\n      // 扩展信息表单校验\n      extendRules: {\n        couponIds: [\n          { required: true, message: \"卡券ID不能为空\", trigger: \"blur\" },\n          { min: 1, max: 1000, message: '卡券ID长度必须介于1和100之间', trigger: 'blur' }\n        ],\n        canUsePoint: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        isMemberDiscount: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        isSingleSpec: [\n          { required: true, message: \"请选择\", trigger: \"blur\" }\n        ],\n        price: [\n          { required: true, message: \"请输入商品价格\", trigger: \"blur\" }, { pattern: /(^[1-9]\\d*(\\.\\d{1,2})?$)|(^0(\\.\\d{1,2})?$)/, message: `价格必须大于0`, trigger: 'blur' }\n        ],\n        /* produceCode: [\n          { pattern: /^=A(0[1-9]|[1-9][0-9])\\|C(0[1-9]|[1-9][0-9]),T(0[1-9]|[1-9][0-9]),W(0[1-9]|[1-9][0-9])$/, message: `输入正确的制作码`, trigger: 'input' }\n        ], */\n        stock: [\n          { required: true, message: \"请输入库存数量\", trigger: \"blur\" }, { pattern: /^[0-9]{1,10}$/, message: `库存数量必须是正整数`, trigger: 'blur' }\n        ],\n        initSale: [\n          { pattern: /^[0-9]{1,10}$/, message: `初始销量必须是正整数`, trigger: 'blur' }\n        ],\n        weight: [\n          { pattern: /(^[1-9]\\d*(\\.\\d{1,2})?$)|(^0(\\.\\d{1,2})?$)/, message: `重量必须大于等于0`, trigger: 'blur' }\n        ],\n      },\n      detailRules: {\n        description: [\n          { required: true, message: \"请输入商品详情\", trigger: \"blur\" }\n        ],\n      }\n    };\n  },\n  created() {\n    const goodsId = this.$route.query.goodsId ? this.$route.query.goodsId : '0'\n    this.getGoodsInfo(goodsId);\n\n    // 先只加载基础信息，等类型确认后再加载套餐数据\n  },\n  methods: {\n    handleTabClick() {\n       // empty\n    },\n\n    // 处理商品类型变化\n    handleTypeChange(value) {\n      // 如果不是套餐商品，清空套餐数据\n      if (value !== 'package') {\n        this.packageData = [];\n      } else if (this.baseForm.goodsId && this.baseForm.goodsId !== '0') {\n        // 如果是套餐商品，加载套餐数据\n        this.getPackageItems(this.baseForm.goodsId);\n      }\n    },\n    // 获取套餐商品项目\n    getPackageItems(goodsId) {\n      if (goodsId && goodsId !== '0') {\n        getPackageItems(goodsId).then(response => {\n          if (response.data && response.data.groups) {\n            this.packageData = response.data.groups;\n          }\n        });\n      }\n    },\n\n    // 处理套餐数据变化\n    handlePackageChange(packageItems) {\n      this.packageData = packageItems;\n    },\n\n    getGoodsInfo(goodsId) {\n      const app = this;\n      getGoodsInfo(goodsId).then(response => {\n          const goodsInfo = response.data.goods;\n          const imagePath = response.data.imagePath;\n          this.uploadDomain = imagePath;\n          if (goodsInfo) {\n              // 商品基础信息\n              this.baseForm.goodsId = goodsInfo.id+'';\n              this.baseForm.type = goodsInfo.type;\n              this.baseForm.name = goodsInfo.name;\n              this.baseForm.goodsNo = goodsInfo.goodsNo;\n              this.baseForm.cateId = goodsInfo.cateId;\n              this.baseForm.storeId = goodsInfo.storeId;\n              this.baseForm.sort = goodsInfo.sort;\n              this.baseForm.status = goodsInfo.status;\n\n              // 如果是套餐商品，获取套餐数据\n              if (goodsInfo.type === 'package' && goodsInfo.id) {\n                  this.getPackageItems(goodsInfo.id);\n              }\n              // 商品图片\n              this.baseForm.images = response.data.images;\n              const images = this.baseForm.images;\n              app.uploadFiles = [];\n              if (images && images.length > 0) {\n                  images.forEach(function(url){\n                     app.uploadFiles.push({ url: imagePath + url })\n                  })\n              }\n\n              // 扩展信息\n              this.extendForm.goodsId = goodsInfo.id;\n              this.extendForm.canUsePoint = goodsInfo.canUsePoint == null ? 'Y' : goodsInfo.canUsePoint;\n              this.extendForm.isMemberDiscount = goodsInfo.isMemberDiscount == null ? 'Y' : goodsInfo.isMemberDiscount;\n              this.extendForm.isSingleSpec = goodsInfo.isSingleSpec == null ? 'Y' : goodsInfo.isSingleSpec;\n              this.extendForm.stock = goodsInfo.stock;\n              this.extendForm.price = goodsInfo.price;\n              this.extendForm.gradePrice = goodsInfo.gradePrice ?? goodsInfo.price;\n              this.extendForm.linePrice = goodsInfo.linePrice;\n              this.extendForm.initSale = goodsInfo.initSale;\n              this.extendForm.salePoint = goodsInfo.salePoint;\n              this.extendForm.drinkMaker = goodsInfo.drinkMaker;\n              this.extendForm.makeCode = goodsInfo.makeCode;\n              this.extendForm.weight = goodsInfo.weight;\n              this.extendForm.serviceTime = goodsInfo.serviceTime;\n              this.extendForm.couponIds = goodsInfo.couponIds;\n              this.extendForm.produceCode = goodsInfo.produceCode;\n\n              // 多规格\n              this.skuData.attrList = response.data.specData;\n              this.skuData.skuList = response.data.skuData;\n              this.skuData.initSkuList = response.data.skuData;\n\n              // 商品详情\n              this.detailForm.goodsId = goodsInfo.id;\n              this.detailForm.description = goodsInfo.description;\n          }\n          this.cateOptions = response.data.cateList;\n          this.storeOptions = response.data.storeList;\n          this.typeOptions = response.data.typeList;\n      });\n    },\n    cancel() {\n      this.$store.dispatch('tagsView/delView', this.$route)\n      this.$router.push( { path: '/goods/goods/index' } );\n    },\n    skuChange(skuData) {\n       // empty\n    },\n    // 提交按钮\n    submitForm: function() {\n      const app = this;\n      if (app.activeTab == 'base') {\n          app.$refs[\"baseForm\"].validate(valid => {\n           if (valid) {\n               saveGoods(app.baseForm).then(response => {\n                  app.$modal.msgSuccess(\"保存成功！\");\n                  app.getGoodsInfo(response.data.goodsInfo.id);\n\n                  // 提示用户基础信息已保存\n                  app.$message.info('商品基础信息已保存')\n               }).catch(error => {\n                 console.error('保存基础信息失败:', error)\n                 app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n               });\n           }\n         });\n      } else if (app.activeTab == 'extend') {\n          if (!app.extendForm.goodsId) {\n              app.$modal.msgError(\"请先提交商品基础信息\");\n              app.activeTab = 'base';\n              return false;\n          }\n          // 多规格商品验证\n          if (app.skuData.skuList && app.skuData.skuList.length > 0) {\n              let isValid0 = true;\n              let isValid1 = true;\n              let isValid2 = true;\n              app.skuData.skuList.forEach(function(item) {\n                 if (!item.skuNo || item.skuNo.length < 1 ) {\n                     isValid0 = false;\n                 }\n                 if (item.stock < 0) {\n                     isValid1 = false;\n                 }\n                 if (item.price < 0) {\n                     isValid2 = false;\n                 }\n              })\n              if (!isValid1) {\n                  app.$modal.alert(\"商品sku编码长度需大于1，请仔细核对！\");\n                  return false;\n              }\n              if (!isValid1) {\n                  app.$modal.alert(\"商品库存须大于等于0，请仔细核对！\");\n                  return false;\n              }\n              if (!isValid2) {\n                  app.$modal.alert(\"商品价格须大于等于0，请仔细核对！\");\n                  return false;\n              }\n          }\n\n          app.extendForm.skuData = app.skuData.skuList;\n          app.extendForm.specData = app.skuData.attrList;\n\n          // 保存套餐信息\n          if (app.baseForm.type === 'package' && app.$refs.packageItem) {\n            const packageGroups = app.$refs.packageItem.getPackageData();\n            if (packageGroups.length === 0) {\n              app.$modal.msgError(\"请添加套餐分组\");\n              return false;\n            }\n\n            // 先保存基本信息\n            app.$refs[\"extendForm\"].validate(valid => {\n              if (valid) {\n                saveGoods(app.extendForm).then(response => {\n                  const goodsId = response.data.goodsInfo.id;\n                  // 再保存套餐项目\n                  savePackageItems({\n                    goodsId: goodsId,\n                    groups: packageGroups\n                  }).then(() => {\n                    app.$modal.msgSuccess(\"保存成功！\");\n                    app.getGoodsInfo(goodsId);\n                  });\n                });\n              }\n            });\n          } else {\n            // 普通商品保存\n            app.$refs[\"extendForm\"].validate(valid => {\n              if (valid) {\n                saveGoods(app.extendForm).then(response => {\n                 app.$modal.msgSuccess(\"保存成功！\");\n                 app.getGoodsInfo(response.data.goodsInfo.id);\n\n                 // 提示用户扩展信息已保存\n                 app.$message.info('商品扩展信息已保存')\n               }).catch(error => {\n                 console.error('保存扩展信息失败:', error)\n                 app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n               });\n              }\n            });\n          }\n      } else {\n          if (!app.detailForm.goodsId) {\n              app.$modal.msgError(\"请先提交商品基础信息\");\n              app.activeTab = 'base';\n              return false;\n          }\n          app.$refs[\"detailForm\"].validate(valid => {\n              if (valid) {\n                  saveGoods(app.detailForm).then(response => {\n                      app.$modal.msgSuccess(\"保存成功！\");\n                      app.getGoodsInfo(response.data.goodsInfo.id);\n\n                      // 提示用户详情信息已保存\n                      app.$message.info('商品详情信息已保存')\n                  }).catch(error => {\n                    console.error('保存详情信息失败:', error)\n                    app.$modal.msgError(\"保存失败: \" + (error.message || '未知错误'));\n                  });\n              }\n          });\n      }\n    },\n    // 文件上传成功\n    handleUploadSuccess(file) {\n      this.baseForm.images.push(file.data.fileName)\n    },\n    // 文件删除处理\n    handleRemove(file) {\n      const newImages = [];\n      if (this.baseForm.images && this.baseForm.images.length > 0) {\n          this.baseForm.images.forEach(function(item){\n              if (file.url.indexOf(item) == -1) {\n                  newImages.push(item);\n              }\n          })\n      }\n      this.baseForm.images = newImages;\n    },\n    // 生成随机条码\n    createGoodsSn() {\n      let sn = (Math.random() + 1) * 100000000000000;\n      this.baseForm.goodsNo = sn.toFixed(0);\n    }\n  }\n};\n</script>\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n   .main-panel {\n      padding-top: 5px;\n      .content {\n          margin-top: 30px;\n          margin-left: 20px;\n      }\n     .footer {\n        margin-top: 20px;\n     }\n     .create-sn {\n        font-size: 12px;\n        color: blue;\n        cursor: pointer;\n        width: 100px;\n     }\n     .time-config-tip {\n       padding: 10px 20px;\n     }\n   }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+TA,SAAAA,QAAA;AACA,SAAAC,SAAA,EAAAC,YAAA,IAAAA,aAAA,EAAAC,eAAA,IAAAA,gBAAA,EAAAC,gBAAA;AACA,OAAAC,GAAA;AACA,OAAAC,cAAA;AACA,OAAAC,cAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAJ,GAAA,EAAAA,GAAA;IAAAC,cAAA,EAAAA,cAAA;IAAAC,cAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA,OAAAC,MAAA,CAAAC,OAAA,CAAAF,OAAA;MACA;MACAG,OAAA;MACAC,SAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,OAAA;QAAAC,QAAA;QAAAC,OAAA;QAAAC,WAAA;MAAA;MACA;MACAC,WAAA;MACA;MACAC,QAAA;QAAAC,IAAA;QAAAC,OAAA;QAAAlB,IAAA;QAAAG,OAAA,OAAAC,MAAA,CAAAC,OAAA,CAAAF,OAAA;QAAAgB,MAAA;QAAAC,OAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,IAAA;MAAA;MACA;MACAC,UAAA;QAAAN,OAAA;QAAAO,WAAA;QAAAC,gBAAA;QAAAC,YAAA;QAAAC,WAAA;QAAAC,WAAA;QAAAC,SAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,UAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,UAAA;QAAAC,QAAA;QAAAC,MAAA;QAAA5B,OAAA;MAAA;MACA;MACA6B,UAAA;QAAAtB,OAAA;QAAAuB,WAAA;MAAA;MACA;MACAC,YAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,kBAAA;MACAC,YAAA;QAAA,gBAAAtD,QAAA;MAAA;MACA;MACAuD,YAAA;MACA;MACAC,WAAA;MACA;MACAC,SAAA;QACAjD,IAAA,GACA;UAAAkD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhC,OAAA,GACA;UAAA8B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjC,MAAA,GACA;UAAA+B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA/B,MAAA,GACA;UAAA6B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAG,WAAA;QACAzB,SAAA,GACA;UAAAoB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,WAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1B,gBAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAzB,YAAA,GACA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACApB,KAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;UAAAI,OAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACA;AACA;AACA;QACArB,KAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;UAAAI,OAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,QAAA,GACA;UAAAkB,OAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,MAAA,GACA;UAAAiB,OAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAK,WAAA;QACAhB,WAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,IAAAxC,OAAA,QAAAyC,MAAA,CAAAC,KAAA,CAAA1C,OAAA,QAAAyC,MAAA,CAAAC,KAAA,CAAA1C,OAAA;IACA,KAAAxB,YAAA,CAAAwB,OAAA;;IAEA;EACA;EACA2C,OAAA;IACAC,cAAA,WAAAA,eAAA;MACA;IAAA,CACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACA;MACA,IAAAA,KAAA;QACA,KAAAjD,WAAA;MACA,gBAAAC,QAAA,CAAAE,OAAA,SAAAF,QAAA,CAAAE,OAAA;QACA;QACA,KAAAvB,eAAA,MAAAqB,QAAA,CAAAE,OAAA;MACA;IACA;IACA;IACAvB,eAAA,WAAAA,gBAAAuB,OAAA;MAAA,IAAA+C,KAAA;MACA,IAAA/C,OAAA,IAAAA,OAAA;QACAvB,gBAAA,CAAAuB,OAAA,EAAAgD,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAjE,IAAA,IAAAiE,QAAA,CAAAjE,IAAA,CAAAkE,MAAA;YACAH,KAAA,CAAAlD,WAAA,GAAAoD,QAAA,CAAAjE,IAAA,CAAAkE,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAAC,YAAA;MACA,KAAAvD,WAAA,GAAAuD,YAAA;IACA;IAEA5E,YAAA,WAAAA,aAAAwB,OAAA;MAAA,IAAAqD,MAAA;MACA,IAAAC,GAAA;MACA9E,aAAA,CAAAwB,OAAA,EAAAgD,IAAA,WAAAC,QAAA;QACA,IAAAM,SAAA,GAAAN,QAAA,CAAAjE,IAAA,CAAAwE,KAAA;QACA,IAAAC,SAAA,GAAAR,QAAA,CAAAjE,IAAA,CAAAyE,SAAA;QACAJ,MAAA,CAAAxB,YAAA,GAAA4B,SAAA;QACA,IAAAF,SAAA;UAAA,IAAAG,qBAAA;UACA;UACAL,MAAA,CAAAvD,QAAA,CAAAE,OAAA,GAAAuD,SAAA,CAAAI,EAAA;UACAN,MAAA,CAAAvD,QAAA,CAAAC,IAAA,GAAAwD,SAAA,CAAAxD,IAAA;UACAsD,MAAA,CAAAvD,QAAA,CAAAhB,IAAA,GAAAyE,SAAA,CAAAzE,IAAA;UACAuE,MAAA,CAAAvD,QAAA,CAAAI,OAAA,GAAAqD,SAAA,CAAArD,OAAA;UACAmD,MAAA,CAAAvD,QAAA,CAAAG,MAAA,GAAAsD,SAAA,CAAAtD,MAAA;UACAoD,MAAA,CAAAvD,QAAA,CAAAb,OAAA,GAAAsE,SAAA,CAAAtE,OAAA;UACAoE,MAAA,CAAAvD,QAAA,CAAAO,IAAA,GAAAkD,SAAA,CAAAlD,IAAA;UACAgD,MAAA,CAAAvD,QAAA,CAAAM,MAAA,GAAAmD,SAAA,CAAAnD,MAAA;;UAEA;UACA,IAAAmD,SAAA,CAAAxD,IAAA,kBAAAwD,SAAA,CAAAI,EAAA;YACAN,MAAA,CAAA5E,eAAA,CAAA8E,SAAA,CAAAI,EAAA;UACA;UACA;UACAN,MAAA,CAAAvD,QAAA,CAAAK,MAAA,GAAA8C,QAAA,CAAAjE,IAAA,CAAAmB,MAAA;UACA,IAAAA,MAAA,GAAAkD,MAAA,CAAAvD,QAAA,CAAAK,MAAA;UACAmD,GAAA,CAAAxB,WAAA;UACA,IAAA3B,MAAA,IAAAA,MAAA,CAAAyD,MAAA;YACAzD,MAAA,CAAA0D,OAAA,WAAAC,GAAA;cACAR,GAAA,CAAAxB,WAAA,CAAAiC,IAAA;gBAAAD,GAAA,EAAAL,SAAA,GAAAK;cAAA;YACA;UACA;;UAEA;UACAT,MAAA,CAAA/C,UAAA,CAAAN,OAAA,GAAAuD,SAAA,CAAAI,EAAA;UACAN,MAAA,CAAA/C,UAAA,CAAAC,WAAA,GAAAgD,SAAA,CAAAhD,WAAA,iBAAAgD,SAAA,CAAAhD,WAAA;UACA8C,MAAA,CAAA/C,UAAA,CAAAE,gBAAA,GAAA+C,SAAA,CAAA/C,gBAAA,iBAAA+C,SAAA,CAAA/C,gBAAA;UACA6C,MAAA,CAAA/C,UAAA,CAAAG,YAAA,GAAA8C,SAAA,CAAA9C,YAAA,iBAAA8C,SAAA,CAAA9C,YAAA;UACA4C,MAAA,CAAA/C,UAAA,CAAAO,KAAA,GAAA0C,SAAA,CAAA1C,KAAA;UACAwC,MAAA,CAAA/C,UAAA,CAAAQ,KAAA,GAAAyC,SAAA,CAAAzC,KAAA;UACAuC,MAAA,CAAA/C,UAAA,CAAAS,UAAA,IAAA2C,qBAAA,GAAAH,SAAA,CAAAxC,UAAA,cAAA2C,qBAAA,cAAAA,qBAAA,GAAAH,SAAA,CAAAzC,KAAA;UACAuC,MAAA,CAAA/C,UAAA,CAAAU,SAAA,GAAAuC,SAAA,CAAAvC,SAAA;UACAqC,MAAA,CAAA/C,UAAA,CAAAc,QAAA,GAAAmC,SAAA,CAAAnC,QAAA;UACAiC,MAAA,CAAA/C,UAAA,CAAAW,SAAA,GAAAsC,SAAA,CAAAtC,SAAA;UACAoC,MAAA,CAAA/C,UAAA,CAAAa,UAAA,GAAAoC,SAAA,CAAApC,UAAA;UACAkC,MAAA,CAAA/C,UAAA,CAAAY,QAAA,GAAAqC,SAAA,CAAArC,QAAA;UACAmC,MAAA,CAAA/C,UAAA,CAAAe,MAAA,GAAAkC,SAAA,CAAAlC,MAAA;UACAgC,MAAA,CAAA/C,UAAA,CAAAK,WAAA,GAAA4C,SAAA,CAAA5C,WAAA;UACA0C,MAAA,CAAA/C,UAAA,CAAAM,SAAA,GAAA2C,SAAA,CAAA3C,SAAA;UACAyC,MAAA,CAAA/C,UAAA,CAAAI,WAAA,GAAA6C,SAAA,CAAA7C,WAAA;;UAEA;UACA2C,MAAA,CAAA5D,OAAA,CAAAC,QAAA,GAAAuD,QAAA,CAAAjE,IAAA,CAAAgF,QAAA;UACAX,MAAA,CAAA5D,OAAA,CAAAE,OAAA,GAAAsD,QAAA,CAAAjE,IAAA,CAAAS,OAAA;UACA4D,MAAA,CAAA5D,OAAA,CAAAG,WAAA,GAAAqD,QAAA,CAAAjE,IAAA,CAAAS,OAAA;;UAEA;UACA4D,MAAA,CAAA/B,UAAA,CAAAtB,OAAA,GAAAuD,SAAA,CAAAI,EAAA;UACAN,MAAA,CAAA/B,UAAA,CAAAC,WAAA,GAAAgC,SAAA,CAAAhC,WAAA;QACA;QACA8B,MAAA,CAAA9D,WAAA,GAAA0D,QAAA,CAAAjE,IAAA,CAAAiF,QAAA;QACAZ,MAAA,CAAA/D,YAAA,GAAA2D,QAAA,CAAAjE,IAAA,CAAAkF,SAAA;QACAb,MAAA,CAAA7D,WAAA,GAAAyD,QAAA,CAAAjE,IAAA,CAAAmF,QAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAlF,MAAA,CAAAmF,QAAA,0BAAA5B,MAAA;MACA,KAAA6B,OAAA,CAAAP,IAAA;QAAAQ,IAAA;MAAA;IACA;IACAC,SAAA,WAAAA,UAAA/E,OAAA;MACA;IAAA,CACA;IACA;IACAgF,UAAA,WAAAA,WAAA;MACA,IAAAnB,GAAA;MACA,IAAAA,GAAA,CAAAjE,SAAA;QACAiE,GAAA,CAAAoB,KAAA,aAAAC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACArG,SAAA,CAAA+E,GAAA,CAAAxD,QAAA,EAAAkD,IAAA,WAAAC,QAAA;cACAK,GAAA,CAAAuB,MAAA,CAAAC,UAAA;cACAxB,GAAA,CAAA9E,YAAA,CAAAyE,QAAA,CAAAjE,IAAA,CAAAuE,SAAA,CAAAI,EAAA;;cAEA;cACAL,GAAA,CAAAyB,QAAA,CAAAC,IAAA;YACA,GAAAC,KAAA,WAAAC,KAAA;cACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;cACA5B,GAAA,CAAAuB,MAAA,CAAAO,QAAA,aAAAF,KAAA,CAAAjD,OAAA;YACA;UACA;QACA;MACA,WAAAqB,GAAA,CAAAjE,SAAA;QACA,KAAAiE,GAAA,CAAAhD,UAAA,CAAAN,OAAA;UACAsD,GAAA,CAAAuB,MAAA,CAAAO,QAAA;UACA9B,GAAA,CAAAjE,SAAA;UACA;QACA;QACA;QACA,IAAAiE,GAAA,CAAA7D,OAAA,CAAAE,OAAA,IAAA2D,GAAA,CAAA7D,OAAA,CAAAE,OAAA,CAAAiE,MAAA;UACA,IAAAyB,QAAA;UACA,IAAAC,QAAA;UACA,IAAAC,QAAA;UACAjC,GAAA,CAAA7D,OAAA,CAAAE,OAAA,CAAAkE,OAAA,WAAA2B,IAAA;YACA,KAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAC,KAAA,CAAA7B,MAAA;cACAyB,QAAA;YACA;YACA,IAAAG,IAAA,CAAA3E,KAAA;cACAyE,QAAA;YACA;YACA,IAAAE,IAAA,CAAA1E,KAAA;cACAyE,QAAA;YACA;UACA;UACA,KAAAD,QAAA;YACAhC,GAAA,CAAAuB,MAAA,CAAAa,KAAA;YACA;UACA;UACA,KAAAJ,QAAA;YACAhC,GAAA,CAAAuB,MAAA,CAAAa,KAAA;YACA;UACA;UACA,KAAAH,QAAA;YACAjC,GAAA,CAAAuB,MAAA,CAAAa,KAAA;YACA;UACA;QACA;QAEApC,GAAA,CAAAhD,UAAA,CAAAb,OAAA,GAAA6D,GAAA,CAAA7D,OAAA,CAAAE,OAAA;QACA2D,GAAA,CAAAhD,UAAA,CAAA0D,QAAA,GAAAV,GAAA,CAAA7D,OAAA,CAAAC,QAAA;;QAEA;QACA,IAAA4D,GAAA,CAAAxD,QAAA,CAAAC,IAAA,kBAAAuD,GAAA,CAAAoB,KAAA,CAAAiB,WAAA;UACA,IAAAC,aAAA,GAAAtC,GAAA,CAAAoB,KAAA,CAAAiB,WAAA,CAAAE,cAAA;UACA,IAAAD,aAAA,CAAAhC,MAAA;YACAN,GAAA,CAAAuB,MAAA,CAAAO,QAAA;YACA;UACA;;UAEA;UACA9B,GAAA,CAAAoB,KAAA,eAAAC,QAAA,WAAAC,KAAA;YACA,IAAAA,KAAA;cACArG,SAAA,CAAA+E,GAAA,CAAAhD,UAAA,EAAA0C,IAAA,WAAAC,QAAA;gBACA,IAAAjD,OAAA,GAAAiD,QAAA,CAAAjE,IAAA,CAAAuE,SAAA,CAAAI,EAAA;gBACA;gBACAjF,gBAAA;kBACAsB,OAAA,EAAAA,OAAA;kBACAkD,MAAA,EAAA0C;gBACA,GAAA5C,IAAA;kBACAM,GAAA,CAAAuB,MAAA,CAAAC,UAAA;kBACAxB,GAAA,CAAA9E,YAAA,CAAAwB,OAAA;gBACA;cACA;YACA;UACA;QACA;UACA;UACAsD,GAAA,CAAAoB,KAAA,eAAAC,QAAA,WAAAC,KAAA;YACA,IAAAA,KAAA;cACArG,SAAA,CAAA+E,GAAA,CAAAhD,UAAA,EAAA0C,IAAA,WAAAC,QAAA;gBACAK,GAAA,CAAAuB,MAAA,CAAAC,UAAA;gBACAxB,GAAA,CAAA9E,YAAA,CAAAyE,QAAA,CAAAjE,IAAA,CAAAuE,SAAA,CAAAI,EAAA;;gBAEA;gBACAL,GAAA,CAAAyB,QAAA,CAAAC,IAAA;cACA,GAAAC,KAAA,WAAAC,KAAA;gBACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;gBACA5B,GAAA,CAAAuB,MAAA,CAAAO,QAAA,aAAAF,KAAA,CAAAjD,OAAA;cACA;YACA;UACA;QACA;MACA;QACA,KAAAqB,GAAA,CAAAhC,UAAA,CAAAtB,OAAA;UACAsD,GAAA,CAAAuB,MAAA,CAAAO,QAAA;UACA9B,GAAA,CAAAjE,SAAA;UACA;QACA;QACAiE,GAAA,CAAAoB,KAAA,eAAAC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACArG,SAAA,CAAA+E,GAAA,CAAAhC,UAAA,EAAA0B,IAAA,WAAAC,QAAA;cACAK,GAAA,CAAAuB,MAAA,CAAAC,UAAA;cACAxB,GAAA,CAAA9E,YAAA,CAAAyE,QAAA,CAAAjE,IAAA,CAAAuE,SAAA,CAAAI,EAAA;;cAEA;cACAL,GAAA,CAAAyB,QAAA,CAAAC,IAAA;YACA,GAAAC,KAAA,WAAAC,KAAA;cACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;cACA5B,GAAA,CAAAuB,MAAA,CAAAO,QAAA,aAAAF,KAAA,CAAAjD,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA6D,mBAAA,WAAAA,oBAAAC,IAAA;MACA,KAAAjG,QAAA,CAAAK,MAAA,CAAA4D,IAAA,CAAAgC,IAAA,CAAA/G,IAAA,CAAAgH,QAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAF,IAAA;MACA,IAAAG,SAAA;MACA,SAAApG,QAAA,CAAAK,MAAA,SAAAL,QAAA,CAAAK,MAAA,CAAAyD,MAAA;QACA,KAAA9D,QAAA,CAAAK,MAAA,CAAA0D,OAAA,WAAA2B,IAAA;UACA,IAAAO,IAAA,CAAAjC,GAAA,CAAAqC,OAAA,CAAAX,IAAA;YACAU,SAAA,CAAAnC,IAAA,CAAAyB,IAAA;UACA;QACA;MACA;MACA,KAAA1F,QAAA,CAAAK,MAAA,GAAA+F,SAAA;IACA;IACA;IACAE,aAAA,WAAAA,cAAA;MACA,IAAAC,EAAA,IAAAC,IAAA,CAAAC,MAAA;MACA,KAAAzG,QAAA,CAAAI,OAAA,GAAAmG,EAAA,CAAAG,OAAA;IACA;EACA;AACA", "ignoreList": []}]}