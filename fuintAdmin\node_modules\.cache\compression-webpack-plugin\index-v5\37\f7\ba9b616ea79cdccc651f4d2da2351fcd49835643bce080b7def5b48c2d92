
f7e49a833d36cec547741a337f0457fe410454dc	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.1f17f65bd0dd2e1f8fbb.hot-update.js\",\"contentHash\":\"7aceacd96cadd8dc54a8df9dd6439b14\"}","integrity":"sha512-7nc/reDllk+fKEN9rYVoUTjuWpULYkZ2f/XZXEKK+bMXBwtTzFPSXDRmGntaItZrnY2MAjTl02f0rhav85nuAQ==","time":1754921484034,"size":18967}