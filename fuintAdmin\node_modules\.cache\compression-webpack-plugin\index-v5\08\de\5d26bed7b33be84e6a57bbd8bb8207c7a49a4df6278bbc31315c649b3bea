
1a314188c8044f8d50da40d8f645a4045d4a47d3	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"2525dfb805adbcb5edbdd8b5258a778d\"}","integrity":"sha512-aa6eM5MmLpU7c25D0zWhcW4Lt1Q+asVuvp2KiGOcGGOOiigbQNhTK/R6iZ7ecqOr7QrL0p5E4yuA41R1U22/lw==","time":1754922009494,"size":2882585}