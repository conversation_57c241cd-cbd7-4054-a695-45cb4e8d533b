{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigList.vue?vue&type=style&index=0&id=0a0596c3&scoped=true&lang=css", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigList.vue", "mtime": 1754839801250}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1734093918845}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1734093920151}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1734093919263}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi50aW1lLWNvbmZpZy1saXN0IHsKICBwYWRkaW5nOiAyMHB4Owp9CgouaGVhZGVyLWFjdGlvbnMgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0K"}, {"version": 3, "sources": ["TimeConfigList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4TA;AACA;AACA;;AAEA;AACA;AACA", "file": "TimeConfigList.vue", "sourceRoot": "src/views/goods/components", "sourcesContent": ["<template>\n  <div class=\"time-config-list\">\n    <div class=\"header-actions\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增时间段配置</el-button>\n      <div class=\"form-tip\">提示：可以为商品设置多个时间段配置，如每日通用、按周设置或自定义日期</div>\n    </div>\n\n    <el-table :data=\"configList\" v-loading=\"loading\" style=\"width: 100%\">\n      <el-table-column prop=\"configType\" label=\"配置类型\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getConfigTypeTag(scope.row.configType)\">\n            {{ getConfigTypeText(scope.row.configType) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"configValue\" label=\"配置值\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div v-if=\"scope.row.configType === 'DAILY'\">\n            <span>每日通用</span>\n          </div>\n          <div v-else-if=\"scope.row.configType === 'WEEKLY'\">\n            <span>{{ formatWeekDays(scope.row.configValue) }}</span>\n          </div>\n          <div v-else-if=\"scope.row.configType === 'CUSTOM'\">\n            <span>{{ formatCustomDates(scope.row.configValue) }}</span>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"timeRanges\" label=\"时间段\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div v-for=\"(range, index) in parseTimeRanges(scope.row.timeRanges)\" :key=\"index\">\n            <el-tag size=\"mini\">{{ formatTimeRange(range.startTime, range.endTime) }}</el-tag>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"A\"\n            inactive-value=\"N\"\n            @change=\"handleStatusChange(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\">\n        <template slot-scope=\"scope\">\n          {{ parseTime(scope.row.createTime) }}\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"mini\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEdit(scope.row)\"\n          >编辑</el-button>\n          <el-button\n            type=\"text\"\n            size=\"mini\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      width=\"600px\"\n      @close=\"handleDialogClose\"\n    >\n      <TimeConfigForm\n        ref=\"timeConfigForm\"\n        v-model=\"currentConfig\"\n        :goods-id=\"goodsId\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleSave\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getGoodsTimeConfigs, saveGoodsTimeConfig, updateGoodsTimeConfig, deleteGoodsTimeConfig } from '@/api/goodsTimeConfig'\nimport TimeConfigForm from './TimeConfigForm'\n\nexport default {\n  name: 'TimeConfigList',\n  components: {\n    TimeConfigForm\n  },\n  props: {\n    goodsId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      configList: [],\n      dialogVisible: false,\n      dialogTitle: '',\n      currentConfig: {},\n      isEdit: false\n    }\n  },\n  watch: {\n    goodsId: {\n      handler(newVal) {\n        if (newVal) {\n          this.loadConfigs()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    loadConfigs() {\n      if (!this.goodsId) return\n\n      this.loading = true\n      getGoodsTimeConfigs(this.goodsId).then(response => {\n        this.configList = response.data || []\n        this.loading = false\n      }).catch(error => {\n        console.error('加载时间段配置失败:', error)\n        this.$message.error('加载时间段配置失败: ' + (error.message || '未知错误'))\n        this.loading = false\n      })\n    },\n\n    handleAdd() {\n      this.isEdit = false\n      this.dialogTitle = '新增时间段配置'\n      this.currentConfig = {\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      }\n      this.dialogVisible = true\n\n      // 提示用户可以开始配置\n      this.$message.info('请配置时间段信息')\n    },\n\n    handleEdit(row) {\n      this.isEdit = true\n      this.dialogTitle = '编辑时间段配置'\n      this.currentConfig = {\n        ...row,\n        weekDays: row.configType === 'WEEKLY' ? JSON.parse(row.configValue || '[]') : [],\n        customDates: row.configType === 'CUSTOM' ? JSON.parse(row.configValue || '[]') : [],\n        timeRanges: JSON.parse(row.timeRanges || '[]')\n      }\n      this.dialogVisible = true\n\n      // 提示用户可以开始编辑\n      this.$message.info('请编辑时间段配置信息')\n    },\n\n    handleDelete(row) {\n      this.$confirm('确认删除该时间段配置吗？', '提示', {\n        type: 'warning'\n      }).then(() => {\n        deleteGoodsTimeConfig(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.loadConfigs()\n\n          // 提示用户配置已删除\n          this.$message.info('时间段配置已删除')\n        }).catch(error => {\n          console.error('删除失败:', error)\n          this.$message.error('删除失败: ' + (error.message || '未知错误'))\n        })\n      }).catch(() => {\n        // 用户取消删除\n        this.$message.info('已取消删除操作')\n      })\n    },\n\n    handleStatusChange(row) {\n      const statusText = row.status === 'A' ? '启用' : '禁用'\n      updateGoodsTimeConfig({\n        id: row.id,\n        status: row.status\n      }).then(() => {\n        this.$message.success(`${statusText}成功`)\n\n        // 提示用户状态已更新\n        this.$message.info(`时间段配置已${statusText}`)\n      }).catch(error => {\n        console.error('状态更新失败:', error)\n        this.$message.error(`${statusText}失败: ` + (error.message || '未知错误'))\n        row.status = row.status === 'A' ? 'N' : 'A'\n      })\n    },\n\n    handleSave() {\n      this.$refs.timeConfigForm.validate().then(formData => {\n        const data = {\n          ...formData,\n          configValue: formData.configType === 'WEEKLY'\n            ? JSON.stringify(formData.weekDays)\n            : formData.configType === 'CUSTOM'\n              ? JSON.stringify(formData.customDates)\n              : '',\n          timeRanges: JSON.stringify(formData.timeRanges)\n        }\n\n        const savePromise = this.isEdit\n          ? updateGoodsTimeConfig(data)\n          : saveGoodsTimeConfig(data)\n\n        savePromise.then(() => {\n          this.$message.success('保存成功')\n          this.dialogVisible = false\n          this.loadConfigs()\n\n          // 提示用户配置已更新\n          this.$message.info('时间段配置已更新')\n        }).catch(error => {\n          console.error('保存失败:', error)\n          this.$message.error('保存失败: ' + (error.message || '未知错误'))\n        })\n      }).catch(() => {\n        // 表单验证失败\n      })\n    },\n\n    handleDialogClose() {\n      this.$refs.timeConfigForm && this.$refs.timeConfigForm.resetForm()\n    },\n\n    getConfigTypeTag(type) {\n      const map = {\n        DAILY: 'success',\n        WEEKLY: 'warning',\n        CUSTOM: 'info'\n      }\n      return map[type] || 'default'\n    },\n\n    getConfigTypeText(type) {\n      const map = {\n        DAILY: '每日通用',\n        WEEKLY: '按周设置',\n        CUSTOM: '自定义日期'\n      }\n      return map[type] || type\n    },\n\n    formatWeekDays(weekDaysStr) {\n      try {\n        const weekDays = JSON.parse(weekDaysStr || '[]')\n        const weekMap = {\n          1: '周一',\n          2: '周二',\n          3: '周三',\n          4: '周四',\n          5: '周五',\n          6: '周六',\n          7: '周日'\n        }\n        return weekDays.map(day => weekMap[day]).join('、')\n      } catch {\n        return weekDaysStr\n      }\n    },\n\n    formatCustomDates(datesStr) {\n      try {\n        const dates = JSON.parse(datesStr || '[]')\n        return dates.join('、')\n      } catch {\n        return datesStr\n      }\n    },\n\n    parseTimeRanges(timeRangesStr) {\n      try {\n        return JSON.parse(timeRangesStr || '[]')\n      } catch {\n        return []\n      }\n    },\n\n    parseTime(time) {\n      if (!time) return ''\n      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss')\n    },\n\n    // 格式化时间段显示\n    formatTimeRange(startTime, endTime) {\n      if (!startTime || !endTime) return ''\n      return `${startTime} - ${endTime}`\n    }\n  }\n}\n</script>\n\n<style scoped>\n.time-config-list {\n  padding: 20px;\n}\n\n.header-actions {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}