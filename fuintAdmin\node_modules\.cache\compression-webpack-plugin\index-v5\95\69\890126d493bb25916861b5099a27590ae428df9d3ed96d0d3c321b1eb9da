
d15831cd8508eb2d94eff06bf7e4f666a18ff09c	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"0f8e98b8c25d15d01ac907a571b87df6\"}","integrity":"sha512-6yntmHuQUaa+ecXxS/GMmsOtLQVjXlmquCcKEkJavUqSBC1Bv50YR95rvXoIS5EhnYMOXo8tmVPY/BmkQTjs3Q==","time":1754920242129,"size":6733179}