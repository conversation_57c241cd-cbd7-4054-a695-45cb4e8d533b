{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue", "mtime": 1754845237275}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TimeConfigForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TimeConfigForm.vue", "sourceRoot": "src/views/goods/components", "sourcesContent": ["<template>\n  <div class=\"time-config-container\">\n    <el-form ref=\"timeConfigForm\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"配置类型\" prop=\"configType\">\n            <el-radio-group v-model=\"form.configType\" @change=\"handleConfigTypeChange\">\n              <el-radio label=\"DAILY\">每日通用</el-radio>\n              <el-radio label=\"WEEKLY\">按周设置</el-radio>\n              <el-radio label=\"CUSTOM\">自定义日期</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 按周设置时显示星期选择 -->\n      <el-row v-if=\"form.configType === 'WEEKLY'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择星期\" prop=\"weekDays\">\n            <el-checkbox-group v-model=\"form.weekDays\">\n              <el-checkbox v-for=\"day in weekOptions\" :key=\"day.value\" :label=\"day.value\">\n                {{ day.label }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 自定义日期时显示日期选择 -->\n      <el-row v-if=\"form.configType === 'CUSTOM'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择日期\" prop=\"customDates\">\n            <el-date-picker\n              v-model=\"form.customDates\"\n              type=\"dates\"\n              placeholder=\"选择一个或多个日期\"\n              value-format=\"yyyy-MM-dd\"\n              :picker-options=\"pickerOptions\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 时间段设置 -->\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"时间段\" prop=\"timeRanges\">\n            <div class=\"time-ranges-container\">\n              <div v-for=\"(range, index) in form.timeRanges\" :key=\"index\" class=\"time-range-item\">\n                <el-time-picker\n                  v-model=\"range.startTime\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <span class=\"time-separator\">至</span>\n                <el-time-picker\n                  v-model=\"range.endTime\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <el-button\n                  type=\"danger\"\n                  icon=\"el-icon-delete\"\n                  size=\"mini\"\n                  circle\n                  @click=\"removeTimeRange(index)\"\n                  :disabled=\"form.timeRanges.length === 1\"\n                />\n              </div>\n              <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"addTimeRange\">\n                添加时间段\n              </el-button>\n              <div class=\"form-tip\">提示：时间段不能重叠，支持跨天时间段配置（如22:00-02:00）</div>\n            </div>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-radio-group v-model=\"form.status\">\n              <el-radio label=\"A\">启用</el-radio>\n              <el-radio label=\"N\">禁用</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TimeConfigForm',\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    goodsId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      form: {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      },\n      weekOptions: [\n        { label: '周一', value: 1 },\n        { label: '周二', value: 2 },\n        { label: '周三', value: 3 },\n        { label: '周四', value: 4 },\n        { label: '周五', value: 5 },\n        { label: '周六', value: 6 },\n        { label: '周日', value: 7 }\n      ],\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7\n        }\n      },\n      rules: {\n        configType: [\n          { required: true, message: '请选择配置类型', trigger: 'change' }\n        ],\n        weekDays: [\n          {\n            required: true,\n            message: '请至少选择一个星期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'WEEKLY' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个星期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        customDates: [\n          {\n            required: true,\n            message: '请至少选择一个日期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'CUSTOM' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个日期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        timeRanges: [\n          {\n            required: true,\n            message: '请设置至少一个时间段',\n            trigger: 'blur',\n            validator: (rule, value, callback) => {\n              if (!value || value.length === 0) {\n                callback(new Error('请设置至少一个时间段'))\n              } else {\n                for (let range of value) {\n                  if (!range.startTime || !range.endTime) {\n                    callback(new Error('请完善时间段设置'))\n                    return\n                  }\n                  // 移除开始时间必须小于结束时间的验证，允许跨天时间段（如22:00-02:00）\n                }\n                // 检查时间段是否重叠\n                for (let i = 0; i < value.length; i++) {\n                  for (let j = i + 1; j < value.length; j++) {\n                    const range1 = value[i];\n                    const range2 = value[j];\n                    // 检查时间段是否重叠（考虑跨天情况）\n                    const start1 = range1.startTime;\n                    const end1 = range1.endTime;\n                    const start2 = range2.startTime;\n                    const end2 = range2.endTime;\n\n                    // 处理跨天时间段重叠检查\n                    let overlap = false;\n                    if (end1 <= start1) {\n                      // range1 是跨天时间段 (如 22:00-02:00)\n                      if (end2 <= start2) {\n                        // range2 也是跨天时间段\n                        overlap = true; // 两个跨天时间段总是重叠\n                      } else {\n                        // range2 不跨天\n                        if (start2 >= start1 || end2 <= end1) {\n                          overlap = true;\n                        }\n                      }\n                    } else {\n                      // range1 不跨天\n                      if (end2 <= start2) {\n                        // range2 是跨天时间段\n                        if (start1 >= start2 || end1 <= end2) {\n                          overlap = true;\n                        }\n                      } else {\n                        // range2 也不跨天\n                        if ((start1 <= start2 && start2 < end1) ||\n                            (start2 <= start1 && start1 < end2)) {\n                          overlap = true;\n                        }\n                      }\n                    }\n\n                    if (overlap) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  }\n                }\n                callback()\n              }\n            }\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        if (newVal && Object.keys(newVal).length > 0) {\n          this.form = {\n            ...this.form,\n            ...newVal,\n            timeRanges: newVal.timeRanges && newVal.timeRanges.length > 0\n              ? newVal.timeRanges\n              : [{ startTime: '', endTime: '' }]\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    form: {\n      handler(newVal) {\n        // 对时间段进行排序\n        if (newVal.timeRanges && newVal.timeRanges.length > 1) {\n          newVal.timeRanges.sort((a, b) => {\n            if (a.startTime && b.startTime) {\n              return a.startTime.localeCompare(b.startTime)\n            }\n            return 0\n          })\n        }\n        this.$emit('input', newVal)\n      },\n      deep: true\n    }\n  },\n  methods: {\n    handleConfigTypeChange() {\n      // 切换配置类型时清空相关数据\n      if (this.form.configType === 'DAILY') {\n        this.form.weekDays = []\n        this.form.customDates = []\n      } else if (this.form.configType === 'WEEKLY') {\n        this.form.customDates = []\n      } else if (this.form.configType === 'CUSTOM') {\n        this.form.weekDays = []\n      }\n\n      // 提示用户配置类型已切换\n      this.$message.info('已切换配置类型，请重新设置相关选项')\n\n      // 验证表单\n      this.$nextTick(() => {\n        this.$refs.timeConfigForm.validateField('weekDays')\n        this.$refs.timeConfigForm.validateField('customDates')\n      })\n    },\n    addTimeRange() {\n      this.form.timeRanges.push({ startTime: '', endTime: '' })\n    },\n    removeTimeRange(index) {\n      if (this.form.timeRanges.length > 1) {\n        this.form.timeRanges.splice(index, 1)\n      }\n    },\n    validate() {\n      return new Promise((resolve, reject) => {\n        this.$refs.timeConfigForm.validate(valid => {\n          if (valid) {\n            resolve(this.form)\n          } else {\n            this.$message.error('表单验证失败，请检查输入内容')\n            reject(new Error('表单验证失败'))\n          }\n        })\n      })\n    },\n    resetForm() {\n      this.$refs.timeConfigForm.resetFields()\n      this.form = {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.time-config-container {\n  padding: 20px;\n}\n\n.time-ranges-container {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.time-range-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  margin: 0 5px;\n  color: #606266;\n}\n</style>\n"]}]}