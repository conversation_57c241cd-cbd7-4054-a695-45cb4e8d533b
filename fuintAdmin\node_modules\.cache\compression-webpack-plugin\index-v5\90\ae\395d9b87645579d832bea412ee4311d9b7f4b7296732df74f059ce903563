
c853b30090534ec748f059f1bb21685c2cc7e7c8	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.5fdfb1f2508e1bf4e55b.hot-update.js\",\"contentHash\":\"e79c8693a2159da0395c136c6e743f70\"}","integrity":"sha512-/NXFBVvXlLeQxc2Rl+DPAD+/L8aJLxrxmvLxou7F8qQFFkLOQB7q8tgOOZbf7TsIQxkK1IkQ36rkZdYvemdTmQ==","time":1754921674910,"size":7678}