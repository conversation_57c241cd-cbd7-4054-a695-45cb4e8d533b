{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\category-config.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\category-config.vue", "mtime": 1754921411934}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFByaW50ZXJMaXN0LCBnZXRDYXRlZ29yeUxpc3QsIHNhdmVQcmludGVyQ2F0ZWdvcmllcywgZ2V0UHJpbnRlckNhdGVnb3JpZXMgfSBmcm9tICJAL2FwaS9wcmludGVyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUHJpbnRlckNhdGVnb3J5Q29uZmlnIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNhdmluZzogZmFsc2UsCiAgICAgIGNhdGVnb3J5TG9hZGluZzogZmFsc2UsCgogICAgICAvLyDooajljZXmlbDmja4KICAgICAgZm9ybTogewogICAgICAgIHByaW50ZXJJZDogJycsCiAgICAgICAgY2F0ZUlkczogW10KICAgICAgfSwKCiAgICAgIC8vIOWfuuehgOaVsOaNrgogICAgICBsYWJlbFByaW50ZXJzOiBbXSwKICAgICAgY2F0ZWdvcnlMaXN0OiBbXSwKICAgICAgc3RvcmVMaXN0OiBbXSwKICAgICAgcHJpbnRlckNhdGVnb3JpZXNNYXA6IHt9CiAgICB9OwogIH0sCgogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmxvYWREYXRhKCk7CiAgfSwKCiAgbWV0aG9kczogewogICAgLy8g5Yqg6L295omA5pyJ5pWw5o2uCiAgICBhc3luYyBsb2FkRGF0YSgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdHJ5IHsKICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChbCiAgICAgICAgICB0aGlzLmxvYWRQcmludGVycygpLAogICAgICAgICAgdGhpcy5sb2FkQ2F0ZWdvcmllcygpCiAgICAgICAgXSk7CiAgICAgICAgYXdhaXQgdGhpcy5sb2FkQWxsUHJpbnRlckNhdGVnb3JpZXMoKTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mlbDmja7lpLHotKU6JywgZXJyb3IpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9LAoKICAgIC8vIOWKoOi9veaJk+WNsOacuuWIl+ihqAogICAgbG9hZFByaW50ZXJzKCkgewogICAgICByZXR1cm4gZ2V0UHJpbnRlckxpc3QoeyB0eXBlOiAnTEFCRUwnIH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubGFiZWxQcmludGVycyA9IHJlc3BvbnNlLmRhdGEucGFnaW5hdGlvblJlc3BvbnNlLmNvbnRlbnQuZmlsdGVyKHAgPT4gcC50eXBlID09PSAnTEFCRUwnKTsKICAgICAgICB0aGlzLnN0b3JlTGlzdCA9IHJlc3BvbnNlLmRhdGEuc3RvcmVMaXN0IHx8IFtdOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g5Yqg6L295YiG57G75YiX6KGoCiAgICBsb2FkQ2F0ZWdvcmllcygpIHsKICAgICAgcmV0dXJuIGdldENhdGVnb3J5TGlzdCgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuY2F0ZWdvcnlMaXN0ID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOWKoOi9veaJgOacieaJk+WNsOacuueahOWIhuexu+WFs+iBlAogICAgYXN5bmMgbG9hZEFsbFByaW50ZXJDYXRlZ29yaWVzKCkgewogICAgICB0aGlzLnByaW50ZXJDYXRlZ29yaWVzTWFwID0ge307CgogICAgICBmb3IgKGNvbnN0IHByaW50ZXIgb2YgdGhpcy5sYWJlbFByaW50ZXJzKSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0UHJpbnRlckNhdGVnb3JpZXMocHJpbnRlci5pZCk7CiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5wcmludGVyQ2F0ZWdvcmllc01hcCwgcHJpbnRlci5pZCwgcmVzcG9uc2UuZGF0YSB8fCBbXSk7CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOWKoOi9veaJk+WNsOacuiR7cHJpbnRlci5pZH3liIbnsbvlhbPogZTlpLHotKU6YCwgZXJyb3IpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvLyDpgInmi6nmiZPljbDmnLoKICAgIHNlbGVjdFByaW50ZXIocHJpbnRlcikgewogICAgICB0aGlzLmZvcm0ucHJpbnRlcklkID0gcHJpbnRlci5pZDsKICAgICAgdGhpcy5vblByaW50ZXJDaGFuZ2UoKTsKICAgIH0sCgogICAgLy8g5omT5Y2w5py65Y+Y5YyW5LqL5Lu2CiAgICBvblByaW50ZXJDaGFuZ2UoKSB7CiAgICAgIGlmICghdGhpcy5mb3JtLnByaW50ZXJJZCkgewogICAgICAgIHRoaXMuZm9ybS5jYXRlSWRzID0gW107CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLmNhdGVnb3J5TG9hZGluZyA9IHRydWU7CiAgICAgIGdldFByaW50ZXJDYXRlZ29yaWVzKHRoaXMuZm9ybS5wcmludGVySWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnN0IHByaW50ZXJDYXRlZ29yaWVzID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsKICAgICAgICB0aGlzLmZvcm0uY2F0ZUlkcyA9IHByaW50ZXJDYXRlZ29yaWVzLm1hcChwYyA9PiBwYy5jYXRlSWQpOwogICAgICAgIHRoaXMuY2F0ZWdvcnlMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3miZPljbDmnLrliIbnsbvlhbPogZTlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuY2F0ZWdvcnlMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDkv53lrZjphY3nva4KICAgIHNhdmVDb25maWcoKSB7CiAgICAgIGlmICghdGhpcy5mb3JtLnByaW50ZXJJZCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup5omT5Y2w5py6Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLnNhdmluZyA9IHRydWU7CgogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIHByaW50ZXJJZDogdGhpcy5mb3JtLnByaW50ZXJJZCwKICAgICAgICBjYXRlSWRzOiB0aGlzLmZvcm0uY2F0ZUlkcwogICAgICB9OwoKICAgICAgc2F2ZVByaW50ZXJDYXRlZ29yaWVzKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6YWN572u5L+d5a2Y5oiQ5YqfJyk7CiAgICAgICAgdGhpcy5sb2FkQWxsUHJpbnRlckNhdGVnb3JpZXMoKTsKICAgICAgICB0aGlzLnNhdmluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y6YWN572u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLnNhdmluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g6YeN572u6KGo5Y2VCiAgICByZXNldEZvcm0oKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBwcmludGVySWQ6ICcnLAogICAgICAgIGNhdGVJZHM6IFtdCiAgICAgIH07CiAgICB9LAoKICAgIC8vIOWIt+aWsOaVsOaNrgogICAgcmVmcmVzaERhdGEoKSB7CiAgICAgIHRoaXMubG9hZERhdGEoKTsKICAgIH0sCgogICAgLy8g6I635Y+W5omT5Y2w5py65YiG57G75YWz6IGUCiAgICBnZXRQcmludGVyQ2F0ZWdvcmllcyhwcmludGVySWQpIHsKICAgICAgcmV0dXJuIHRoaXMucHJpbnRlckNhdGVnb3JpZXNNYXBbcHJpbnRlcklkXSB8fCBbXTsKICAgIH0sCgogICAgLy8g6I635Y+W5omT5Y2w5py65YiG57G75ZCN56ewCiAgICBnZXRQcmludGVyQ2F0ZWdvcnlOYW1lcyhwcmludGVySWQpIHsKICAgICAgY29uc3QgcHJpbnRlckNhdGVnb3JpZXMgPSB0aGlzLmdldFByaW50ZXJDYXRlZ29yaWVzKHByaW50ZXJJZCk7CiAgICAgIHJldHVybiBwcmludGVyQ2F0ZWdvcmllcy5tYXAocGMgPT4gewogICAgICAgIGNvbnN0IGNhdGVnb3J5ID0gdGhpcy5jYXRlZ29yeUxpc3QuZmluZChjID0+IGMuaWQgPT09IHBjLmNhdGVJZCk7CiAgICAgICAgcmV0dXJuIGNhdGVnb3J5ID8gY2F0ZWdvcnkubmFtZSA6IGDliIbnsbske3BjLmNhdGVJZH1gOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g6I635Y+W5bqX6ZO65ZCN56ewCiAgICBnZXRTdG9yZU5hbWUoc3RvcmVJZCkgewogICAgICBpZiAoIXN0b3JlSWQgfHwgc3RvcmVJZCA9PT0gMCkgcmV0dXJuICflhazlhbHmiYDmnIknOwogICAgICBjb25zdCBzdG9yZSA9IHRoaXMuc3RvcmVMaXN0LmZpbmQocyA9PiBzLmlkID09PSBzdG9yZUlkKTsKICAgICAgcmV0dXJuIHN0b3JlID8gc3RvcmUubmFtZSA6ICfmnKrnn6Xlupfpk7onOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["category-config.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "category-config.vue", "sourceRoot": "src/views/printer", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"page-header\">\n      <h2>打印机分类配置</h2>\n      <p>为标签打印机配置对应的商品分类，实现按分类智能打印</p>\n    </div>\n\n    <!-- 配置表单 -->\n    <el-card class=\"config-card\" shadow=\"hover\">\n      <div slot=\"header\">\n        <span>配置打印机分类关联</span>\n      </div>\n\n      <el-form :model=\"form\" label-width=\"120px\" class=\"config-form\">\n        <el-form-item label=\"选择打印机\" required>\n          <el-select\n            v-model=\"form.printerId\"\n            placeholder=\"请选择标签打印机\"\n            @change=\"onPrinterChange\"\n            style=\"width: 100%\"\n            clearable\n          >\n            <el-option\n              v-for=\"printer in labelPrinters\"\n              :key=\"printer.id\"\n              :label=\"`${printer.name} (${printer.sn})`\"\n              :value=\"printer.id\"\n            >\n              <span style=\"float: left\">{{ printer.name }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ printer.sn }}</span>\n            </el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"关联分类\" v-if=\"form.printerId\">\n          <div v-loading=\"categoryLoading\">\n            <el-checkbox-group v-model=\"form.cateIds\" class=\"category-group\">\n              <el-checkbox\n                v-for=\"category in categoryList\"\n                :key=\"category.id\"\n                :label=\"category.id\"\n                class=\"category-item\"\n              >\n                <span class=\"category-name\">{{ category.name }}</span>\n                <span class=\"category-desc\" v-if=\"category.description\">{{ category.description }}</span>\n              </el-checkbox>\n            </el-checkbox-group>\n          </div>\n        </el-form-item>\n\n        <el-form-item v-if=\"form.printerId\">\n          <el-button type=\"primary\" @click=\"saveConfig\" :loading=\"saving\" icon=\"el-icon-check\">\n            保存配置\n          </el-button>\n          <el-button @click=\"resetForm\" icon=\"el-icon-refresh\">\n            重置\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 当前配置展示 -->\n    <el-card class=\"status-card\" shadow=\"hover\">\n      <div slot=\"header\">\n        <span>当前配置状态</span>\n        <el-button\n          style=\"float: right; padding: 3px 0\"\n          type=\"text\"\n          @click=\"refreshData\"\n          :loading=\"loading\"\n        >\n          刷新\n        </el-button>\n      </div>\n\n      <div v-loading=\"loading\" class=\"printer-grid\">\n        <div\n          v-for=\"printer in labelPrinters\"\n          :key=\"printer.id\"\n          class=\"printer-card\"\n          :class=\"{ 'active': form.printerId === printer.id }\"\n          @click=\"selectPrinter(printer)\"\n        >\n          <div class=\"printer-header\">\n            <h4>{{ printer.name }}</h4>\n            <el-tag :type=\"printer.status === 'A' ? 'success' : 'danger'\" size=\"small\">\n              {{ printer.status === 'A' ? '启用' : '禁用' }}\n            </el-tag>\n          </div>\n\n          <div class=\"printer-info\">\n            <p><strong>编号:</strong> {{ printer.sn }}</p>\n            <p><strong>店铺:</strong> {{ getStoreName(printer.storeId) }}</p>\n          </div>\n\n          <div class=\"category-section\">\n            <p class=\"section-title\">关联分类:</p>\n            <div class=\"category-tags\">\n              <template v-if=\"getPrinterCategories(printer.id).length > 0\">\n                <el-tag\n                  v-for=\"categoryName in getPrinterCategoryNames(printer.id)\"\n                  :key=\"categoryName\"\n                  size=\"mini\"\n                  type=\"primary\"\n                  class=\"category-tag\"\n                >\n                  {{ categoryName }}\n                </el-tag>\n              </template>\n              <span v-else class=\"no-category\">未配置分类</span>\n            </div>\n          </div>\n\n          <div class=\"printer-actions\">\n            <el-button\n              size=\"mini\"\n              type=\"primary\"\n              @click.stop=\"selectPrinter(printer)\"\n              icon=\"el-icon-setting\"\n            >\n              配置\n            </el-button>\n          </div>\n        </div>\n      </div>\n\n      <div v-if=\"labelPrinters.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无标签打印机\">\n          <el-button type=\"primary\" @click=\"$router.push('/printer')\">\n            去添加打印机\n          </el-button>\n        </el-empty>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { getPrinterList, getCategoryList, savePrinterCategories, getPrinterCategories } from \"@/api/printer\";\n\nexport default {\n  name: \"PrinterCategoryConfig\",\n  data() {\n    return {\n      loading: false,\n      saving: false,\n      categoryLoading: false,\n\n      // 表单数据\n      form: {\n        printerId: '',\n        cateIds: []\n      },\n\n      // 基础数据\n      labelPrinters: [],\n      categoryList: [],\n      storeList: [],\n      printerCategoriesMap: {}\n    };\n  },\n\n  created() {\n    this.loadData();\n  },\n\n  methods: {\n    // 加载所有数据\n    async loadData() {\n      this.loading = true;\n      try {\n        await Promise.all([\n          this.loadPrinters(),\n          this.loadCategories()\n        ]);\n        await this.loadAllPrinterCategories();\n      } catch (error) {\n        console.error('加载数据失败:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 加载打印机列表\n    loadPrinters() {\n      return getPrinterList({ type: 'LABEL' }).then(response => {\n        this.labelPrinters = response.data.paginationResponse.content.filter(p => p.type === 'LABEL');\n        this.storeList = response.data.storeList || [];\n      });\n    },\n\n    // 加载分类列表\n    loadCategories() {\n      return getCategoryList().then(response => {\n        this.categoryList = response.data || [];\n      });\n    },\n\n    // 加载所有打印机的分类关联\n    async loadAllPrinterCategories() {\n      this.printerCategoriesMap = {};\n\n      for (const printer of this.labelPrinters) {\n        try {\n          const response = await getPrinterCategories(printer.id);\n          this.$set(this.printerCategoriesMap, printer.id, response.data || []);\n        } catch (error) {\n          console.error(`加载打印机${printer.id}分类关联失败:`, error);\n        }\n      }\n    },\n\n    // 选择打印机\n    selectPrinter(printer) {\n      this.form.printerId = printer.id;\n      this.onPrinterChange();\n    },\n\n    // 打印机变化事件\n    onPrinterChange() {\n      if (!this.form.printerId) {\n        this.form.cateIds = [];\n        return;\n      }\n\n      this.categoryLoading = true;\n      getPrinterCategories(this.form.printerId).then(response => {\n        const printerCategories = response.data || [];\n        this.form.cateIds = printerCategories.map(pc => pc.cateId);\n        this.categoryLoading = false;\n      }).catch(error => {\n        console.error('加载打印机分类关联失败:', error);\n        this.categoryLoading = false;\n      });\n    },\n\n    // 保存配置\n    saveConfig() {\n      if (!this.form.printerId) {\n        this.$message.warning('请先选择打印机');\n        return;\n      }\n\n      this.saving = true;\n\n      const data = {\n        printerId: this.form.printerId,\n        cateIds: this.form.cateIds\n      };\n\n      savePrinterCategories(data).then(response => {\n        this.$message.success('配置保存成功');\n        this.loadAllPrinterCategories();\n        this.saving = false;\n      }).catch(error => {\n        console.error('保存配置失败:', error);\n        this.saving = false;\n      });\n    },\n\n    // 重置表单\n    resetForm() {\n      this.form = {\n        printerId: '',\n        cateIds: []\n      };\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.loadData();\n    },\n\n    // 获取打印机分类关联\n    getPrinterCategories(printerId) {\n      return this.printerCategoriesMap[printerId] || [];\n    },\n\n    // 获取打印机分类名称\n    getPrinterCategoryNames(printerId) {\n      const printerCategories = this.getPrinterCategories(printerId);\n      return printerCategories.map(pc => {\n        const category = this.categoryList.find(c => c.id === pc.cateId);\n        return category ? category.name : `分类${pc.cateId}`;\n      });\n    },\n\n    // 获取店铺名称\n    getStoreName(storeId) {\n      if (!storeId || storeId === 0) return '公共所有';\n      const store = this.storeList.find(s => s.id === storeId);\n      return store ? store.name : '未知店铺';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px 0;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.page-header h2 {\n  margin: 0 0 8px 0;\n  color: #303133;\n}\n\n.page-header p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.config-card,\n.status-card {\n  margin-bottom: 20px;\n}\n\n.config-form {\n  max-width: 800px;\n}\n\n.category-group {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n  padding: 16px;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  background-color: #fafafa;\n}\n\n.category-item {\n  margin: 0 !important;\n  padding: 12px;\n  border: 1px solid #e0e0e0;\n  border-radius: 6px;\n  background: white;\n  transition: all 0.3s;\n  cursor: pointer;\n}\n\n.category-item:hover {\n  background: #f0f8ff;\n  border-color: #409eff;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n}\n\n.category-name {\n  display: block;\n  font-weight: 500;\n  color: #303133;\n}\n\n.category-desc {\n  display: block;\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.printer-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 20px;\n}\n\n.printer-card {\n  border: 2px solid #e4e7ed;\n  border-radius: 8px;\n  padding: 20px;\n  background: white;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.printer-card:hover {\n  border-color: #409eff;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.printer-card.active {\n  border-color: #409eff;\n  background: #f0f8ff;\n}\n\n.printer-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.printer-header h4 {\n  margin: 0;\n  color: #303133;\n}\n\n.printer-info {\n  margin-bottom: 16px;\n}\n\n.printer-info p {\n  margin: 4px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.category-section {\n  margin-bottom: 16px;\n}\n\n.section-title {\n  margin: 0 0 8px 0;\n  font-weight: 500;\n  color: #303133;\n  font-size: 14px;\n}\n\n.category-tags {\n  min-height: 24px;\n}\n\n.category-tag {\n  margin: 2px 4px 2px 0;\n}\n\n.no-category {\n  color: #c0c4cc;\n  font-style: italic;\n  font-size: 13px;\n}\n\n.printer-actions {\n  text-align: right;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 0;\n}\n</style>\n"]}]}