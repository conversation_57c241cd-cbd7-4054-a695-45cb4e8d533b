{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\index.vue", "mtime": 1754838939888}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEdvb2RzTGlzdCwgdXBkYXRlR29vZHNTdGF0dXMsIGNvcHlHb29kcyB9IGZyb20gIkAvYXBpL2dvb2RzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJHb29kc0luZGV4IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g5aSN5Yi25a+56K+d5qGGCiAgICAgIGNvcHlEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgY29weUZvcm06IHsKICAgICAgICB0YXJnZXRTdG9yZUlkOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgc3RvcmVJZDogdGhpcy4kc3RvcmUuZ2V0dGVycy5zdG9yZUlkLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlupfpk7rliJfooagKICAgICAgc3RvcmVPcHRpb25zOiBbXSwKICAgICAgLy8g5ZWG5ZOB57G75Z6LCiAgICAgIHR5cGVPcHRpb25zOiBbXSwKICAgICAgLy8g5ZWG5ZOB5YiG57G7CiAgICAgIGNhdGVMaXN0OiBbXSwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDpu5jorqTmjpLluo8KICAgICAgZGVmYXVsdFNvcnQ6IHtwcm9wOiAnc29ydCcsIG9yZGVyOiAnZGVzY2VuZGluZyd9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybTogeyBpZDogJycsIG5hbWU6ICcnLCBsb2dvOiAnJywgc29ydDogMCwgc3RhdHVzOiAiQSIgfSwKICAgICAgLy8g5LiK5Lyg5Zyw5Z2ACiAgICAgIHVwbG9hZEFjdGlvbjogcHJvY2Vzcy5lbnYuVlVFX0FQUF9TRVJWRVJfVVJMICsgJ2JhY2tlbmRBcGkvZmlsZS91cGxvYWQnLAogICAgICAvLyDpmpDol4/kuIrkvKAKICAgICAgaGlkZVVwbG9hZDogZmFsc2UsCiAgICAgIC8vIOS4iuS8oOaWh+S7tuWIl+ihqAogICAgICB1cGxvYWRGaWxlczogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHN0b3JlSWQ6ICcnLAogICAgICAgIGNhdGVJZDogJycsCiAgICAgICAgbmFtZTogJycsCiAgICAgICAgaXNTaW5nbGVTcGVjOiAnJywKICAgICAgICBnb29kc05vOiAnJywKICAgICAgICBzdG9jazogJycsCiAgICAgICAgc3RhdHVzOiAnJwogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBuYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAyMDAsIG1lc3NhZ2U6ICflkI3np7Dplb/luqblv4Xpobvku4vkuo4yIOWSjCAyMDAg5LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGxvZ286IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35LiK5Lyg5Zu+54mHIiwgdHJpZ2dlcjogImJsdXIiIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDmn6Xor6LllYblk4HliJfooagKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGdldEdvb2RzTGlzdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKCByZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLmxpc3QgPSByZXNwb25zZS5kYXRhLnBhZ2luYXRpb25SZXNwb25zZS5jb250ZW50OwogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEucGFnaW5hdGlvblJlc3BvbnNlLnRvdGFsRWxlbWVudHM7CiAgICAgICAgICB0aGlzLnR5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YS50eXBlTGlzdDsKICAgICAgICAgIHRoaXMuc3RvcmVPcHRpb25zID0gcmVzcG9uc2UuZGF0YS5zdG9yZUxpc3Q7CiAgICAgICAgICB0aGlzLmNhdGVMaXN0ID0gcmVzcG9uc2UuZGF0YS5jYXRlTGlzdDsKICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0KICAgICAgKS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5ZWG5ZOB5YiX6KGo5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWVhuWTgeWIl+ihqOWksei0pTogJyArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSkKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5pCc57Si5oyJ6ZKu5pON5L2cCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g6YeN572u5oyJ6ZKu5pON5L2cCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuJHJlZnMudGFibGVzLnNvcnQodGhpcy5kZWZhdWx0U29ydC5wcm9wLCB0aGlzLmRlZmF1bHRTb3J0Lm9yZGVyKQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g54q25oCB5L+u5pS5CiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PSAiQSIgPyAi5LiK5p62IiA6ICLkuIvmnrYiOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEnICsgdGV4dCArICciJyArIHJvdy5uYW1lICsgJyLlkJfvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiB1cGRhdGVHb29kc1N0YXR1cyhyb3cuaWQsIHJvdy5zdGF0dXMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign54q25oCB5pu05paw5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHRleHQgKyAi5aSx6LSlOiAiICsgKGVycm9yLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKTsKICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIk4iID8gIkEiIDogIk4iOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLy8g5o6S5bqP6Kem5Y+R5LqL5Lu2CiAgICBoYW5kbGVTb3J0Q2hhbmdlKGNvbHVtbiwgcHJvcCwgb3JkZXIpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcmRlckJ5Q29sdW1uID0gY29sdW1uLnByb3A7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXNBc2MgPSBjb2x1bW4ub3JkZXI7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOaWsOWinuaMiemSruaTjeS9nAogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCggeyBwYXRoOiAnL2dvb2RzL2dvb2RzL2FkZCcgfSApCiAgICB9LAogICAgaGFuZGxlQ29weSgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHlpI3liLbnmoTllYblk4EiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5jb3B5Rm9ybS50YXJnZXRTdG9yZUlkID0gdW5kZWZpbmVkOwogICAgICB0aGlzLmNvcHlEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDmj5DkuqTlpI3liLYKICAgIHN1Ym1pdENvcHkoKSB7CiAgICAgIGlmICghdGhpcy5jb3B5Rm9ybS50YXJnZXRTdG9yZUlkKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeebruagh+W6l+mTuiIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjb3B5R29vZHModGhpcy5pZHMsIHRoaXMuY29weUZvcm0udGFyZ2V0U3RvcmVJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5aSN5Yi25oiQ5YqfIik7CiAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgICAgIHRoaXMuY29weURpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WkjeWItuWksei0pTonLCBlcnJvcikKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5aSN5Yi25aSx6LSlOiAiICsgKGVycm9yLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKTsKICAgICAgfSk7CgogICAgfSwKICAgIC8vIOS/ruaUueaMiemSruaTjeS9nAogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCggeyBwYXRoOiAnL2dvb2RzL2dvb2RzL2VkaXQ/Z29vZHNJZD0nICsgcm93LmlkIH0gKQogICAgfSwKICAgIC8vIOWIoOmZpOaMiemSruaTjeS9nAogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBuYW1lID0gcm93Lm5hbWUKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5ZWG5ZOBIicgKyBuYW1lICsgJyLvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiB1cGRhdGVHb29kc1N0YXR1cyhyb3cuaWQsICdEJyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yig6Zmk5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLliKDpmaTlpLHotKU6ICIgKyAoZXJyb3IubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzIChmaWxlKSB7CiAgICAgIHRoaXMuZm9ybS5sb2dvID0gZmlsZS5kYXRhLmZpbGVOYW1lCiAgICB9LAogICAgaGFuZGxlUmVtb3ZlIChmaWxlLCBmaWxlTGlzdCkgewogICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLmhpZGVVcGxvYWQgPSBmaWxlTGlzdC5sZW5ndGggPiAwCiAgICAgIH0sIDUyMCkKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0NA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/goods/goods", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" class=\"main-search\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"商品名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入商品名称\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"所属店铺\" prop=\"store\">\n        <el-select\n          v-model=\"queryParams.storeId\"\n          placeholder=\"所属店铺\"\n          clearable\n          style=\"width: 180px\"\n        >\n          <el-option :key=\"0\" label=\"公共商品\" v-if=\"!storeId\" :value=\"0\"/>\n          <el-option v-for=\"storeInfo in storeOptions\" :key=\"storeInfo.id\" :label=\"storeInfo.name\" :value=\"storeInfo.id\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"商品分类\" prop=\"cateId\">\n        <el-select class=\"input\" v-model=\"queryParams.cateId\" clearable placeholder=\"请选择商品分类\">\n          <el-option\n            v-for=\"item in cateList\"\n            :key=\"item.id\"\n            :label=\"item.name\"\n            :value=\"item.id\"\n            :disabled=\"item.status !== 'A'\"\n          ></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"商品条码\" prop=\"goodsNo\">\n        <el-input\n          v-model=\"queryParams.goodsNo\"\n          placeholder=\"请输入商品条码\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"商品类型\" prop=\"type\">\n        <el-select class=\"input\" v-model=\"queryParams.type\" clearable placeholder=\"请选择商品类型\">\n          <el-option\n            v-for=\"item in typeOptions\"\n            :key=\"item.key\"\n            :label=\"item.name\"\n            :value=\"item.key\"\n          ></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"规格类型\" prop=\"isSingleSpec\">\n        <el-select\n          v-model=\"queryParams.isSingleSpec\"\n          placeholder=\"规格类型\"\n          clearable\n          style=\"width: 120px\"\n        >\n          <el-option key=\"Y\" label=\"单规格\" value=\"Y\"/>\n          <el-option key=\"N\" label=\"多规格\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"上架状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"上架状态\"\n          clearable\n          style=\"width: 120px\"\n        >\n          <el-option key=\"A\" label=\"上架\" value=\"A\"/>\n          <el-option key=\"N\" label=\"下架\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"库存状态\" prop=\"stock\">\n        <el-select\n          v-model=\"queryParams.stock\"\n          placeholder=\"库存状态\"\n          clearable\n          style=\"width: 120px\"\n        >\n          <el-option key=\"Y\" label=\"有库存\" value=\"Y\"/>\n          <el-option key=\"N\" label=\"无库存\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['goods:goods:add']\"\n        >新增</el-button>\n\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleCopy\"\n          v-hasPermi=\"['goods:goods:add']\"\n        >批量复制</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\n      <el-table-column type=\"selection\" width=\"55\" />\n      <el-table-column label=\"ID\" prop=\"id\" width=\"50\"/>\n      <el-table-column label=\"所属店铺\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.storeInfo\">{{ scope.row.storeInfo.name }}</span>\n          <span v-else>公共所有</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"商品名称\" align=\"center\" min-width=\"300\" prop=\"name\" />\n      <el-table-column label=\"主图\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n           <img class=\"list-img\" :src=\"scope.row.logo\">\n        </template>\n      </el-table-column>\n      <el-table-column label=\"商品条码\" align=\"center\" prop=\"goodsNo\" width=\"140\"/>\n      <el-table-column label=\"剩余库存\" align=\"center\" prop=\"stock\" width=\"100\"/>\n      <el-table-column label=\"所属分类\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.cateInfo.name }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"价格\" align=\"center\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.price\">{{ scope.row.price.toFixed(2) }}</span>\n          <span v-else>0.00</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"上架状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"A\"\n            inactive-value=\"N\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"时间段配置\" align=\"center\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.hasTimeConfig ? 'success' : 'info'\">\n            {{ scope.row.hasTimeConfig ? '已配置' : '未配置' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" width=\"150\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"更新时间\" align=\"center\" width=\"150\" prop=\"updateTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.updateTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"120\" fixed='right'>\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            v-hasPermi=\"['goods:goods:edit']\"\n            @click=\"handleUpdate(scope.row)\"\n            v-if=\"storeId == scope.row.storeId || storeId == 0\"\n          >编辑</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            v-hasPermi=\"['goods:goods:edit']\"\n            v-if=\"storeId == scope.row.storeId || storeId == 0\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page-sizes=\"[10, 20, 50, 100, 200]\"\n      :page.sync=\"queryParams.page\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 复制商品对话框 -->\n    <el-dialog title=\"批量复制商品\" :visible.sync=\"copyDialogVisible\" width=\"500px\" append-to-body>\n      <el-form :model=\"copyForm\" label-width=\"80px\">\n        <el-form-item label=\"目标店铺\" prop=\"targetStoreId\">\n          <el-select v-model=\"copyForm.targetStoreId\" placeholder=\"请选择目标店铺\" style=\"width: 100%\">\n            <el-option\n              v-for=\"item in storeOptions\"\n              :key=\"item.id\"\n              :label=\"item.name\"\n              :value=\"item.id\"\n            />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitCopy\">确 定</el-button>\n        <el-button @click=\"copyDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getGoodsList, updateGoodsStatus, copyGoods } from \"@/api/goods\";\nexport default {\n  name: \"GoodsIndex\",\n  data() {\n    return {\n      // 复制对话框\n      copyDialogVisible: false,\n      copyForm: {\n        targetStoreId: undefined\n      },\n      storeId: this.$store.getters.storeId,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 店铺列表\n      storeOptions: [],\n      // 商品类型\n      typeOptions: [],\n      // 商品分类\n      cateList: [],\n      // 是否显示弹出层\n      open: false,\n      // 默认排序\n      defaultSort: {prop: 'sort', order: 'descending'},\n      // 表单参数\n      form: { id: '', name: '', logo: '', sort: 0, status: \"A\" },\n      // 上传地址\n      uploadAction: process.env.VUE_APP_SERVER_URL + 'backendApi/file/upload',\n      // 隐藏上传\n      hideUpload: false,\n      // 上传文件列表\n      uploadFiles: [],\n      // 查询参数\n      queryParams: {\n        page: 1,\n        pageSize: 10,\n        storeId: '',\n        cateId: '',\n        name: '',\n        isSingleSpec: '',\n        goodsNo: '',\n        stock: '',\n        status: ''\n      },\n      // 表单校验\n      rules: {\n        name: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '名称长度必须介于2 和 200 之间', trigger: 'blur' }\n        ],\n        logo: [{ required: true, message: \"请上传图片\", trigger: \"blur\" }]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 查询商品列表\n    getList() {\n      this.loading = true;\n      getGoodsList(this.queryParams).then( response => {\n          this.list = response.data.paginationResponse.content;\n          this.total = response.data.paginationResponse.totalElements;\n          this.typeOptions = response.data.typeList;\n          this.storeOptions = response.data.storeList;\n          this.cateList = response.data.cateList;\n          this.loading = false;\n        }\n      ).catch(error => {\n        console.error('获取商品列表失败:', error)\n        this.$message.error('获取商品列表失败: ' + (error.message || '未知错误'))\n        this.loading = false;\n      });\n    },\n    // 搜索按钮操作\n    handleQuery() {\n      this.queryParams.page = 1;\n      this.getList();\n    },\n    // 重置按钮操作\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\n      this.handleQuery();\n    },\n    // 状态修改\n    handleStatusChange(row) {\n      let text = row.status == \"A\" ? \"上架\" : \"下架\";\n      this.$modal.confirm('确认要' + text + '\"' + row.name + '\"吗？').then(function() {\n        return updateGoodsStatus(row.id, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function(error) {\n        console.error('状态更新失败:', error)\n        this.$modal.msgError(text + \"失败: \" + (error.message || '未知错误'));\n        row.status = row.status === \"N\" ? \"A\" : \"N\";\n      });\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.multiple = !selection.length\n    },\n    // 排序触发事件\n    handleSortChange(column, prop, order) {\n      this.queryParams.orderByColumn = column.prop;\n      this.queryParams.isAsc = column.order;\n      this.getList();\n    },\n    // 新增按钮操作\n    handleAdd() {\n      this.$router.push( { path: '/goods/goods/add' } )\n    },\n    handleCopy() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要复制的商品\");\n        return;\n      }\n      this.copyForm.targetStoreId = undefined;\n      this.copyDialogVisible = true;\n    },\n    // 提交复制\n    submitCopy() {\n      if (!this.copyForm.targetStoreId) {\n        this.$modal.msgError(\"请选择目标店铺\");\n        return;\n      }\n      copyGoods(this.ids, this.copyForm.targetStoreId).then(response => {\n        this.$modal.msgSuccess(\"复制成功\");\n        this.handleQuery();\n        this.copyDialogVisible = false;\n      }).catch(error => {\n        console.error('复制失败:', error)\n        this.$modal.msgError(\"复制失败: \" + (error.message || '未知错误'));\n      });\n\n    },\n    // 修改按钮操作\n    handleUpdate(row) {\n      this.$router.push( { path: '/goods/goods/edit?goodsId=' + row.id } )\n    },\n    // 删除按钮操作\n    handleDelete(row) {\n      const name = row.name\n      this.$modal.confirm('是否确认删除商品\"' + name + '\"？').then(function() {\n        return updateGoodsStatus(row.id, 'D');\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(error => {\n        console.error('删除失败:', error)\n        this.$modal.msgError(\"删除失败: \" + (error.message || '未知错误'));\n      });\n    },\n    handleUploadSuccess (file) {\n      this.form.logo = file.data.fileName\n    },\n    handleRemove (file, fileList) {\n      setTimeout(() => {\n        this.hideUpload = fileList.length > 0\n      }, 520)\n    }\n  }\n};\n</script>\n\n"]}]}