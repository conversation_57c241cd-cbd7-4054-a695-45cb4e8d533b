
245f68fa3f5a851a8012ee537b1c3a69d2bf4ec5	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"9ca2e37670349237ab0065768d1c659f\"}","integrity":"sha512-X2Cmu6MxiIqHmY0fsTt9NJ5BcZZ4zqI9bu0zfDKTRNRos0ArZXEvv47AYbLK3PhQv85MpsiMtAH9OxrcILo4Vw==","time":1754921568261,"size":6667632}