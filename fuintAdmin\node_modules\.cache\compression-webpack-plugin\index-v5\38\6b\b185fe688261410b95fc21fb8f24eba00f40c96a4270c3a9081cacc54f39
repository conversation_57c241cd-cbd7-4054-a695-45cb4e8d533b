
4e4c07a05593625b08e39d34ba4181506246e0da	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"181d542243977216a6e2829188ca8343\"}","integrity":"sha512-hymhQqMDyZVcpiqYRKthx4sADacMl+Vqc3NIU50R1CwH2PJcier1EFfm9eaGLDlSijZoKRZBl7kSeYa3VwCF9g==","time":1754921412763,"size":2946153}