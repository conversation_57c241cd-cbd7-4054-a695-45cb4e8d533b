
8493a5d7379a29b5f44d406fa52db5857aeed7cd	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"fcde3afab47a9515979fe83742f36646\"}","integrity":"sha512-bwXcR8m83V1MT2R15Q54TsM1sSJ9SMUVspjdHyRWgLfXGIBpvSLGs8MfKehr4eYGml4HSKdJLNGMURj9aGZ0zw==","time":1754921522723,"size":2882480}