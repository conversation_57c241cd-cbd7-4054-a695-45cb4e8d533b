
f274eea650e1776955bce9a493be3b4dc746070d	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"932ed11006c60fb37b5fa8f31a4236be\"}","integrity":"sha512-zDq0stHe5/qrO+aFcMaiiw6VJxG6/PanLiZ4hXiKtmiTxYFPrnsA9Tytcsug/VJzd4E/2Z2LuU2JRoU5e3YG1g==","time":1754921983418,"size":2883147}