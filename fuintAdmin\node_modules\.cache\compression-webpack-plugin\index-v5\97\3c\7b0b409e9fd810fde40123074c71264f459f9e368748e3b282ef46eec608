
4bcbbabf296b7dbebb3833076cb9f5fe371fbca4	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.dfcbff2c0670f64cc464.hot-update.js\",\"contentHash\":\"8178d22b22396e0ed9740f6fbc01aa39\"}","integrity":"sha512-N2p5QwA7cxxtaLv4A8DWD5uDx6FRpFGNZsy81czOIlJKY6aSVB/E7iWyRNr8F8joCw/+SO3rkPIZLmoAwhOfow==","time":1754921138076,"size":18830}