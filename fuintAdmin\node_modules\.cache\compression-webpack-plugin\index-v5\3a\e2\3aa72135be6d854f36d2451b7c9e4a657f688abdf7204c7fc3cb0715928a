
d079e45602809a5574afd6b9caad1269be546a5d	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"4d788a17403e04307c494592703edbe7\"}","integrity":"sha512-mBtpalcwxXjkc2XKTPvV3mgXOXRMjHmrmLPAL6fdpZEZPM1bkhB0TDdh/bPwejUs2ErkiIVlrcaK8lze5JSwpQ==","time":1754921083066,"size":2946315}