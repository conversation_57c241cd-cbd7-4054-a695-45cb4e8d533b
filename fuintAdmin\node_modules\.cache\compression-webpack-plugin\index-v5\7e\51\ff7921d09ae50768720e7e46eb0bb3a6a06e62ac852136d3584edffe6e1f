
c590b24d0a027337ef297526abe49aa941ea743c	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.4736218215ff1c322578.hot-update.js\",\"contentHash\":\"7157e2a652cf2e74c60acd78a43682e0\"}","integrity":"sha512-WJt+p0ZflYrSRVbX47ifg4Yt6oVIrx9JBSyqtFNBbHWSUvliIwbQkooZxlWJkjXHiAOQRZVd8XTNDaHZAWR85g==","time":1754921082923,"size":1322}