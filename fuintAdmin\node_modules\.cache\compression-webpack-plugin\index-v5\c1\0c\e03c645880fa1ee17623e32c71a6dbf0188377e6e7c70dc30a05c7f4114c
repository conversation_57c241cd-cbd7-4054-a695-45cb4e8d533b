
7af55ab10ae1c5565898b974984e547069b36346	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"html\\u002Fie.html\",\"contentHash\":\"5074f6c3c192f43181bb3bfc2a53f39d\"}","integrity":"sha512-ZFCX66Xx/TcfPuNeRHSYiG9AxGcUAFBmutfP3tba+/ybFBiJMV9GkWEFSALPER9LAzbdZR9N4G8VwOM40otT0A==","time":1754920241785,"size":62143}