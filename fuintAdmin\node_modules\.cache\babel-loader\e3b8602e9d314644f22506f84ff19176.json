{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigList.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigList.vue", "mtime": 1754839801250}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getGoodsTimeConfigs", "saveGoodsTimeConfig", "updateGoodsTimeConfig", "deleteGoodsTimeConfig", "TimeConfigForm", "name", "components", "props", "goodsId", "type", "String", "Number", "required", "data", "loading", "configList", "dialogVisible", "dialogTitle", "currentConfig", "isEdit", "watch", "handler", "newVal", "loadConfigs", "immediate", "methods", "_this", "then", "response", "catch", "error", "console", "$message", "message", "handleAdd", "configType", "weekDays", "customDates", "timeRanges", "startTime", "endTime", "status", "info", "handleEdit", "row", "_objectSpread", "JSON", "parse", "config<PERSON><PERSON><PERSON>", "handleDelete", "_this2", "$confirm", "id", "success", "handleStatusChange", "_this3", "statusText", "concat", "handleSave", "_this4", "$refs", "timeConfigForm", "validate", "formData", "stringify", "savePromise", "handleDialogClose", "resetForm", "getConfigTypeTag", "map", "DAILY", "WEEKLY", "CUSTOM", "getConfigTypeText", "formatWeekDays", "weekDaysStr", "weekMap", "day", "join", "_unused", "formatCustomDates", "datesStr", "dates", "_unused2", "parseTimeRanges", "timeRangesStr", "_unused3", "parseTime", "time", "$moment", "format", "formatTimeRange"], "sources": ["src/views/goods/components/TimeConfigList.vue"], "sourcesContent": ["<template>\n  <div class=\"time-config-list\">\n    <div class=\"header-actions\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增时间段配置</el-button>\n      <div class=\"form-tip\">提示：可以为商品设置多个时间段配置，如每日通用、按周设置或自定义日期</div>\n    </div>\n\n    <el-table :data=\"configList\" v-loading=\"loading\" style=\"width: 100%\">\n      <el-table-column prop=\"configType\" label=\"配置类型\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getConfigTypeTag(scope.row.configType)\">\n            {{ getConfigTypeText(scope.row.configType) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"configValue\" label=\"配置值\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div v-if=\"scope.row.configType === 'DAILY'\">\n            <span>每日通用</span>\n          </div>\n          <div v-else-if=\"scope.row.configType === 'WEEKLY'\">\n            <span>{{ formatWeekDays(scope.row.configValue) }}</span>\n          </div>\n          <div v-else-if=\"scope.row.configType === 'CUSTOM'\">\n            <span>{{ formatCustomDates(scope.row.configValue) }}</span>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"timeRanges\" label=\"时间段\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div v-for=\"(range, index) in parseTimeRanges(scope.row.timeRanges)\" :key=\"index\">\n            <el-tag size=\"mini\">{{ formatTimeRange(range.startTime, range.endTime) }}</el-tag>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"A\"\n            inactive-value=\"N\"\n            @change=\"handleStatusChange(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n\n      <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\">\n        <template slot-scope=\"scope\">\n          {{ parseTime(scope.row.createTime) }}\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"mini\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEdit(scope.row)\"\n          >编辑</el-button>\n          <el-button\n            type=\"text\"\n            size=\"mini\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      width=\"600px\"\n      @close=\"handleDialogClose\"\n    >\n      <TimeConfigForm\n        ref=\"timeConfigForm\"\n        v-model=\"currentConfig\"\n        :goods-id=\"goodsId\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleSave\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getGoodsTimeConfigs, saveGoodsTimeConfig, updateGoodsTimeConfig, deleteGoodsTimeConfig } from '@/api/goodsTimeConfig'\nimport TimeConfigForm from './TimeConfigForm'\n\nexport default {\n  name: 'TimeConfigList',\n  components: {\n    TimeConfigForm\n  },\n  props: {\n    goodsId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      configList: [],\n      dialogVisible: false,\n      dialogTitle: '',\n      currentConfig: {},\n      isEdit: false\n    }\n  },\n  watch: {\n    goodsId: {\n      handler(newVal) {\n        if (newVal) {\n          this.loadConfigs()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    loadConfigs() {\n      if (!this.goodsId) return\n\n      this.loading = true\n      getGoodsTimeConfigs(this.goodsId).then(response => {\n        this.configList = response.data || []\n        this.loading = false\n      }).catch(error => {\n        console.error('加载时间段配置失败:', error)\n        this.$message.error('加载时间段配置失败: ' + (error.message || '未知错误'))\n        this.loading = false\n      })\n    },\n\n    handleAdd() {\n      this.isEdit = false\n      this.dialogTitle = '新增时间段配置'\n      this.currentConfig = {\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      }\n      this.dialogVisible = true\n\n      // 提示用户可以开始配置\n      this.$message.info('请配置时间段信息')\n    },\n\n    handleEdit(row) {\n      this.isEdit = true\n      this.dialogTitle = '编辑时间段配置'\n      this.currentConfig = {\n        ...row,\n        weekDays: row.configType === 'WEEKLY' ? JSON.parse(row.configValue || '[]') : [],\n        customDates: row.configType === 'CUSTOM' ? JSON.parse(row.configValue || '[]') : [],\n        timeRanges: JSON.parse(row.timeRanges || '[]')\n      }\n      this.dialogVisible = true\n\n      // 提示用户可以开始编辑\n      this.$message.info('请编辑时间段配置信息')\n    },\n\n    handleDelete(row) {\n      this.$confirm('确认删除该时间段配置吗？', '提示', {\n        type: 'warning'\n      }).then(() => {\n        deleteGoodsTimeConfig(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.loadConfigs()\n\n          // 提示用户配置已删除\n          this.$message.info('时间段配置已删除')\n        }).catch(error => {\n          console.error('删除失败:', error)\n          this.$message.error('删除失败: ' + (error.message || '未知错误'))\n        })\n      }).catch(() => {\n        // 用户取消删除\n        this.$message.info('已取消删除操作')\n      })\n    },\n\n    handleStatusChange(row) {\n      const statusText = row.status === 'A' ? '启用' : '禁用'\n      updateGoodsTimeConfig({\n        id: row.id,\n        status: row.status\n      }).then(() => {\n        this.$message.success(`${statusText}成功`)\n\n        // 提示用户状态已更新\n        this.$message.info(`时间段配置已${statusText}`)\n      }).catch(error => {\n        console.error('状态更新失败:', error)\n        this.$message.error(`${statusText}失败: ` + (error.message || '未知错误'))\n        row.status = row.status === 'A' ? 'N' : 'A'\n      })\n    },\n\n    handleSave() {\n      this.$refs.timeConfigForm.validate().then(formData => {\n        const data = {\n          ...formData,\n          configValue: formData.configType === 'WEEKLY'\n            ? JSON.stringify(formData.weekDays)\n            : formData.configType === 'CUSTOM'\n              ? JSON.stringify(formData.customDates)\n              : '',\n          timeRanges: JSON.stringify(formData.timeRanges)\n        }\n\n        const savePromise = this.isEdit\n          ? updateGoodsTimeConfig(data)\n          : saveGoodsTimeConfig(data)\n\n        savePromise.then(() => {\n          this.$message.success('保存成功')\n          this.dialogVisible = false\n          this.loadConfigs()\n\n          // 提示用户配置已更新\n          this.$message.info('时间段配置已更新')\n        }).catch(error => {\n          console.error('保存失败:', error)\n          this.$message.error('保存失败: ' + (error.message || '未知错误'))\n        })\n      }).catch(() => {\n        // 表单验证失败\n      })\n    },\n\n    handleDialogClose() {\n      this.$refs.timeConfigForm && this.$refs.timeConfigForm.resetForm()\n    },\n\n    getConfigTypeTag(type) {\n      const map = {\n        DAILY: 'success',\n        WEEKLY: 'warning',\n        CUSTOM: 'info'\n      }\n      return map[type] || 'default'\n    },\n\n    getConfigTypeText(type) {\n      const map = {\n        DAILY: '每日通用',\n        WEEKLY: '按周设置',\n        CUSTOM: '自定义日期'\n      }\n      return map[type] || type\n    },\n\n    formatWeekDays(weekDaysStr) {\n      try {\n        const weekDays = JSON.parse(weekDaysStr || '[]')\n        const weekMap = {\n          1: '周一',\n          2: '周二',\n          3: '周三',\n          4: '周四',\n          5: '周五',\n          6: '周六',\n          7: '周日'\n        }\n        return weekDays.map(day => weekMap[day]).join('、')\n      } catch {\n        return weekDaysStr\n      }\n    },\n\n    formatCustomDates(datesStr) {\n      try {\n        const dates = JSON.parse(datesStr || '[]')\n        return dates.join('、')\n      } catch {\n        return datesStr\n      }\n    },\n\n    parseTimeRanges(timeRangesStr) {\n      try {\n        return JSON.parse(timeRangesStr || '[]')\n      } catch {\n        return []\n      }\n    },\n\n    parseTime(time) {\n      if (!time) return ''\n      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss')\n    },\n\n    // 格式化时间段显示\n    formatTimeRange(startTime, endTime) {\n      if (!startTime || !endTime) return ''\n      return `${startTime} - ${endTime}`\n    }\n  }\n}\n</script>\n\n<style scoped>\n.time-config-list {\n  padding: 20px;\n}\n\n.header-actions {\n  margin-bottom: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8FA,SAAAA,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA;AACA,OAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF,cAAA,EAAAA;EACA;EACAG,KAAA;IACAC,OAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,aAAA;MACAC,WAAA;MACAC,aAAA;MACAC,MAAA;IACA;EACA;EACAC,KAAA;IACAZ,OAAA;MACAa,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACAF,WAAA,WAAAA,YAAA;MAAA,IAAAG,KAAA;MACA,UAAAlB,OAAA;MAEA,KAAAM,OAAA;MACAd,mBAAA,MAAAQ,OAAA,EAAAmB,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAX,UAAA,GAAAa,QAAA,CAAAf,IAAA;QACAa,KAAA,CAAAZ,OAAA;MACA,GAAAe,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;QACAJ,KAAA,CAAAM,QAAA,CAAAF,KAAA,kBAAAA,KAAA,CAAAG,OAAA;QACAP,KAAA,CAAAZ,OAAA;MACA;IACA;IAEAoB,SAAA,WAAAA,UAAA;MACA,KAAAf,MAAA;MACA,KAAAF,WAAA;MACA,KAAAC,aAAA;QACAV,OAAA,OAAAA,OAAA;QACA2B,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,UAAA;UAAAC,SAAA;UAAAC,OAAA;QAAA;QACAC,MAAA;MACA;MACA,KAAAzB,aAAA;;MAEA;MACA,KAAAgB,QAAA,CAAAU,IAAA;IACA;IAEAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAzB,MAAA;MACA,KAAAF,WAAA;MACA,KAAAC,aAAA,GAAA2B,aAAA,CAAAA,aAAA,KACAD,GAAA;QACAR,QAAA,EAAAQ,GAAA,CAAAT,UAAA,gBAAAW,IAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,WAAA;QACAX,WAAA,EAAAO,GAAA,CAAAT,UAAA,gBAAAW,IAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,WAAA;QACAV,UAAA,EAAAQ,IAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAN,UAAA;MAAA,EACA;MACA,KAAAtB,aAAA;;MAEA;MACA,KAAAgB,QAAA,CAAAU,IAAA;IACA;IAEAO,YAAA,WAAAA,aAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,KAAAC,QAAA;QACA1C,IAAA;MACA,GAAAkB,IAAA;QACAxB,qBAAA,CAAAyC,GAAA,CAAAQ,EAAA,EAAAzB,IAAA;UACAuB,MAAA,CAAAlB,QAAA,CAAAqB,OAAA;UACAH,MAAA,CAAA3B,WAAA;;UAEA;UACA2B,MAAA,CAAAlB,QAAA,CAAAU,IAAA;QACA,GAAAb,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;UACAoB,MAAA,CAAAlB,QAAA,CAAAF,KAAA,aAAAA,KAAA,CAAAG,OAAA;QACA;MACA,GAAAJ,KAAA;QACA;QACAqB,MAAA,CAAAlB,QAAA,CAAAU,IAAA;MACA;IACA;IAEAY,kBAAA,WAAAA,mBAAAV,GAAA;MAAA,IAAAW,MAAA;MACA,IAAAC,UAAA,GAAAZ,GAAA,CAAAH,MAAA;MACAvC,qBAAA;QACAkD,EAAA,EAAAR,GAAA,CAAAQ,EAAA;QACAX,MAAA,EAAAG,GAAA,CAAAH;MACA,GAAAd,IAAA;QACA4B,MAAA,CAAAvB,QAAA,CAAAqB,OAAA,IAAAI,MAAA,CAAAD,UAAA;;QAEA;QACAD,MAAA,CAAAvB,QAAA,CAAAU,IAAA,wCAAAe,MAAA,CAAAD,UAAA;MACA,GAAA3B,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACAyB,MAAA,CAAAvB,QAAA,CAAAF,KAAA,IAAA2B,MAAA,CAAAD,UAAA,uBAAA1B,KAAA,CAAAG,OAAA;QACAW,GAAA,CAAAH,MAAA,GAAAG,GAAA,CAAAH,MAAA;MACA;IACA;IAEAiB,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAC,cAAA,CAAAC,QAAA,GAAAnC,IAAA,WAAAoC,QAAA;QACA,IAAAlD,IAAA,GAAAgC,aAAA,CAAAA,aAAA,KACAkB,QAAA;UACAf,WAAA,EAAAe,QAAA,CAAA5B,UAAA,gBACAW,IAAA,CAAAkB,SAAA,CAAAD,QAAA,CAAA3B,QAAA,IACA2B,QAAA,CAAA5B,UAAA,gBACAW,IAAA,CAAAkB,SAAA,CAAAD,QAAA,CAAA1B,WAAA,IACA;UACAC,UAAA,EAAAQ,IAAA,CAAAkB,SAAA,CAAAD,QAAA,CAAAzB,UAAA;QAAA,EACA;QAEA,IAAA2B,WAAA,GAAAN,MAAA,CAAAxC,MAAA,GACAjB,qBAAA,CAAAW,IAAA,IACAZ,mBAAA,CAAAY,IAAA;QAEAoD,WAAA,CAAAtC,IAAA;UACAgC,MAAA,CAAA3B,QAAA,CAAAqB,OAAA;UACAM,MAAA,CAAA3C,aAAA;UACA2C,MAAA,CAAApC,WAAA;;UAEA;UACAoC,MAAA,CAAA3B,QAAA,CAAAU,IAAA;QACA,GAAAb,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;UACA6B,MAAA,CAAA3B,QAAA,CAAAF,KAAA,aAAAA,KAAA,CAAAG,OAAA;QACA;MACA,GAAAJ,KAAA;QACA;MAAA,CACA;IACA;IAEAqC,iBAAA,WAAAA,kBAAA;MACA,KAAAN,KAAA,CAAAC,cAAA,SAAAD,KAAA,CAAAC,cAAA,CAAAM,SAAA;IACA;IAEAC,gBAAA,WAAAA,iBAAA3D,IAAA;MACA,IAAA4D,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA,OAAAH,GAAA,CAAA5D,IAAA;IACA;IAEAgE,iBAAA,WAAAA,kBAAAhE,IAAA;MACA,IAAA4D,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA,OAAAH,GAAA,CAAA5D,IAAA,KAAAA,IAAA;IACA;IAEAiE,cAAA,WAAAA,eAAAC,WAAA;MACA;QACA,IAAAvC,QAAA,GAAAU,IAAA,CAAAC,KAAA,CAAA4B,WAAA;QACA,IAAAC,OAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA,OAAAxC,QAAA,CAAAiC,GAAA,WAAAQ,GAAA;UAAA,OAAAD,OAAA,CAAAC,GAAA;QAAA,GAAAC,IAAA;MACA,SAAAC,OAAA;QACA,OAAAJ,WAAA;MACA;IACA;IAEAK,iBAAA,WAAAA,kBAAAC,QAAA;MACA;QACA,IAAAC,KAAA,GAAApC,IAAA,CAAAC,KAAA,CAAAkC,QAAA;QACA,OAAAC,KAAA,CAAAJ,IAAA;MACA,SAAAK,QAAA;QACA,OAAAF,QAAA;MACA;IACA;IAEAG,eAAA,WAAAA,gBAAAC,aAAA;MACA;QACA,OAAAvC,IAAA,CAAAC,KAAA,CAAAsC,aAAA;MACA,SAAAC,QAAA;QACA;MACA;IACA;IAEAC,SAAA,WAAAA,UAAAC,IAAA;MACA,KAAAA,IAAA;MACA,YAAAC,OAAA,CAAAD,IAAA,EAAAE,MAAA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAApD,SAAA,EAAAC,OAAA;MACA,KAAAD,SAAA,KAAAC,OAAA;MACA,UAAAiB,MAAA,CAAAlB,SAAA,SAAAkB,MAAA,CAAAjB,OAAA;IACA;EACA;AACA", "ignoreList": []}]}