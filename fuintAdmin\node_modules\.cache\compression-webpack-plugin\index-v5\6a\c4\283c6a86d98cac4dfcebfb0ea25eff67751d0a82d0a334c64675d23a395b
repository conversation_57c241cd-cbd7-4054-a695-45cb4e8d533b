
fe6243984d827d1e9703d5835eeda634f172157b	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"8bbdc000cb2aa5a4d7b336b15424b044\"}","integrity":"sha512-kYgkFuqk5TAw4Gvng7dsU0966gS/YKlf3knFPgpiSk7Y2xtp7RJLwYJU7DmX2F5gc0Nk3N6QgrZoTJTEVPF1dA==","time":1754921983615,"size":6668932}