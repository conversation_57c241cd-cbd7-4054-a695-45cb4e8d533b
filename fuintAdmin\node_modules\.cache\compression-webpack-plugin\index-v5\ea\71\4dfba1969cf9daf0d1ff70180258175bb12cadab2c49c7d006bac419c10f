
95f9136e0cd28669349414c2aff7d774458e520e	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"f19990b08a1574e15c6f745b92e05934\"}","integrity":"sha512-8DB+BxKJDxUQXwIS/hmttuTcB6lWg3xKl0vCnPnZ//NXAPSA2sQG9eYIOSyTK78zgKsiMt0P2fI6aBts4s0HaA==","time":1754920991914,"size":2882657}