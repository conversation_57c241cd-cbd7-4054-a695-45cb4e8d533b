
1589d4ea76a559073032447d943abaa424970226	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.ffcb4e7d56899f0704a1.hot-update.js\",\"contentHash\":\"1d98f3f6b3da439f92a5b3926452c10f\"}","integrity":"sha512-jlDJ7HZewQUV2dJW5zOQvx+jxvBVJGJADxSlj2jniqRhhe1LYcANpV/+a/d3ey41LoYzAaFtMzN4/eMjYNJRug==","time":1754921983314,"size":52974}