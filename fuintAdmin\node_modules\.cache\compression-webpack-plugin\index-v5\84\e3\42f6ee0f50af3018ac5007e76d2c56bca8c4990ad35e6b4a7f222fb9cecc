
69590a5a0dbd09eb2a1de1e7eb7f329bd4621008	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.4574ca2763f3f98da0a5.hot-update.js\",\"contentHash\":\"a9463b2f76e5003c8d1cad49ef3e6625\"}","integrity":"sha512-RxEAwoxXzGBRoNWNOMnRX776U9CPwOL36GEA3B2e6uxSY3K5F7YRZScjt4G3xLRI9TlvgHCI8Ur1ApJxot9Acw==","time":1754921993537,"size":53403}