
74f53b8f296e96842d4f75ed3adff5c7f99df99a	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.54ebd69dbbb6fb6cfd59.hot-update.js\",\"contentHash\":\"06cb8861e2c511f3277a3a4615a5256c\"}","integrity":"sha512-+XGE+GI5M4P+eOFdqAiwVL4BfHS8WvbiNYgJ2UIC00MnlJu9k5lAnRj89wjYRNXvgbHTePocxWDDQ7zXrbV1vg==","time":1754920925102,"size":18795}