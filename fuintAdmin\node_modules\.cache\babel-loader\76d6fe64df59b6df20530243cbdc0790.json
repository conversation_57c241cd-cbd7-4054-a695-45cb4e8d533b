{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\index.vue", "mtime": 1754838939888}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getGoodsList", "updateGoodsStatus", "copyGoods", "name", "data", "copyDialogVisible", "copyForm", "targetStoreId", "undefined", "storeId", "$store", "getters", "loading", "ids", "multiple", "showSearch", "total", "list", "storeOptions", "typeOptions", "cateList", "open", "defaultSort", "prop", "order", "form", "id", "logo", "sort", "status", "uploadAction", "process", "env", "VUE_APP_SERVER_URL", "hideUpload", "uploadFiles", "queryParams", "page", "pageSize", "cateId", "isSingleSpec", "goodsNo", "stock", "rules", "required", "message", "trigger", "min", "max", "created", "getList", "methods", "_this", "then", "response", "paginationResponse", "content", "totalElements", "typeList", "storeList", "catch", "error", "console", "$message", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "$refs", "tables", "handleStatusChange", "row", "_this2", "text", "$modal", "confirm", "msgSuccess", "msgError", "handleSelectionChange", "selection", "map", "item", "length", "handleSortChange", "column", "orderByColumn", "isAsc", "handleAdd", "$router", "push", "path", "handleCopy", "submitCopy", "_this3", "handleUpdate", "handleDelete", "_this4", "handleUploadSuccess", "file", "fileName", "handleRemove", "fileList", "_this5", "setTimeout"], "sources": ["src/views/goods/goods/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" class=\"main-search\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"商品名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入商品名称\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"所属店铺\" prop=\"store\">\n        <el-select\n          v-model=\"queryParams.storeId\"\n          placeholder=\"所属店铺\"\n          clearable\n          style=\"width: 180px\"\n        >\n          <el-option :key=\"0\" label=\"公共商品\" v-if=\"!storeId\" :value=\"0\"/>\n          <el-option v-for=\"storeInfo in storeOptions\" :key=\"storeInfo.id\" :label=\"storeInfo.name\" :value=\"storeInfo.id\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"商品分类\" prop=\"cateId\">\n        <el-select class=\"input\" v-model=\"queryParams.cateId\" clearable placeholder=\"请选择商品分类\">\n          <el-option\n            v-for=\"item in cateList\"\n            :key=\"item.id\"\n            :label=\"item.name\"\n            :value=\"item.id\"\n            :disabled=\"item.status !== 'A'\"\n          ></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"商品条码\" prop=\"goodsNo\">\n        <el-input\n          v-model=\"queryParams.goodsNo\"\n          placeholder=\"请输入商品条码\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"商品类型\" prop=\"type\">\n        <el-select class=\"input\" v-model=\"queryParams.type\" clearable placeholder=\"请选择商品类型\">\n          <el-option\n            v-for=\"item in typeOptions\"\n            :key=\"item.key\"\n            :label=\"item.name\"\n            :value=\"item.key\"\n          ></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"规格类型\" prop=\"isSingleSpec\">\n        <el-select\n          v-model=\"queryParams.isSingleSpec\"\n          placeholder=\"规格类型\"\n          clearable\n          style=\"width: 120px\"\n        >\n          <el-option key=\"Y\" label=\"单规格\" value=\"Y\"/>\n          <el-option key=\"N\" label=\"多规格\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"上架状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"上架状态\"\n          clearable\n          style=\"width: 120px\"\n        >\n          <el-option key=\"A\" label=\"上架\" value=\"A\"/>\n          <el-option key=\"N\" label=\"下架\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"库存状态\" prop=\"stock\">\n        <el-select\n          v-model=\"queryParams.stock\"\n          placeholder=\"库存状态\"\n          clearable\n          style=\"width: 120px\"\n        >\n          <el-option key=\"Y\" label=\"有库存\" value=\"Y\"/>\n          <el-option key=\"N\" label=\"无库存\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['goods:goods:add']\"\n        >新增</el-button>\n\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleCopy\"\n          v-hasPermi=\"['goods:goods:add']\"\n        >批量复制</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\n      <el-table-column type=\"selection\" width=\"55\" />\n      <el-table-column label=\"ID\" prop=\"id\" width=\"50\"/>\n      <el-table-column label=\"所属店铺\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.storeInfo\">{{ scope.row.storeInfo.name }}</span>\n          <span v-else>公共所有</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"商品名称\" align=\"center\" min-width=\"300\" prop=\"name\" />\n      <el-table-column label=\"主图\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n           <img class=\"list-img\" :src=\"scope.row.logo\">\n        </template>\n      </el-table-column>\n      <el-table-column label=\"商品条码\" align=\"center\" prop=\"goodsNo\" width=\"140\"/>\n      <el-table-column label=\"剩余库存\" align=\"center\" prop=\"stock\" width=\"100\"/>\n      <el-table-column label=\"所属分类\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.cateInfo.name }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"价格\" align=\"center\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.price\">{{ scope.row.price.toFixed(2) }}</span>\n          <span v-else>0.00</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"上架状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"A\"\n            inactive-value=\"N\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"时间段配置\" align=\"center\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.hasTimeConfig ? 'success' : 'info'\">\n            {{ scope.row.hasTimeConfig ? '已配置' : '未配置' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" width=\"150\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"更新时间\" align=\"center\" width=\"150\" prop=\"updateTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.updateTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"120\" fixed='right'>\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            v-hasPermi=\"['goods:goods:edit']\"\n            @click=\"handleUpdate(scope.row)\"\n            v-if=\"storeId == scope.row.storeId || storeId == 0\"\n          >编辑</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            v-hasPermi=\"['goods:goods:edit']\"\n            v-if=\"storeId == scope.row.storeId || storeId == 0\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page-sizes=\"[10, 20, 50, 100, 200]\"\n      :page.sync=\"queryParams.page\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 复制商品对话框 -->\n    <el-dialog title=\"批量复制商品\" :visible.sync=\"copyDialogVisible\" width=\"500px\" append-to-body>\n      <el-form :model=\"copyForm\" label-width=\"80px\">\n        <el-form-item label=\"目标店铺\" prop=\"targetStoreId\">\n          <el-select v-model=\"copyForm.targetStoreId\" placeholder=\"请选择目标店铺\" style=\"width: 100%\">\n            <el-option\n              v-for=\"item in storeOptions\"\n              :key=\"item.id\"\n              :label=\"item.name\"\n              :value=\"item.id\"\n            />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitCopy\">确 定</el-button>\n        <el-button @click=\"copyDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getGoodsList, updateGoodsStatus, copyGoods } from \"@/api/goods\";\nexport default {\n  name: \"GoodsIndex\",\n  data() {\n    return {\n      // 复制对话框\n      copyDialogVisible: false,\n      copyForm: {\n        targetStoreId: undefined\n      },\n      storeId: this.$store.getters.storeId,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 店铺列表\n      storeOptions: [],\n      // 商品类型\n      typeOptions: [],\n      // 商品分类\n      cateList: [],\n      // 是否显示弹出层\n      open: false,\n      // 默认排序\n      defaultSort: {prop: 'sort', order: 'descending'},\n      // 表单参数\n      form: { id: '', name: '', logo: '', sort: 0, status: \"A\" },\n      // 上传地址\n      uploadAction: process.env.VUE_APP_SERVER_URL + 'backendApi/file/upload',\n      // 隐藏上传\n      hideUpload: false,\n      // 上传文件列表\n      uploadFiles: [],\n      // 查询参数\n      queryParams: {\n        page: 1,\n        pageSize: 10,\n        storeId: '',\n        cateId: '',\n        name: '',\n        isSingleSpec: '',\n        goodsNo: '',\n        stock: '',\n        status: ''\n      },\n      // 表单校验\n      rules: {\n        name: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '名称长度必须介于2 和 200 之间', trigger: 'blur' }\n        ],\n        logo: [{ required: true, message: \"请上传图片\", trigger: \"blur\" }]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 查询商品列表\n    getList() {\n      this.loading = true;\n      getGoodsList(this.queryParams).then( response => {\n          this.list = response.data.paginationResponse.content;\n          this.total = response.data.paginationResponse.totalElements;\n          this.typeOptions = response.data.typeList;\n          this.storeOptions = response.data.storeList;\n          this.cateList = response.data.cateList;\n          this.loading = false;\n        }\n      ).catch(error => {\n        console.error('获取商品列表失败:', error)\n        this.$message.error('获取商品列表失败: ' + (error.message || '未知错误'))\n        this.loading = false;\n      });\n    },\n    // 搜索按钮操作\n    handleQuery() {\n      this.queryParams.page = 1;\n      this.getList();\n    },\n    // 重置按钮操作\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\n      this.handleQuery();\n    },\n    // 状态修改\n    handleStatusChange(row) {\n      let text = row.status == \"A\" ? \"上架\" : \"下架\";\n      this.$modal.confirm('确认要' + text + '\"' + row.name + '\"吗？').then(function() {\n        return updateGoodsStatus(row.id, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function(error) {\n        console.error('状态更新失败:', error)\n        this.$modal.msgError(text + \"失败: \" + (error.message || '未知错误'));\n        row.status = row.status === \"N\" ? \"A\" : \"N\";\n      });\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.multiple = !selection.length\n    },\n    // 排序触发事件\n    handleSortChange(column, prop, order) {\n      this.queryParams.orderByColumn = column.prop;\n      this.queryParams.isAsc = column.order;\n      this.getList();\n    },\n    // 新增按钮操作\n    handleAdd() {\n      this.$router.push( { path: '/goods/goods/add' } )\n    },\n    handleCopy() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要复制的商品\");\n        return;\n      }\n      this.copyForm.targetStoreId = undefined;\n      this.copyDialogVisible = true;\n    },\n    // 提交复制\n    submitCopy() {\n      if (!this.copyForm.targetStoreId) {\n        this.$modal.msgError(\"请选择目标店铺\");\n        return;\n      }\n      copyGoods(this.ids, this.copyForm.targetStoreId).then(response => {\n        this.$modal.msgSuccess(\"复制成功\");\n        this.handleQuery();\n        this.copyDialogVisible = false;\n      }).catch(error => {\n        console.error('复制失败:', error)\n        this.$modal.msgError(\"复制失败: \" + (error.message || '未知错误'));\n      });\n\n    },\n    // 修改按钮操作\n    handleUpdate(row) {\n      this.$router.push( { path: '/goods/goods/edit?goodsId=' + row.id } )\n    },\n    // 删除按钮操作\n    handleDelete(row) {\n      const name = row.name\n      this.$modal.confirm('是否确认删除商品\"' + name + '\"？').then(function() {\n        return updateGoodsStatus(row.id, 'D');\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(error => {\n        console.error('删除失败:', error)\n        this.$modal.msgError(\"删除失败: \" + (error.message || '未知错误'));\n      });\n    },\n    handleUploadSuccess (file) {\n      this.form.logo = file.data.fileName\n    },\n    handleRemove (file, fileList) {\n      setTimeout(() => {\n        this.hideUpload = fileList.length > 0\n      }, 520)\n    }\n  }\n};\n</script>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0NA,SAAAA,YAAA,EAAAC,iBAAA,EAAAC,SAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,iBAAA;MACAC,QAAA;QACAC,aAAA,EAAAC;MACA;MACAC,OAAA,OAAAC,MAAA,CAAAC,OAAA,CAAAF,OAAA;MACA;MACAG,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;MACA;MACAC,QAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QAAAC,IAAA;QAAAC,KAAA;MAAA;MACA;MACAC,IAAA;QAAAC,EAAA;QAAAvB,IAAA;QAAAwB,IAAA;QAAAC,IAAA;QAAAC,MAAA;MAAA;MACA;MACAC,YAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,kBAAA;MACA;MACAC,UAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,IAAA;QACAC,QAAA;QACA7B,OAAA;QACA8B,MAAA;QACApC,IAAA;QACAqC,YAAA;QACAC,OAAA;QACAC,KAAA;QACAb,MAAA;MACA;MACA;MACAc,KAAA;QACAxC,IAAA,GACA;UAAAyC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,IAAA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAxC,OAAA;MACAZ,YAAA,MAAAoC,WAAA,EAAAiB,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAnC,IAAA,GAAAqC,QAAA,CAAAlD,IAAA,CAAAmD,kBAAA,CAAAC,OAAA;QACAJ,KAAA,CAAApC,KAAA,GAAAsC,QAAA,CAAAlD,IAAA,CAAAmD,kBAAA,CAAAE,aAAA;QACAL,KAAA,CAAAjC,WAAA,GAAAmC,QAAA,CAAAlD,IAAA,CAAAsD,QAAA;QACAN,KAAA,CAAAlC,YAAA,GAAAoC,QAAA,CAAAlD,IAAA,CAAAuD,SAAA;QACAP,KAAA,CAAAhC,QAAA,GAAAkC,QAAA,CAAAlD,IAAA,CAAAgB,QAAA;QACAgC,KAAA,CAAAxC,OAAA;MACA,CACA,EAAAgD,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAT,KAAA,CAAAW,QAAA,CAAAF,KAAA,iBAAAA,KAAA,CAAAhB,OAAA;QACAO,KAAA,CAAAxC,OAAA;MACA;IACA;IACA;IACAoD,WAAA,WAAAA,YAAA;MACA,KAAA5B,WAAA,CAAAC,IAAA;MACA,KAAAa,OAAA;IACA;IACA;IACAe,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAxC,IAAA,MAAAN,WAAA,CAAAC,IAAA,OAAAD,WAAA,CAAAE,KAAA;MACA,KAAAwC,WAAA;IACA;IACA;IACAK,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAzC,MAAA;MACA,KAAA4C,MAAA,CAAAC,OAAA,SAAAF,IAAA,SAAAF,GAAA,CAAAnE,IAAA,UAAAkD,IAAA;QACA,OAAApD,iBAAA,CAAAqE,GAAA,CAAA5C,EAAA,EAAA4C,GAAA,CAAAzC,MAAA;MACA,GAAAwB,IAAA;QACAkB,MAAA,CAAAE,MAAA,CAAAE,UAAA,CAAAH,IAAA;MACA,GAAAZ,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACA,KAAAY,MAAA,CAAAG,QAAA,CAAAJ,IAAA,aAAAX,KAAA,CAAAhB,OAAA;QACAyB,GAAA,CAAAzC,MAAA,GAAAyC,GAAA,CAAAzC,MAAA;MACA;IACA;IACA;IACAgD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAjE,GAAA,GAAAiE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAtD,EAAA;MAAA;MACA,KAAAZ,QAAA,IAAAgE,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,MAAA,EAAA5D,IAAA,EAAAC,KAAA;MACA,KAAAY,WAAA,CAAAgD,aAAA,GAAAD,MAAA,CAAA5D,IAAA;MACA,KAAAa,WAAA,CAAAiD,KAAA,GAAAF,MAAA,CAAA3D,KAAA;MACA,KAAA0B,OAAA;IACA;IACA;IACAoC,SAAA,WAAAA,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAA7E,GAAA,CAAAoE,MAAA;QACA,KAAAR,MAAA,CAAAG,QAAA;QACA;MACA;MACA,KAAAtE,QAAA,CAAAC,aAAA,GAAAC,SAAA;MACA,KAAAH,iBAAA;IACA;IACA;IACAsF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,UAAAtF,QAAA,CAAAC,aAAA;QACA,KAAAkE,MAAA,CAAAG,QAAA;QACA;MACA;MACA1E,SAAA,MAAAW,GAAA,OAAAP,QAAA,CAAAC,aAAA,EAAA8C,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAAnB,MAAA,CAAAE,UAAA;QACAiB,MAAA,CAAA5B,WAAA;QACA4B,MAAA,CAAAvF,iBAAA;MACA,GAAAuD,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA+B,MAAA,CAAAnB,MAAA,CAAAG,QAAA,aAAAf,KAAA,CAAAhB,OAAA;MACA;IAEA;IACA;IACAgD,YAAA,WAAAA,aAAAvB,GAAA;MACA,KAAAiB,OAAA,CAAAC,IAAA;QAAAC,IAAA,iCAAAnB,GAAA,CAAA5C;MAAA;IACA;IACA;IACAoE,YAAA,WAAAA,aAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,IAAA5F,IAAA,GAAAmE,GAAA,CAAAnE,IAAA;MACA,KAAAsE,MAAA,CAAAC,OAAA,eAAAvE,IAAA,SAAAkD,IAAA;QACA,OAAApD,iBAAA,CAAAqE,GAAA,CAAA5C,EAAA;MACA,GAAA2B,IAAA;QACA0C,MAAA,CAAA7C,OAAA;QACA6C,MAAA,CAAAtB,MAAA,CAAAE,UAAA;MACA,GAAAf,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACAkC,MAAA,CAAAtB,MAAA,CAAAG,QAAA,aAAAf,KAAA,CAAAhB,OAAA;MACA;IACA;IACAmD,mBAAA,WAAAA,oBAAAC,IAAA;MACA,KAAAxE,IAAA,CAAAE,IAAA,GAAAsE,IAAA,CAAA7F,IAAA,CAAA8F,QAAA;IACA;IACAC,YAAA,WAAAA,aAAAF,IAAA,EAAAG,QAAA;MAAA,IAAAC,MAAA;MACAC,UAAA;QACAD,MAAA,CAAAnE,UAAA,GAAAkE,QAAA,CAAAnB,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}