
3d2387d3e61df29d8b9b04cafd3f89558db96052	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"eb0655884052b895fcb0230cc30da8af\"}","integrity":"sha512-grSFaeMefA/G0tg/wiI84xwvrrKcxX0DFm+RlsE6T6BtWyhUcClz4kxnVw0kRAo6WRKcDiYZKQmB+irCyBqBzA==","time":1754921675223,"size":6667816}