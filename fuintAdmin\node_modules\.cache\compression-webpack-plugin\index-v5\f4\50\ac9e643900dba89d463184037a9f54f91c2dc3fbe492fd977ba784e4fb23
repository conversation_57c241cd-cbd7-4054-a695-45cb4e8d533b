
b615160adbc0b9905771cfdf1cee2658bd7e6b26	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"711c9e04322a43ed77f6467fa22960c6\"}","integrity":"sha512-+H9nTNhqj69hJ2Nv2xMbBkQgtRCRWFq0WuYFLCVTIRJpHszkqUJLO7Yo96JajcB8P5z7oPJF6zPr8utY/Kj4Mg==","time":1754921687130,"size":6667992}