
bd1ff9104c713b0739274636f4e9932c70413210	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"3749883191f2c3ca669dfc2017c6ee61\"}","integrity":"sha512-br80VUfMrzIrYoZdL60zcFXCR+qduczFYN72H8LiYrgYy6bi1rK9QQj9SeDYC6V6nuLPfyYEYtvfzpdL0z27Rg==","time":1754921675025,"size":2883177}