{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue", "mtime": 1754845237275}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovd29ya3NwYWNlL2Z1aW50Rm9vZFN5c3RlbS9mdWludEFkbWluL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkQ6L3dvcmtzcGFjZS9mdWludEZvb2RTeXN0ZW0vZnVpbnRBZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmVycm9yLmNhdXNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNvcnQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zcGxpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdUaW1lQ29uZmlnRm9ybScsCiAgcHJvcHM6IHsKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9LAogICAgZ29vZHNJZDogewogICAgICB0eXBlOiBbU3RyaW5nLCBOdW1iZXJdLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICByZXR1cm4gewogICAgICBmb3JtOiB7CiAgICAgICAgaWQ6IHVuZGVmaW5lZCwKICAgICAgICBnb29kc0lkOiB0aGlzLmdvb2RzSWQsCiAgICAgICAgY29uZmlnVHlwZTogJ0RBSUxZJywKICAgICAgICB3ZWVrRGF5czogW10sCiAgICAgICAgY3VzdG9tRGF0ZXM6IFtdLAogICAgICAgIHRpbWVSYW5nZXM6IFt7CiAgICAgICAgICBzdGFydFRpbWU6ICcnLAogICAgICAgICAgZW5kVGltZTogJycKICAgICAgICB9XSwKICAgICAgICBzdGF0dXM6ICdBJwogICAgICB9LAogICAgICB3ZWVrT3B0aW9uczogW3sKICAgICAgICBsYWJlbDogJ+WRqOS4gCcsCiAgICAgICAgdmFsdWU6IDEKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5ZGo5LqMJywKICAgICAgICB2YWx1ZTogMgogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICflkajkuIknLAogICAgICAgIHZhbHVlOiAzCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+WRqOWbmycsCiAgICAgICAgdmFsdWU6IDQKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5ZGo5LqUJywKICAgICAgICB2YWx1ZTogNQogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICflkajlha0nLAogICAgICAgIHZhbHVlOiA2CiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+WRqOaXpScsCiAgICAgICAgdmFsdWU6IDcKICAgICAgfV0sCiAgICAgIHBpY2tlck9wdGlvbnM6IHsKICAgICAgICBkaXNhYmxlZERhdGU6IGZ1bmN0aW9uIGRpc2FibGVkRGF0ZSh0aW1lKSB7CiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBEYXRlLm5vdygpIC0gOC42NGU3OwogICAgICAgIH0KICAgICAgfSwKICAgICAgcnVsZXM6IHsKICAgICAgICBjb25maWdUeXBlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup6YWN572u57G75Z6LJywKICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnCiAgICAgICAgfV0sCiAgICAgICAgd2Vla0RheXM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7foh7PlsJHpgInmi6nkuIDkuKrmmJ/mnJ8nLAogICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZScsCiAgICAgICAgICB2YWxpZGF0b3I6IGZ1bmN0aW9uIHZhbGlkYXRvcihydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgICAgICAgaWYgKF90aGlzLmZvcm0uY29uZmlnVHlwZSA9PT0gJ1dFRUtMWScgJiYgKCF2YWx1ZSB8fCB2YWx1ZS5sZW5ndGggPT09IDApKSB7CiAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7foh7PlsJHpgInmi6nkuIDkuKrmmJ/mnJ8nKSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH1dLAogICAgICAgIGN1c3RvbURhdGVzOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36Iez5bCR6YCJ5oup5LiA5Liq5pel5pyfJywKICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnLAogICAgICAgICAgdmFsaWRhdG9yOiBmdW5jdGlvbiB2YWxpZGF0b3IocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgICAgICAgIGlmIChfdGhpcy5mb3JtLmNvbmZpZ1R5cGUgPT09ICdDVVNUT00nICYmICghdmFsdWUgfHwgdmFsdWUubGVuZ3RoID09PSAwKSkgewogICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36Iez5bCR6YCJ5oup5LiA5Liq5pel5pyfJykpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9XSwKICAgICAgICB0aW1lUmFuZ2VzOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36K6+572u6Iez5bCR5LiA5Liq5pe26Ze05q61JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJywKICAgICAgICAgIHZhbGlkYXRvcjogZnVuY3Rpb24gdmFsaWRhdG9yKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICAgICAgICBpZiAoIXZhbHVlIHx8IHZhbHVlLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36K6+572u6Iez5bCR5LiA5Liq5pe26Ze05q61JykpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHZhciBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcih2YWx1ZSksCiAgICAgICAgICAgICAgICBfc3RlcDsKICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3IucygpOyAhKF9zdGVwID0gX2l0ZXJhdG9yLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgdmFyIHJhbmdlID0gX3N0ZXAudmFsdWU7CiAgICAgICAgICAgICAgICAgIGlmICghcmFuZ2Uuc3RhcnRUaW1lIHx8ICFyYW5nZS5lbmRUaW1lKSB7CiAgICAgICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7flrozlloTml7bpl7TmrrXorr7nva4nKSk7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIC8vIOenu+mZpOW8gOWni+aXtumXtOW/hemhu+Wwj+S6jue7k+adn+aXtumXtOeahOmqjOivge+8jOWFgeiuuOi3qOWkqeaXtumXtOaute+8iOWmgjIyOjAwLTAyOjAw77yJCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAvLyDmo4Dmn6Xml7bpl7TmrrXmmK/lkKbph43lj6AKICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdmFsdWUubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICAgIGZvciAodmFyIGogPSBpICsgMTsgaiA8IHZhbHVlLmxlbmd0aDsgaisrKSB7CiAgICAgICAgICAgICAgICAgIHZhciByYW5nZTEgPSB2YWx1ZVtpXTsKICAgICAgICAgICAgICAgICAgdmFyIHJhbmdlMiA9IHZhbHVlW2pdOwogICAgICAgICAgICAgICAgICAvLyDmo4Dmn6Xml7bpl7TmrrXmmK/lkKbph43lj6DvvIjogIPomZHot6jlpKnmg4XlhrXvvIkKICAgICAgICAgICAgICAgICAgdmFyIHN0YXJ0MSA9IHJhbmdlMS5zdGFydFRpbWU7CiAgICAgICAgICAgICAgICAgIHZhciBlbmQxID0gcmFuZ2UxLmVuZFRpbWU7CiAgICAgICAgICAgICAgICAgIHZhciBzdGFydDIgPSByYW5nZTIuc3RhcnRUaW1lOwogICAgICAgICAgICAgICAgICB2YXIgZW5kMiA9IHJhbmdlMi5lbmRUaW1lOwoKICAgICAgICAgICAgICAgICAgLy8g5aSE55CG6Leo5aSp5pe26Ze05q616YeN5Y+g5qOA5p+lCiAgICAgICAgICAgICAgICAgIHZhciBvdmVybGFwID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIGlmIChlbmQxIDw9IHN0YXJ0MSkgewogICAgICAgICAgICAgICAgICAgIC8vIHJhbmdlMSDmmK/ot6jlpKnml7bpl7TmrrUgKOWmgiAyMjowMC0wMjowMCkKICAgICAgICAgICAgICAgICAgICBpZiAoZW5kMiA8PSBzdGFydDIpIHsKICAgICAgICAgICAgICAgICAgICAgIC8vIHJhbmdlMiDkuZ/mmK/ot6jlpKnml7bpl7TmrrUKICAgICAgICAgICAgICAgICAgICAgIG92ZXJsYXAgPSB0cnVlOyAvLyDkuKTkuKrot6jlpKnml7bpl7TmrrXmgLvmmK/ph43lj6AKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgLy8gcmFuZ2UyIOS4jei3qOWkqQogICAgICAgICAgICAgICAgICAgICAgaWYgKHN0YXJ0MiA+PSBzdGFydDEgfHwgZW5kMiA8PSBlbmQxKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIG92ZXJsYXAgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAvLyByYW5nZTEg5LiN6Leo5aSpCiAgICAgICAgICAgICAgICAgICAgaWYgKGVuZDIgPD0gc3RhcnQyKSB7CiAgICAgICAgICAgICAgICAgICAgICAvLyByYW5nZTIg5piv6Leo5aSp5pe26Ze05q61CiAgICAgICAgICAgICAgICAgICAgICBpZiAoc3RhcnQxID49IHN0YXJ0MiB8fCBlbmQxIDw9IGVuZDIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgb3ZlcmxhcCA9IHRydWU7CiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgIC8vIHJhbmdlMiDkuZ/kuI3ot6jlpKkKICAgICAgICAgICAgICAgICAgICAgIGlmIChzdGFydDEgPD0gc3RhcnQyICYmIHN0YXJ0MiA8IGVuZDEgfHwgc3RhcnQyIDw9IHN0YXJ0MSAmJiBzdGFydDEgPCBlbmQyKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIG92ZXJsYXAgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBpZiAob3ZlcmxhcCkgewogICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5pe26Ze05q615LiN6IO96YeN5Y+gJykpOwogICAgICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBjYWxsYmFjaygpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfV0KICAgICAgfQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICB2YWx1ZTogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmIChuZXdWYWwgJiYgT2JqZWN0LmtleXMobmV3VmFsKS5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0aGlzLmZvcm0gPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5mb3JtKSwgbmV3VmFsKSwge30sIHsKICAgICAgICAgICAgdGltZVJhbmdlczogbmV3VmFsLnRpbWVSYW5nZXMgJiYgbmV3VmFsLnRpbWVSYW5nZXMubGVuZ3RoID4gMCA/IG5ld1ZhbC50aW1lUmFuZ2VzIDogW3sKICAgICAgICAgICAgICBzdGFydFRpbWU6ICcnLAogICAgICAgICAgICAgIGVuZFRpbWU6ICcnCiAgICAgICAgICAgIH1dCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwKICAgICAgZGVlcDogdHJ1ZQogICAgfSwKICAgIGZvcm06IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICAvLyDlr7nml7bpl7TmrrXov5vooYzmjpLluo8KICAgICAgICBpZiAobmV3VmFsLnRpbWVSYW5nZXMgJiYgbmV3VmFsLnRpbWVSYW5nZXMubGVuZ3RoID4gMSkgewogICAgICAgICAgbmV3VmFsLnRpbWVSYW5nZXMuc29ydChmdW5jdGlvbiAoYSwgYikgewogICAgICAgICAgICBpZiAoYS5zdGFydFRpbWUgJiYgYi5zdGFydFRpbWUpIHsKICAgICAgICAgICAgICByZXR1cm4gYS5zdGFydFRpbWUubG9jYWxlQ29tcGFyZShiLnN0YXJ0VGltZSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIDA7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgICAgdGhpcy4kZW1pdCgnaW5wdXQnLCBuZXdWYWwpOwogICAgICB9LAogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVDb25maWdUeXBlQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVDb25maWdUeXBlQ2hhbmdlKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgLy8g5YiH5o2i6YWN572u57G75Z6L5pe25riF56m655u45YWz5pWw5o2uCiAgICAgIGlmICh0aGlzLmZvcm0uY29uZmlnVHlwZSA9PT0gJ0RBSUxZJykgewogICAgICAgIHRoaXMuZm9ybS53ZWVrRGF5cyA9IFtdOwogICAgICAgIHRoaXMuZm9ybS5jdXN0b21EYXRlcyA9IFtdOwogICAgICB9IGVsc2UgaWYgKHRoaXMuZm9ybS5jb25maWdUeXBlID09PSAnV0VFS0xZJykgewogICAgICAgIHRoaXMuZm9ybS5jdXN0b21EYXRlcyA9IFtdOwogICAgICB9IGVsc2UgaWYgKHRoaXMuZm9ybS5jb25maWdUeXBlID09PSAnQ1VTVE9NJykgewogICAgICAgIHRoaXMuZm9ybS53ZWVrRGF5cyA9IFtdOwogICAgICB9CgogICAgICAvLyDmj5DnpLrnlKjmiLfphY3nva7nsbvlnovlt7LliIfmjaIKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LliIfmjaLphY3nva7nsbvlnovvvIzor7fph43mlrDorr7nva7nm7jlhbPpgInpobknKTsKCiAgICAgIC8vIOmqjOivgeihqOWNlQogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMyLiRyZWZzLnRpbWVDb25maWdGb3JtLnZhbGlkYXRlRmllbGQoJ3dlZWtEYXlzJyk7CiAgICAgICAgX3RoaXMyLiRyZWZzLnRpbWVDb25maWdGb3JtLnZhbGlkYXRlRmllbGQoJ2N1c3RvbURhdGVzJyk7CiAgICAgIH0pOwogICAgfSwKICAgIGFkZFRpbWVSYW5nZTogZnVuY3Rpb24gYWRkVGltZVJhbmdlKCkgewogICAgICB0aGlzLmZvcm0udGltZVJhbmdlcy5wdXNoKHsKICAgICAgICBzdGFydFRpbWU6ICcnLAogICAgICAgIGVuZFRpbWU6ICcnCiAgICAgIH0pOwogICAgfSwKICAgIHJlbW92ZVRpbWVSYW5nZTogZnVuY3Rpb24gcmVtb3ZlVGltZVJhbmdlKGluZGV4KSB7CiAgICAgIGlmICh0aGlzLmZvcm0udGltZVJhbmdlcy5sZW5ndGggPiAxKSB7CiAgICAgICAgdGhpcy5mb3JtLnRpbWVSYW5nZXMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgfQogICAgfSwKICAgIHZhbGlkYXRlOiBmdW5jdGlvbiB2YWxpZGF0ZSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgICAgX3RoaXMzLiRyZWZzLnRpbWVDb25maWdGb3JtLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgIHJlc29sdmUoX3RoaXMzLmZvcm0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLmVycm9yKCfooajljZXpqozor4HlpLHotKXvvIzor7fmo4Dmn6XovpPlhaXlhoXlrrknKTsKICAgICAgICAgICAgcmVqZWN0KG5ldyBFcnJvcign6KGo5Y2V6aqM6K+B5aSx6LSlJykpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICByZXNldEZvcm06IGZ1bmN0aW9uIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmcy50aW1lQ29uZmlnRm9ybS5yZXNldEZpZWxkcygpOwogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IHVuZGVmaW5lZCwKICAgICAgICBnb29kc0lkOiB0aGlzLmdvb2RzSWQsCiAgICAgICAgY29uZmlnVHlwZTogJ0RBSUxZJywKICAgICAgICB3ZWVrRGF5czogW10sCiAgICAgICAgY3VzdG9tRGF0ZXM6IFtdLAogICAgICAgIHRpbWVSYW5nZXM6IFt7CiAgICAgICAgICBzdGFydFRpbWU6ICcnLAogICAgICAgICAgZW5kVGltZTogJycKICAgICAgICB9XSwKICAgICAgICBzdGF0dXM6ICdBJwogICAgICB9OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "props", "value", "type", "Object", "default", "_default", "goodsId", "String", "Number", "required", "data", "_this", "form", "id", "undefined", "configType", "weekDays", "customDates", "timeRanges", "startTime", "endTime", "status", "weekOptions", "label", "pickerOptions", "disabledDate", "time", "getTime", "Date", "now", "rules", "message", "trigger", "validator", "rule", "callback", "length", "Error", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "range", "err", "e", "f", "i", "j", "range1", "range2", "start1", "end1", "start2", "end2", "overlap", "watch", "handler", "newVal", "keys", "_objectSpread", "immediate", "deep", "sort", "a", "b", "localeCompare", "$emit", "methods", "handleConfigTypeChange", "_this2", "$message", "info", "$nextTick", "$refs", "timeConfigForm", "validateField", "addTimeRange", "push", "removeTimeRange", "index", "splice", "validate", "_this3", "Promise", "resolve", "reject", "valid", "error", "resetForm", "resetFields"], "sources": ["src/views/goods/components/TimeConfigForm.vue"], "sourcesContent": ["<template>\n  <div class=\"time-config-container\">\n    <el-form ref=\"timeConfigForm\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"配置类型\" prop=\"configType\">\n            <el-radio-group v-model=\"form.configType\" @change=\"handleConfigTypeChange\">\n              <el-radio label=\"DAILY\">每日通用</el-radio>\n              <el-radio label=\"WEEKLY\">按周设置</el-radio>\n              <el-radio label=\"CUSTOM\">自定义日期</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 按周设置时显示星期选择 -->\n      <el-row v-if=\"form.configType === 'WEEKLY'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择星期\" prop=\"weekDays\">\n            <el-checkbox-group v-model=\"form.weekDays\">\n              <el-checkbox v-for=\"day in weekOptions\" :key=\"day.value\" :label=\"day.value\">\n                {{ day.label }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 自定义日期时显示日期选择 -->\n      <el-row v-if=\"form.configType === 'CUSTOM'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择日期\" prop=\"customDates\">\n            <el-date-picker\n              v-model=\"form.customDates\"\n              type=\"dates\"\n              placeholder=\"选择一个或多个日期\"\n              value-format=\"yyyy-MM-dd\"\n              :picker-options=\"pickerOptions\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 时间段设置 -->\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"时间段\" prop=\"timeRanges\">\n            <div class=\"time-ranges-container\">\n              <div v-for=\"(range, index) in form.timeRanges\" :key=\"index\" class=\"time-range-item\">\n                <el-time-picker\n                  v-model=\"range.startTime\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <span class=\"time-separator\">至</span>\n                <el-time-picker\n                  v-model=\"range.endTime\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <el-button\n                  type=\"danger\"\n                  icon=\"el-icon-delete\"\n                  size=\"mini\"\n                  circle\n                  @click=\"removeTimeRange(index)\"\n                  :disabled=\"form.timeRanges.length === 1\"\n                />\n              </div>\n              <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"addTimeRange\">\n                添加时间段\n              </el-button>\n              <div class=\"form-tip\">提示：时间段不能重叠，支持跨天时间段配置（如22:00-02:00）</div>\n            </div>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-radio-group v-model=\"form.status\">\n              <el-radio label=\"A\">启用</el-radio>\n              <el-radio label=\"N\">禁用</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TimeConfigForm',\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    goodsId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      form: {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      },\n      weekOptions: [\n        { label: '周一', value: 1 },\n        { label: '周二', value: 2 },\n        { label: '周三', value: 3 },\n        { label: '周四', value: 4 },\n        { label: '周五', value: 5 },\n        { label: '周六', value: 6 },\n        { label: '周日', value: 7 }\n      ],\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7\n        }\n      },\n      rules: {\n        configType: [\n          { required: true, message: '请选择配置类型', trigger: 'change' }\n        ],\n        weekDays: [\n          {\n            required: true,\n            message: '请至少选择一个星期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'WEEKLY' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个星期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        customDates: [\n          {\n            required: true,\n            message: '请至少选择一个日期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'CUSTOM' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个日期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        timeRanges: [\n          {\n            required: true,\n            message: '请设置至少一个时间段',\n            trigger: 'blur',\n            validator: (rule, value, callback) => {\n              if (!value || value.length === 0) {\n                callback(new Error('请设置至少一个时间段'))\n              } else {\n                for (let range of value) {\n                  if (!range.startTime || !range.endTime) {\n                    callback(new Error('请完善时间段设置'))\n                    return\n                  }\n                  // 移除开始时间必须小于结束时间的验证，允许跨天时间段（如22:00-02:00）\n                }\n                // 检查时间段是否重叠\n                for (let i = 0; i < value.length; i++) {\n                  for (let j = i + 1; j < value.length; j++) {\n                    const range1 = value[i];\n                    const range2 = value[j];\n                    // 检查时间段是否重叠（考虑跨天情况）\n                    const start1 = range1.startTime;\n                    const end1 = range1.endTime;\n                    const start2 = range2.startTime;\n                    const end2 = range2.endTime;\n\n                    // 处理跨天时间段重叠检查\n                    let overlap = false;\n                    if (end1 <= start1) {\n                      // range1 是跨天时间段 (如 22:00-02:00)\n                      if (end2 <= start2) {\n                        // range2 也是跨天时间段\n                        overlap = true; // 两个跨天时间段总是重叠\n                      } else {\n                        // range2 不跨天\n                        if (start2 >= start1 || end2 <= end1) {\n                          overlap = true;\n                        }\n                      }\n                    } else {\n                      // range1 不跨天\n                      if (end2 <= start2) {\n                        // range2 是跨天时间段\n                        if (start1 >= start2 || end1 <= end2) {\n                          overlap = true;\n                        }\n                      } else {\n                        // range2 也不跨天\n                        if ((start1 <= start2 && start2 < end1) ||\n                            (start2 <= start1 && start1 < end2)) {\n                          overlap = true;\n                        }\n                      }\n                    }\n\n                    if (overlap) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  }\n                }\n                callback()\n              }\n            }\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        if (newVal && Object.keys(newVal).length > 0) {\n          this.form = {\n            ...this.form,\n            ...newVal,\n            timeRanges: newVal.timeRanges && newVal.timeRanges.length > 0\n              ? newVal.timeRanges\n              : [{ startTime: '', endTime: '' }]\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    form: {\n      handler(newVal) {\n        // 对时间段进行排序\n        if (newVal.timeRanges && newVal.timeRanges.length > 1) {\n          newVal.timeRanges.sort((a, b) => {\n            if (a.startTime && b.startTime) {\n              return a.startTime.localeCompare(b.startTime)\n            }\n            return 0\n          })\n        }\n        this.$emit('input', newVal)\n      },\n      deep: true\n    }\n  },\n  methods: {\n    handleConfigTypeChange() {\n      // 切换配置类型时清空相关数据\n      if (this.form.configType === 'DAILY') {\n        this.form.weekDays = []\n        this.form.customDates = []\n      } else if (this.form.configType === 'WEEKLY') {\n        this.form.customDates = []\n      } else if (this.form.configType === 'CUSTOM') {\n        this.form.weekDays = []\n      }\n\n      // 提示用户配置类型已切换\n      this.$message.info('已切换配置类型，请重新设置相关选项')\n\n      // 验证表单\n      this.$nextTick(() => {\n        this.$refs.timeConfigForm.validateField('weekDays')\n        this.$refs.timeConfigForm.validateField('customDates')\n      })\n    },\n    addTimeRange() {\n      this.form.timeRanges.push({ startTime: '', endTime: '' })\n    },\n    removeTimeRange(index) {\n      if (this.form.timeRanges.length > 1) {\n        this.form.timeRanges.splice(index, 1)\n      }\n    },\n    validate() {\n      return new Promise((resolve, reject) => {\n        this.$refs.timeConfigForm.validate(valid => {\n          if (valid) {\n            resolve(this.form)\n          } else {\n            this.$message.error('表单验证失败，请检查输入内容')\n            reject(new Error('表单验证失败'))\n          }\n        })\n      })\n    },\n    resetForm() {\n      this.$refs.timeConfigForm.resetFields()\n      this.form = {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.time-config-container {\n  padding: 20px;\n}\n\n.time-ranges-container {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.time-range-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  margin: 0 5px;\n  color: #606266;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAC,OAAA;MACAJ,IAAA,GAAAK,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,IAAA;QACAC,EAAA,EAAAC,SAAA;QACAR,OAAA,OAAAA,OAAA;QACAS,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,UAAA;UAAAC,SAAA;UAAAC,OAAA;QAAA;QACAC,MAAA;MACA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,EACA;MACAuB,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;MACAC,KAAA;QACAf,UAAA,GACA;UAAAN,QAAA;UAAAsB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,QAAA,GACA;UACAP,QAAA;UACAsB,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,IAAA,EAAAjC,KAAA,EAAAkC,QAAA;YACA,IAAAxB,KAAA,CAAAC,IAAA,CAAAG,UAAA,mBAAAd,KAAA,IAAAA,KAAA,CAAAmC,MAAA;cACAD,QAAA,KAAAE,KAAA;YACA;cACAF,QAAA;YACA;UACA;QACA,EACA;QACAlB,WAAA,GACA;UACAR,QAAA;UACAsB,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,IAAA,EAAAjC,KAAA,EAAAkC,QAAA;YACA,IAAAxB,KAAA,CAAAC,IAAA,CAAAG,UAAA,mBAAAd,KAAA,IAAAA,KAAA,CAAAmC,MAAA;cACAD,QAAA,KAAAE,KAAA;YACA;cACAF,QAAA;YACA;UACA;QACA,EACA;QACAjB,UAAA,GACA;UACAT,QAAA;UACAsB,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,IAAA,EAAAjC,KAAA,EAAAkC,QAAA;YACA,KAAAlC,KAAA,IAAAA,KAAA,CAAAmC,MAAA;cACAD,QAAA,KAAAE,KAAA;YACA;cAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAtC,KAAA;gBAAAuC,KAAA;cAAA;gBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;kBAAA,IAAAC,KAAA,GAAAJ,KAAA,CAAAvC,KAAA;kBACA,KAAA2C,KAAA,CAAAzB,SAAA,KAAAyB,KAAA,CAAAxB,OAAA;oBACAe,QAAA,KAAAE,KAAA;oBACA;kBACA;kBACA;gBACA;gBACA;cAAA,SAAAQ,GAAA;gBAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;cAAA;gBAAAP,SAAA,CAAAS,CAAA;cAAA;cACA,SAAAC,CAAA,MAAAA,CAAA,GAAA/C,KAAA,CAAAmC,MAAA,EAAAY,CAAA;gBACA,SAAAC,CAAA,GAAAD,CAAA,MAAAC,CAAA,GAAAhD,KAAA,CAAAmC,MAAA,EAAAa,CAAA;kBACA,IAAAC,MAAA,GAAAjD,KAAA,CAAA+C,CAAA;kBACA,IAAAG,MAAA,GAAAlD,KAAA,CAAAgD,CAAA;kBACA;kBACA,IAAAG,MAAA,GAAAF,MAAA,CAAA/B,SAAA;kBACA,IAAAkC,IAAA,GAAAH,MAAA,CAAA9B,OAAA;kBACA,IAAAkC,MAAA,GAAAH,MAAA,CAAAhC,SAAA;kBACA,IAAAoC,IAAA,GAAAJ,MAAA,CAAA/B,OAAA;;kBAEA;kBACA,IAAAoC,OAAA;kBACA,IAAAH,IAAA,IAAAD,MAAA;oBACA;oBACA,IAAAG,IAAA,IAAAD,MAAA;sBACA;sBACAE,OAAA;oBACA;sBACA;sBACA,IAAAF,MAAA,IAAAF,MAAA,IAAAG,IAAA,IAAAF,IAAA;wBACAG,OAAA;sBACA;oBACA;kBACA;oBACA;oBACA,IAAAD,IAAA,IAAAD,MAAA;sBACA;sBACA,IAAAF,MAAA,IAAAE,MAAA,IAAAD,IAAA,IAAAE,IAAA;wBACAC,OAAA;sBACA;oBACA;sBACA;sBACA,IAAAJ,MAAA,IAAAE,MAAA,IAAAA,MAAA,GAAAD,IAAA,IACAC,MAAA,IAAAF,MAAA,IAAAA,MAAA,GAAAG,IAAA;wBACAC,OAAA;sBACA;oBACA;kBACA;kBAEA,IAAAA,OAAA;oBACArB,QAAA,KAAAE,KAAA;oBACA;kBACA;gBACA;cACA;cACAF,QAAA;YACA;UACA;QACA;MAEA;IACA;EACA;EACAsB,KAAA;IACAxD,KAAA;MACAyD,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAxD,MAAA,CAAAyD,IAAA,CAAAD,MAAA,EAAAvB,MAAA;UACA,KAAAxB,IAAA,GAAAiD,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACA,KAAAjD,IAAA,GACA+C,MAAA;YACAzC,UAAA,EAAAyC,MAAA,CAAAzC,UAAA,IAAAyC,MAAA,CAAAzC,UAAA,CAAAkB,MAAA,OACAuB,MAAA,CAAAzC,UAAA,GACA;cAAAC,SAAA;cAAAC,OAAA;YAAA;UAAA,EACA;QACA;MACA;MACA0C,SAAA;MACAC,IAAA;IACA;IACAnD,IAAA;MACA8C,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,IAAAA,MAAA,CAAAzC,UAAA,IAAAyC,MAAA,CAAAzC,UAAA,CAAAkB,MAAA;UACAuB,MAAA,CAAAzC,UAAA,CAAA8C,IAAA,WAAAC,CAAA,EAAAC,CAAA;YACA,IAAAD,CAAA,CAAA9C,SAAA,IAAA+C,CAAA,CAAA/C,SAAA;cACA,OAAA8C,CAAA,CAAA9C,SAAA,CAAAgD,aAAA,CAAAD,CAAA,CAAA/C,SAAA;YACA;YACA;UACA;QACA;QACA,KAAAiD,KAAA,UAAAT,MAAA;MACA;MACAI,IAAA;IACA;EACA;EACAM,OAAA;IACAC,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA3D,IAAA,CAAAG,UAAA;QACA,KAAAH,IAAA,CAAAI,QAAA;QACA,KAAAJ,IAAA,CAAAK,WAAA;MACA,gBAAAL,IAAA,CAAAG,UAAA;QACA,KAAAH,IAAA,CAAAK,WAAA;MACA,gBAAAL,IAAA,CAAAG,UAAA;QACA,KAAAH,IAAA,CAAAI,QAAA;MACA;;MAEA;MACA,KAAAwD,QAAA,CAAAC,IAAA;;MAEA;MACA,KAAAC,SAAA;QACAH,MAAA,CAAAI,KAAA,CAAAC,cAAA,CAAAC,aAAA;QACAN,MAAA,CAAAI,KAAA,CAAAC,cAAA,CAAAC,aAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAlE,IAAA,CAAAM,UAAA,CAAA6D,IAAA;QAAA5D,SAAA;QAAAC,OAAA;MAAA;IACA;IACA4D,eAAA,WAAAA,gBAAAC,KAAA;MACA,SAAArE,IAAA,CAAAM,UAAA,CAAAkB,MAAA;QACA,KAAAxB,IAAA,CAAAM,UAAA,CAAAgE,MAAA,CAAAD,KAAA;MACA;IACA;IACAE,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAH,MAAA,CAAAT,KAAA,CAAAC,cAAA,CAAAO,QAAA,WAAAK,KAAA;UACA,IAAAA,KAAA;YACAF,OAAA,CAAAF,MAAA,CAAAxE,IAAA;UACA;YACAwE,MAAA,CAAAZ,QAAA,CAAAiB,KAAA;YACAF,MAAA,KAAAlD,KAAA;UACA;QACA;MACA;IACA;IACAqD,SAAA,WAAAA,UAAA;MACA,KAAAf,KAAA,CAAAC,cAAA,CAAAe,WAAA;MACA,KAAA/E,IAAA;QACAC,EAAA,EAAAC,SAAA;QACAR,OAAA,OAAAA,OAAA;QACAS,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,UAAA;UAAAC,SAAA;UAAAC,OAAA;QAAA;QACAC,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}