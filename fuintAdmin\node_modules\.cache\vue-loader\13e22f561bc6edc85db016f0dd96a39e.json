{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue?vue&type=style&index=0&id=53e7202e&scoped=true&lang=css", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue", "mtime": 1754845237275}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1734093918845}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1734093920151}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1734093919263}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnRpbWUtY29uZmlnLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweDsKfQoKLnRpbWUtcmFuZ2VzLWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogMTBweDsKfQoKLnRpbWUtcmFuZ2UtaXRlbSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMTBweDsKfQoKLnRpbWUtc2VwYXJhdG9yIHsKICBtYXJnaW46IDAgNXB4OwogIGNvbG9yOiAjNjA2MjY2Owp9Cg=="}, {"version": 3, "sources": ["TimeConfigForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0UA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "TimeConfigForm.vue", "sourceRoot": "src/views/goods/components", "sourcesContent": ["<template>\n  <div class=\"time-config-container\">\n    <el-form ref=\"timeConfigForm\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"配置类型\" prop=\"configType\">\n            <el-radio-group v-model=\"form.configType\" @change=\"handleConfigTypeChange\">\n              <el-radio label=\"DAILY\">每日通用</el-radio>\n              <el-radio label=\"WEEKLY\">按周设置</el-radio>\n              <el-radio label=\"CUSTOM\">自定义日期</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 按周设置时显示星期选择 -->\n      <el-row v-if=\"form.configType === 'WEEKLY'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择星期\" prop=\"weekDays\">\n            <el-checkbox-group v-model=\"form.weekDays\">\n              <el-checkbox v-for=\"day in weekOptions\" :key=\"day.value\" :label=\"day.value\">\n                {{ day.label }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 自定义日期时显示日期选择 -->\n      <el-row v-if=\"form.configType === 'CUSTOM'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择日期\" prop=\"customDates\">\n            <el-date-picker\n              v-model=\"form.customDates\"\n              type=\"dates\"\n              placeholder=\"选择一个或多个日期\"\n              value-format=\"yyyy-MM-dd\"\n              :picker-options=\"pickerOptions\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 时间段设置 -->\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"时间段\" prop=\"timeRanges\">\n            <div class=\"time-ranges-container\">\n              <div v-for=\"(range, index) in form.timeRanges\" :key=\"index\" class=\"time-range-item\">\n                <el-time-picker\n                  v-model=\"range.startTime\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <span class=\"time-separator\">至</span>\n                <el-time-picker\n                  v-model=\"range.endTime\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <el-button\n                  type=\"danger\"\n                  icon=\"el-icon-delete\"\n                  size=\"mini\"\n                  circle\n                  @click=\"removeTimeRange(index)\"\n                  :disabled=\"form.timeRanges.length === 1\"\n                />\n              </div>\n              <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"addTimeRange\">\n                添加时间段\n              </el-button>\n              <div class=\"form-tip\">提示：时间段不能重叠，支持跨天时间段配置（如22:00-02:00）</div>\n            </div>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-radio-group v-model=\"form.status\">\n              <el-radio label=\"A\">启用</el-radio>\n              <el-radio label=\"N\">禁用</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TimeConfigForm',\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    goodsId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      form: {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      },\n      weekOptions: [\n        { label: '周一', value: 1 },\n        { label: '周二', value: 2 },\n        { label: '周三', value: 3 },\n        { label: '周四', value: 4 },\n        { label: '周五', value: 5 },\n        { label: '周六', value: 6 },\n        { label: '周日', value: 7 }\n      ],\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7\n        }\n      },\n      rules: {\n        configType: [\n          { required: true, message: '请选择配置类型', trigger: 'change' }\n        ],\n        weekDays: [\n          {\n            required: true,\n            message: '请至少选择一个星期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'WEEKLY' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个星期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        customDates: [\n          {\n            required: true,\n            message: '请至少选择一个日期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'CUSTOM' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个日期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        timeRanges: [\n          {\n            required: true,\n            message: '请设置至少一个时间段',\n            trigger: 'blur',\n            validator: (rule, value, callback) => {\n              if (!value || value.length === 0) {\n                callback(new Error('请设置至少一个时间段'))\n              } else {\n                for (let range of value) {\n                  if (!range.startTime || !range.endTime) {\n                    callback(new Error('请完善时间段设置'))\n                    return\n                  }\n                  // 移除开始时间必须小于结束时间的验证，允许跨天时间段（如22:00-02:00）\n                }\n                // 检查时间段是否重叠\n                for (let i = 0; i < value.length; i++) {\n                  for (let j = i + 1; j < value.length; j++) {\n                    const range1 = value[i];\n                    const range2 = value[j];\n                    // 检查时间段是否重叠（考虑跨天情况）\n                    const start1 = range1.startTime;\n                    const end1 = range1.endTime;\n                    const start2 = range2.startTime;\n                    const end2 = range2.endTime;\n\n                    // 处理跨天时间段重叠检查\n                    let overlap = false;\n                    if (end1 <= start1) {\n                      // range1 是跨天时间段 (如 22:00-02:00)\n                      if (end2 <= start2) {\n                        // range2 也是跨天时间段\n                        overlap = true; // 两个跨天时间段总是重叠\n                      } else {\n                        // range2 不跨天\n                        if (start2 >= start1 || end2 <= end1) {\n                          overlap = true;\n                        }\n                      }\n                    } else {\n                      // range1 不跨天\n                      if (end2 <= start2) {\n                        // range2 是跨天时间段\n                        if (start1 >= start2 || end1 <= end2) {\n                          overlap = true;\n                        }\n                      } else {\n                        // range2 也不跨天\n                        if ((start1 <= start2 && start2 < end1) ||\n                            (start2 <= start1 && start1 < end2)) {\n                          overlap = true;\n                        }\n                      }\n                    }\n\n                    if (overlap) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  }\n                }\n                callback()\n              }\n            }\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        if (newVal && Object.keys(newVal).length > 0) {\n          this.form = {\n            ...this.form,\n            ...newVal,\n            timeRanges: newVal.timeRanges && newVal.timeRanges.length > 0\n              ? newVal.timeRanges\n              : [{ startTime: '', endTime: '' }]\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    form: {\n      handler(newVal) {\n        // 对时间段进行排序\n        if (newVal.timeRanges && newVal.timeRanges.length > 1) {\n          newVal.timeRanges.sort((a, b) => {\n            if (a.startTime && b.startTime) {\n              return a.startTime.localeCompare(b.startTime)\n            }\n            return 0\n          })\n        }\n        this.$emit('input', newVal)\n      },\n      deep: true\n    }\n  },\n  methods: {\n    handleConfigTypeChange() {\n      // 切换配置类型时清空相关数据\n      if (this.form.configType === 'DAILY') {\n        this.form.weekDays = []\n        this.form.customDates = []\n      } else if (this.form.configType === 'WEEKLY') {\n        this.form.customDates = []\n      } else if (this.form.configType === 'CUSTOM') {\n        this.form.weekDays = []\n      }\n\n      // 提示用户配置类型已切换\n      this.$message.info('已切换配置类型，请重新设置相关选项')\n\n      // 验证表单\n      this.$nextTick(() => {\n        this.$refs.timeConfigForm.validateField('weekDays')\n        this.$refs.timeConfigForm.validateField('customDates')\n      })\n    },\n    addTimeRange() {\n      this.form.timeRanges.push({ startTime: '', endTime: '' })\n    },\n    removeTimeRange(index) {\n      if (this.form.timeRanges.length > 1) {\n        this.form.timeRanges.splice(index, 1)\n      }\n    },\n    validate() {\n      return new Promise((resolve, reject) => {\n        this.$refs.timeConfigForm.validate(valid => {\n          if (valid) {\n            resolve(this.form)\n          } else {\n            this.$message.error('表单验证失败，请检查输入内容')\n            reject(new Error('表单验证失败'))\n          }\n        })\n      })\n    },\n    resetForm() {\n      this.$refs.timeConfigForm.resetFields()\n      this.form = {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.time-config-container {\n  padding: 20px;\n}\n\n.time-ranges-container {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.time-range-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  margin: 0 5px;\n  color: #606266;\n}\n</style>\n"]}]}