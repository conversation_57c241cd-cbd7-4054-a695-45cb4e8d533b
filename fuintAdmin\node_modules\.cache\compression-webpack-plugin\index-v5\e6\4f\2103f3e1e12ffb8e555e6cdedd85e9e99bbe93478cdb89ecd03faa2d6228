
4c19f84eea1e738fe05c39771ded3cb6fb8592fa	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"cb1de6d4ad7db5fd5c5ba03cc8609488\"}","integrity":"sha512-9XiEoGPFbSlSUE6HJhmwqleRE4unNNpvISj4cFVl5IctHuyMeRoy+y8/FVhoucKiIWEe+IZu2JPdrE4jZfj1Zw==","time":1754920925383,"size":6668960}