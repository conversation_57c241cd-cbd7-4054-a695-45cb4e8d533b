
56b331fa2f4685c4f19bbb714558da8e901911db	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"21167ebde50dbd6ec225ce040b8e7739\"}","integrity":"sha512-IiC11B2UD/i/y+FWNbKWaDlpkj6oqlPdwQRqDDGP2zzNxGL/pbb2RchqERWXrp36P5IM14NVwxbEY3nQVgGoZg==","time":1754921522917,"size":6671847}