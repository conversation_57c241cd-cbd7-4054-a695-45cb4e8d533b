{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\index.vue?vue&type=template&id=24560e14", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\goods\\index.vue", "mtime": 1754838939888}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1734093920186}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}