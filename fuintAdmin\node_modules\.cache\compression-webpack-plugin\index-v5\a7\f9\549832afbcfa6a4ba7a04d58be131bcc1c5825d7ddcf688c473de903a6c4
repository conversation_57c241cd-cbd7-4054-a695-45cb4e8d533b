
8774c06e6b99ea90a21eeb6aca097c790c6a9060	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.13b061d45fd710fe44b5.hot-update.js\",\"contentHash\":\"142e38ed1df67308dd380f70c5e59744\"}","integrity":"sha512-fa/JoR3+XdzaYEtnzB0oijjmT12GjEGqjx51YJt5oYWQWAKWPAAeYRlFO/fcDVhYCcItuBgrE7I6/VtdTAlwaQ==","time":1754922009393,"size":48247}