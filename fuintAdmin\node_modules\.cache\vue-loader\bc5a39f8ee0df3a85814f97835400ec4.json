{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\printer\\index.vue", "mtime": 1754922008756}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFByaW50ZXJMaXN0LCB1cGRhdGVQcmludGVyU3RhdHVzLCBnZXRQcmludGVySW5mbywgc2F2ZVByaW50ZXIsIHNhdmVTZXR0aW5nLCBnZXRTZXR0aW5nSW5mbywgcHJpbnRFeHBpcnlMYWJlbCwgZ2V0Q2F0ZWdvcnlMaXN0LCBzYXZlUHJpbnRlckNhdGVnb3JpZXMsIGdldFByaW50ZXJDYXRlZ29yaWVzIH0gZnJvbSAiQC9hcGkvcHJpbnRlciI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUHJpbnRlckxpc3QiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOm7mOiupOaOkuW6jwogICAgICBkZWZhdWx0U29ydDoge3Byb3A6ICdzb3J0Jywgb3JkZXI6ICdhc2NlbmRpbmcnfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHsgaWQ6ICcnLCBuYW1lOiAnJywgc246ICcnLCBiZWZvcmVQYXk6ICdOJywgYWZ0ZXJQYXk6ICdZJywgYXV0b1ByaW50OiAnWScsIHN0b3JlSWQ6IDAsIHR5cGU6ICdSRUNFSVBUJywgIHN0YXR1czogIkEiLCBzb3J0OiAwIH0sCiAgICAgIC8vIOW6l+mTuuWIl+ihqAogICAgICBzdG9yZUxpc3Q6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBuYW1lOiAnJywKICAgICAgICBzbjogJycsCiAgICAgICAgc3RvcmVJZDogJycsCiAgICAgICAgc3RhdHVzOiAnJwogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBuYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAyMDAsIG1lc3NhZ2U6ICflkI3np7Dplb/luqblv4Xpobvku4vkuo4yIOWSjCA1MCDkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgc246IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnvJblj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDIwMCwgbWVzc2FnZTogJ+e8luWPt+mVv+W6puW/hemhu+S7i+S6jjIg5ZKMIDY0IOS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgfSwKICAgICAgLy8g5omT5Y2w6K6+572uCiAgICAgIG9wZW5TZXR0aW5nOiBmYWxzZSwKICAgICAgc2V0dGluZ0Zvcm06IHsgdXNlck5hbWU6ICcnLCB1c2VyS2V5OiAnJywgZW5hYmxlOiAnWScgfSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHNldHRpbmdSdWxlczogewogICAgICAgIHVzZXJOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAyMDAsIG1lc3NhZ2U6ICflkI3np7Dplb/luqblv4Xpobvku4vkuo4yIOWSjCA1MCDkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgdXNlcktleTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue8luWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDIsIG1heDogMjAwLCBtZXNzYWdlOiAn57yW5Y+36ZW/5bqm5b+F6aG75LuL5LqOMiDlkowgNjQg5LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICB9LAogICAgICAvLyDmiZPljbDmoIfnrb7nm7jlhbMKICAgICAgcHJpbnRMYWJlbE9wZW46IGZhbHNlLAogICAgICBjdXJyZW50UHJpbnRlcklkOiBudWxsLAogICAgICBsYWJlbEZvcm06IHsKICAgICAgICBuYW1lOiAnJywKICAgICAgICBwcm9kdWN0RGF0ZTogJycsCiAgICAgICAgZXhwaXJlRGF5czogMzAKICAgICAgfSwKICAgICAgbGFiZWxSdWxlczogewogICAgICAgIG5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcHJvZHVjdERhdGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlJ/kuqfml6XmnJ/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgZXhwaXJlRGF5czogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuacieaViOacn+WkqeaVsOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtYXg6IDk5OSwgbWVzc2FnZTogJ+acieaViOacn+WkqeaVsOW/hemhu+WcqDEtOTk55LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8vIOWIhuexu+mFjee9ruebuOWFswogICAgICBjYXRlZ29yeUNvbmZpZ09wZW46IGZhbHNlLAogICAgICBjYXRlZ29yeUxvYWRpbmc6IGZhbHNlLAogICAgICBjYXRlZ29yeVN1Ym1pdHRpbmc6IGZhbHNlLAogICAgICBjYXRlZ29yeUxpc3Q6IFtdLAogICAgICBzZWxlY3RlZENhdGVnb3JpZXM6IFtdLAogICAgICBjdXJyZW50UHJpbnRlcjoge30sCiAgICAgIHByaW50ZXJDYXRlZ29yaWVzTWFwOiB7fSAvLyDlrZjlgqjmr4/kuKrmiZPljbDmnLrnmoTliIbnsbvlhbPogZQKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDmn6Xor6LliJfooagKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGdldFByaW50ZXJMaXN0KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oIHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLmRhdGEucGFnaW5hdGlvblJlc3BvbnNlLmNvbnRlbnQ7CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS5wYWdpbmF0aW9uUmVzcG9uc2UudG90YWxFbGVtZW50czsKICAgICAgICAgIHRoaXMuc3RvcmVMaXN0ID0gcmVzcG9uc2UuZGF0YS5zdG9yZUxpc3Q7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIC8vIOWKoOi9veaJk+WNsOacuuWIhuexu+WFs+iBlOaVsOaNrgogICAgICAgICAgdGhpcy5sb2FkUHJpbnRlckNhdGVnb3JpZXMoKTsKICAgICAgICB9CiAgICAgICk7CiAgICB9LAogICAgLy8g5pCc57Si5oyJ6ZKu5pON5L2cCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLy8g5omT5Y2w6K6+572uCiAgICBoYW5kbGVTZXR0aW5nKCkgewogICAgICBnZXRTZXR0aW5nSW5mbygpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuc2V0dGluZ0Zvcm0udXNlck5hbWUgPSByZXNwb25zZS5kYXRhLnVzZXJOYW1lOwogICAgICAgIHRoaXMuc2V0dGluZ0Zvcm0udXNlcktleSA9IHJlc3BvbnNlLmRhdGEudXNlcktleTsKICAgICAgICB0aGlzLnNldHRpbmdGb3JtLmVuYWJsZSA9IHJlc3BvbnNlLmRhdGEuZW5hYmxlOwogICAgICAgIHRoaXMub3BlblNldHRpbmcgPSB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDph43nva7mjInpkq7mk43kvZwKICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy4kcmVmcy50YWJsZXMuc29ydCh0aGlzLmRlZmF1bHRTb3J0LnByb3AsIHRoaXMuZGVmYXVsdFNvcnQub3JkZXIpCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDnirbmgIHkv67mlLkKICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09ICJBIiA/ICLlkK/nlKgiIDogIuemgeeUqCI7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgScgKyB0ZXh0ICsgJyInICsgcm93LnRpdGxlICsgJyLlkJfvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiB1cGRhdGVQcmludGVyU3RhdHVzKHJvdy5pZCwgcm93LnN0YXR1cyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24oKSB7CiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICJOIiA/ICJBIiA6ICJOIjsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8vIOaOkuW6j+inpuWPkeS6i+S7tgogICAgaGFuZGxlU29ydENoYW5nZShjb2x1bW4sIHByb3AsIG9yZGVyKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMub3JkZXJCeUNvbHVtbiA9IGNvbHVtbi5wcm9wOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzQXNjID0gY29sdW1uLm9yZGVyOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDmlrDlop7mjInpkq7mk43kvZwKICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIuaWsOWinuaJk+WNsOiuvuWkhyI7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiAiIiwKICAgICAgICBuYW1lOiAiIiwKICAgICAgICBzbjogIiIsCiAgICAgICAgc3RvcmVJZDogIiIsCiAgICAgICAgYXV0b1ByaW50OiAiWSIsCiAgICAgICAgdHlwZTogJ1JFQ0VJUFQnLAogICAgICAgIGJlZm9yZVBheTogJ1knLAogICAgICAgIGFmdGVyUGF5OiAnWScsCiAgICAgICAgZGVzY3JpcHRpb246ICIiLAogICAgICAgIHN0YXR1czogIkEiLAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOaPkOS6pOaMiemSrgogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkKSB7CiAgICAgICAgICAgICAgc2F2ZVByaW50ZXIodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgc2F2ZVByaW50ZXIodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOS/ruaUueaMiemSruaTjeS9nAogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOwogICAgICBnZXRQcmludGVySW5mbyhpZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YS5wcmludGVySW5mbzsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi57yW6L6R5omT5Y2w5py6IjsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yig6Zmk5oyJ6ZKu5pON5L2cCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IG5hbWUgPSByb3cubmFtZQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaQiJyArIG5hbWUgKyAnIuaJk+WNsOacuu+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIHVwZGF0ZVByaW50ZXJTdGF0dXMocm93LmlkLCAnRCcpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5o+Q5Lqk6K6+572u5oyJ6ZKuCiAgICBzdWJtaXRTZXR0aW5nRm9ybTogZnVuY3Rpb24oKSB7CiAgICAgIHRoaXMuJHJlZnNbInNldHRpbmdGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICBzYXZlU2V0dGluZyh0aGlzLnNldHRpbmdGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv53lrZjorr7nva7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW5TZXR0aW5nID0gZmFsc2U7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI6K6+572u5oyJ6ZKuCiAgICBjYW5jZWxTZXR0aW5nKCkgewogICAgICB0aGlzLm9wZW5TZXR0aW5nID0gZmFsc2U7CiAgICB9LAogICAgLy8g5omT5Y2w5qCH562+5oyJ6ZKu5pON5L2cCiAgICAvLyDorqHnrpfmnInmlYjmnJ/ml6XmnJ8KICAgIGNhbGN1bGF0ZUV4cGlyZURhdGUocHJvZHVjdERhdGUsIGRheXMpIHsKICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHByb2R1Y3REYXRlKTsKICAgICAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpICsgZGF5cyk7CiAgICAgIHJldHVybiBkYXRlLmdldEZ1bGxZZWFyKCkgKyAnLScgKwogICAgICAgICAgICAgU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJykgKyAnLScgKwogICAgICAgICAgICAgU3RyaW5nKGRhdGUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOwogICAgfSwKICAgIC8vIOaJk+WNsOagh+etvuaMiemSruaTjeS9nAogICAgaGFuZGxlUHJpbnRMYWJlbChyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50UHJpbnRlcklkID0gcm93LmlkOwogICAgICB0aGlzLmxhYmVsRm9ybSA9IHsKICAgICAgICBuYW1lOiAnJywKICAgICAgICBwcm9kdWN0RGF0ZTogJycsCiAgICAgICAgZXhwaXJlRGF5czogMzAKICAgICAgfTsKICAgICAgdGhpcy5wcmludExhYmVsT3BlbiA9IHRydWU7CiAgICB9LAogICAgLy8g5Y+W5raI5omT5Y2w5qCH562+CiAgICBjYW5jZWxQcmludExhYmVsKCkgewogICAgICB0aGlzLnByaW50TGFiZWxPcGVuID0gZmFsc2U7CiAgICAgIHRoaXMuY3VycmVudFByaW50ZXJJZCA9IG51bGw7CiAgICAgIHRoaXMubGFiZWxGb3JtID0gewogICAgICAgIG5hbWU6ICcnLAogICAgICAgIHByb2R1Y3REYXRlOiAnJywKICAgICAgICBleHBpcmVEYXlzOiAzMAogICAgICB9OwogICAgfSwKICAgIC8vIOaPkOS6pOaJk+WNsOagh+etvgogICAgc3VibWl0TGFiZWxGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJsYWJlbEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBjb25zdCBleHBpcmVEYXRlID0gdGhpcy5jYWxjdWxhdGVFeHBpcmVEYXRlKHRoaXMubGFiZWxGb3JtLnByb2R1Y3REYXRlLCB0aGlzLmxhYmVsRm9ybS5leHBpcmVEYXlzKTsKICAgICAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgICAgIGlkOiB0aGlzLmN1cnJlbnRQcmludGVySWQsCiAgICAgICAgICAgIG5hbWU6IHRoaXMubGFiZWxGb3JtLm5hbWUsCiAgICAgICAgICAgIHByb2R1Y3REYXRlOiB0aGlzLmxhYmVsRm9ybS5wcm9kdWN0RGF0ZSwKICAgICAgICAgICAgZXhwaXJlRGF0ZTogZXhwaXJlRGF0ZSwKICAgICAgICAgICAgcHJpbnRlcklkOiB0aGlzLmN1cnJlbnRQcmludGVySWQKICAgICAgICAgIH07CiAgICAgICAgICBwcmludEV4cGlyeUxhYmVsKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmiZPljbDmiJDlip8iKTsKICAgICAgICAgICAgdGhpcy5wcmludExhYmVsT3BlbiA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLmNhbmNlbFByaW50TGFiZWwoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yqg6L295ZWG5ZOB5YiG57G75YiX6KGoCiAgICAgICAgbG9hZENhdGVnb3J5TGlzdChzdG9yZUlkKSB7CiAgICAgICAgICBjb25zdCBwYXJhbXMgPSB7fTsKICAgICAgICAgIGlmIChzdG9yZUlkICE9PSB1bmRlZmluZWQgJiYgc3RvcmVJZCAhPT0gbnVsbCkgewogICAgICAgICAgICBwYXJhbXMuc3RvcmVJZCA9IHN0b3JlSWQ7CiAgICAgICAgICB9CiAgICAgICAgICBnZXRDYXRlZ29yeUxpc3QocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgdGhpcy5jYXRlZ29yeUxpc3QgPSByZXNwb25zZS5kYXRhIHx8IFtdOwogICAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3liIbnsbvliJfooajlpLHotKU6JywgZXJyb3IpOwogICAgICAgICAgICB0aGlzLmNhdGVnb3J5TGlzdCA9IFtdOwogICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgIC8vIOWKoOi9veaJk+WNsOacuuWIhuexu+WFs+iBlOaVsOaNrgogICAgbG9hZFByaW50ZXJDYXRlZ29yaWVzKCkgewogICAgICB0aGlzLnByaW50ZXJDYXRlZ29yaWVzTWFwID0ge307CiAgICAgIGNvbnN0IGxhYmVsUHJpbnRlcnMgPSB0aGlzLmxpc3QuZmlsdGVyKHByaW50ZXIgPT4gcHJpbnRlci50eXBlID09PSAnTEFCRUwnKTsKCiAgICAgIGxhYmVsUHJpbnRlcnMuZm9yRWFjaChwcmludGVyID0+IHsKICAgICAgICBnZXRQcmludGVyQ2F0ZWdvcmllcyhwcmludGVyLmlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnByaW50ZXJDYXRlZ29yaWVzTWFwLCBwcmludGVyLmlkLCByZXNwb25zZS5kYXRhIHx8IFtdKTsKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKGDliqDovb3miZPljbDmnLoke3ByaW50ZXIuaWR95YiG57G75YWz6IGU5aSx6LSlOmAsIGVycm9yKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6I635Y+W5omT5Y2w5py65YWz6IGU55qE5YiG57G75ZCN56ewCiAgICBnZXRQcmludGVyQ2F0ZWdvcnlOYW1lcyhwcmludGVySWQpIHsKICAgICAgY29uc3QgcHJpbnRlckNhdGVnb3JpZXMgPSB0aGlzLnByaW50ZXJDYXRlZ29yaWVzTWFwW3ByaW50ZXJJZF0gfHwgW107CiAgICAgIHJldHVybiBwcmludGVyQ2F0ZWdvcmllcy5tYXAocGMgPT4gewogICAgICAgIGNvbnN0IGNhdGVnb3J5ID0gdGhpcy5jYXRlZ29yeUxpc3QuZmluZChjID0+IGMuaWQgPT09IHBjLmNhdGVJZCk7CiAgICAgICAgcmV0dXJuIGNhdGVnb3J5ID8gY2F0ZWdvcnkubmFtZSA6IGDliIbnsbske3BjLmNhdGVJZH1gOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmiZPlvIDliIbnsbvphY3nva7lr7nor53moYYKICAgICAgICBoYW5kbGVDYXRlZ29yeUNvbmZpZyhyb3cpIHsKICAgICAgICAgIHRoaXMuY3VycmVudFByaW50ZXIgPSByb3c7CiAgICAgICAgICB0aGlzLmNhdGVnb3J5Q29uZmlnT3BlbiA9IHRydWU7CgogICAgICAgICAgLy8g5Yqg6L295b2T5YmN5omT5Y2w5py655qE5YiG57G75YWz6IGUCiAgICAgICAgICB0aGlzLmNhdGVnb3J5TG9hZGluZyA9IHRydWU7CiAgICAgICAgICBnZXRQcmludGVyQ2F0ZWdvcmllcyhyb3cuaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICBjb25zdCBwcmludGVyQ2F0ZWdvcmllcyA9IHJlc3BvbnNlLmRhdGEgfHwgW107CiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yaWVzID0gcHJpbnRlckNhdGVnb3JpZXMubWFwKHBjID0+IHBjLmNhdGVJZCk7CiAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaJk+WNsOacuuWIhuexu+WFs+iBlOWksei0pTonLCBlcnJvcik7CiAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KTsKCiAgICAgICAgICAvLyDph43mlrDliqDovb3or6Xlupfpk7rnmoTllYblk4HliIbnsbsKICAgICAgICAgIHRoaXMubG9hZENhdGVnb3J5TGlzdChyb3cuc3RvcmVJZCk7CiAgICAgICAgfSwKICAgIC8vIOaPkOS6pOWIhuexu+mFjee9rgogICAgc3VibWl0Q2F0ZWdvcnlDb25maWcoKSB7CiAgICAgIHRoaXMuY2F0ZWdvcnlTdWJtaXR0aW5nID0gdHJ1ZTsKCiAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgcHJpbnRlcklkOiB0aGlzLmN1cnJlbnRQcmludGVyLmlkLAogICAgICAgIGNhdGVJZHM6IHRoaXMuc2VsZWN0ZWRDYXRlZ29yaWVzCiAgICAgIH07CgogICAgICBzYXZlUHJpbnRlckNhdGVnb3JpZXMoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6YWN572u5L+d5a2Y5oiQ5YqfIik7CiAgICAgICAgdGhpcy5jYXRlZ29yeUNvbmZpZ09wZW4gPSBmYWxzZTsKICAgICAgICB0aGlzLmNhdGVnb3J5U3VibWl0dGluZyA9IGZhbHNlOwogICAgICAgIC8vIOmHjeaWsOWKoOi9veWIhuexu+WFs+iBlOaVsOaNrgogICAgICAgIHRoaXMubG9hZFByaW50ZXJDYXRlZ29yaWVzKCk7CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCfkv53lrZjliIbnsbvphY3nva7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuY2F0ZWdvcnlTdWJtaXR0aW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOWIhuexu+mFjee9rgogICAgY2FuY2VsQ2F0ZWdvcnlDb25maWcoKSB7CiAgICAgIHRoaXMuY2F0ZWdvcnlDb25maWdPcGVuID0gZmFsc2U7CiAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yaWVzID0gW107CiAgICAgIHRoaXMuY3VycmVudFByaW50ZXIgPSB7fTsKICAgIH0sCiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/printer", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" class=\"main-search\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"设备名称\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入打印机名称\"\n          clearable\n          style=\"width: 200px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"编号\" prop=\"sn\">\n        <el-input\n          v-model=\"queryParams.sn\"\n          placeholder=\"请输入打印机编号\"\n          clearable\n          style=\"width: 200px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"所属店铺\" prop=\"store\">\n        <el-select\n          v-model=\"queryParams.storeId\"\n          placeholder=\"所属店铺\"\n          clearable\n          style=\"width: 180px\"\n        >\n          <el-option v-for=\"storeInfo in storeList\" :key=\"storeInfo.id\" :label=\"storeInfo.name\" :value=\"storeInfo.id\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 100px\"\n        >\n          <el-option key=\"A\" label=\"启用\" value=\"A\"/>\n          <el-option key=\"N\" label=\"禁用\" value=\"N\"/>\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['printer:index']\"\n        >新增云打印设备</el-button>\n\n        <!-- <el-button type=\"primary\" icon=\"el-icon-setting\" size=\"mini\" v-hasPermi=\"['printer:setting']\" @click=\"handleSetting\">设置芯烨账号</el-button> -->\n      </el-form-item>\n    </el-form>\n\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\n      <el-table-column label=\"ID\" prop=\"id\" width=\"55\"/>\n      <el-table-column label=\"设备名称\" align=\"center\" prop=\"name\" />\n      <el-table-column label=\"设备编号\" align=\"center\" prop=\"sn\" />\n      <el-table-column label=\"类型\" align=\"center\" prop=\"type\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.type === 'LABEL' ? 'success' : 'primary'\" size=\"small\">\n            {{ scope.row.type === 'LABEL' ? '标签' : '小票' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所属店铺\" align=\"center\" prop=\"store\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.storeId && scope.row.storeId > 0\">\n              <span>{{ getName(storeList, scope.row.storeId) }}</span>\n          </span>\n          <span v-else>公共所有</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.updateTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"A\"\n            inactive-value=\"N\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            v-hasPermi=\"['printer:index']\"\n            @click=\"handleUpdate(scope.row)\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            v-hasPermi=\"['printer:index']\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-printer\"\n            v-hasPermi=\"['printer:index']\"\n            v-if=\"scope.row.type === 'LABEL'\"\n            @click=\"handlePrintLabel(scope.row)\"\n          >打印标签</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-setting\"\n            v-hasPermi=\"['printer:index']\"\n            v-if=\"scope.row.type === 'LABEL'\"\n            @click=\"handleCategoryConfig(scope.row)\"\n          >分类配置</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.page\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改对话框 start-->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" class=\"common-dialog\" width=\"700px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备名称\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入打印机名称\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备编号\" prop=\"sn\">\n              <el-input v-model=\"form.sn\" placeholder=\"请输入打印机编号\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"所属店铺\" prop=\"storeId\">\n              <el-select\n                v-model=\"form.storeId\"\n                style=\"width: 260px\"\n                placeholder=\"所属店铺，空则为公共所有\"\n              >\n                <el-option :key=\"0\" label=\"公共所有\" v-if=\"!this.$store.getters.storeId\" :value=\"0\"/>\n                <el-option v-for=\"storeInfo in storeList\" :key=\"storeInfo.id\" :label=\"storeInfo.name\" :value=\"storeInfo.id\"/>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"打印机类型\">\n              <el-radio-group v-model=\"form.type\">\n                <el-radio key=\"RECEIPT\" label=\"RECEIPT\" value=\"RECEIPT\">小票打印机</el-radio>\n                <el-radio key=\"LABEL\" label=\"LABEL\" value=\"LABEL\">标签打印机</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"支付前打印\">\n              <el-radio-group v-model=\"form.beforePay\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">是</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"支付后打印\">\n              <el-radio-group v-model=\"form.afterPay\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">是</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"打印订单\">\n              <el-radio-group v-model=\"form.autoPrint\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">自动打印</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">手动打印</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注信息\">\n              <el-input v-model=\"form.description\" type=\"textarea\" rows=\"5\" placeholder=\"请输入备注信息\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio key=\"A\" label=\"A\" value=\"A\">启用</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">禁用</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n        <el-button @click=\"cancel\">取消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改对话框 end-->\n\n    <!-- 打印设置对话框 start-->\n    <el-dialog title=\"芯烨云打印设置\" :visible.sync=\"openSetting\" class=\"common-dialog\" width=\"700px\" append-to-body>\n      <el-form ref=\"settingForm\" :model=\"settingForm\" :rules=\"settingRules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"开发者ID\" prop=\"userName\">\n              <el-input v-model=\"settingForm.userName\" placeholder=\"请输入开发者ID\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"开发者密钥\" prop=\"userKey\">\n              <el-input v-model=\"settingForm.userKey\" placeholder=\"请输入开发者密钥\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"settingForm.enable\">\n                <el-radio key=\"Y\" label=\"Y\" value=\"Y\">启用</el-radio>\n                <el-radio key=\"N\" label=\"N\" value=\"N\">禁用</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitSettingForm\">确定</el-button>\n        <el-button @click=\"cancelSetting\">取消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 打印设置对话框 end-->\n\n    <!-- 打印标签对话框 -->\n    <el-dialog title=\"打印标签\" :visible.sync=\"printLabelOpen\" class=\"common-dialog\" width=\"500px\" append-to-body>\n      <el-form ref=\"labelForm\" :model=\"labelForm\" :rules=\"labelRules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"名称\" prop=\"name\">\n              <el-input v-model=\"labelForm.name\" placeholder=\"请输入名称\" maxlength=\"200\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产日期\" prop=\"productDate\">\n              <el-date-picker\n                v-model=\"labelForm.productDate\"\n                type=\"date\"\n                style=\"width: 100%\"\n                placeholder=\"选择生产日期\"\n                value-format=\"yyyy-MM-dd\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"有效期(天)\" prop=\"expireDays\">\n              <el-input-number\n                v-model=\"labelForm.expireDays\"\n                :min=\"1\"\n                :max=\"999\"\n                style=\"width: 100%\"\n                placeholder=\"请输入有效期天数\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitLabelForm\">确定</el-button>\n        <el-button @click=\"cancelPrintLabel\">取消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 分类配置对话框 -->\n    <el-dialog title=\"打印机分类配置\" :visible.sync=\"categoryConfigOpen\" class=\"common-dialog\" width=\"600px\" append-to-body>\n      <div style=\"margin-bottom: 20px;\">\n        <el-alert\n          title=\"配置说明\"\n          description=\"为标签打印机配置对应的商品分类。下方显示该打印机所属店铺的所有商品分类，可多选配置。打印时会优先使用配置了分类的打印机，未配置的使用通用打印机。\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon>\n        </el-alert>\n      </div>\n\n      <el-form label-width=\"120px\">\n        <el-form-item label=\"打印机\">\n          <el-input :value=\"currentPrinter.name + ' (' + currentPrinter.sn + ')'\" disabled />\n        </el-form-item>\n\n        <el-form-item label=\"关联分类\">\n          <div v-loading=\"categoryLoading\">\n            <el-checkbox-group v-model=\"selectedCategories\" class=\"category-checkbox-group\">\n              <el-checkbox\n                v-for=\"category in categoryList\"\n                :key=\"category.id\"\n                :label=\"category.id\"\n                class=\"category-checkbox\"\n              >\n                {{ category.name }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitCategoryConfig\" :loading=\"categorySubmitting\">保存配置</el-button>\n        <el-button @click=\"cancelCategoryConfig\">取消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPrinterList, updatePrinterStatus, getPrinterInfo, savePrinter, saveSetting, getSettingInfo, printExpiryLabel, getCategoryList, savePrinterCategories, getPrinterCategories } from \"@/api/printer\";\nexport default {\n  name: \"PrinterList\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 标题\n      title: \"\",\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 是否显示弹出层\n      open: false,\n      // 默认排序\n      defaultSort: {prop: 'sort', order: 'ascending'},\n      // 表单参数\n      form: { id: '', name: '', sn: '', beforePay: 'N', afterPay: 'Y', autoPrint: 'Y', storeId: 0, type: 'RECEIPT',  status: \"A\", sort: 0 },\n      // 店铺列表\n      storeList: [],\n      // 查询参数\n      queryParams: {\n        page: 1,\n        pageSize: 10,\n        name: '',\n        sn: '',\n        storeId: '',\n        status: ''\n      },\n      // 表单校验\n      rules: {\n        name: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '名称长度必须介于2 和 50 之间', trigger: 'blur' }\n        ],\n        sn: [\n          { required: true, message: \"编号不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '编号长度必须介于2 和 64 之间', trigger: 'blur' }\n        ],\n      },\n      // 打印设置\n      openSetting: false,\n      settingForm: { userName: '', userKey: '', enable: 'Y' },\n      // 表单校验\n      settingRules: {\n        userName: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '名称长度必须介于2 和 50 之间', trigger: 'blur' }\n        ],\n        userKey: [\n          { required: true, message: \"编号不能为空\", trigger: \"blur\" },\n          { min: 2, max: 200, message: '编号长度必须介于2 和 64 之间', trigger: 'blur' }\n        ],\n      },\n      // 打印标签相关\n      printLabelOpen: false,\n      currentPrinterId: null,\n      labelForm: {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      },\n      labelRules: {\n        name: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" }\n        ],\n        productDate: [\n          { required: true, message: \"生产日期不能为空\", trigger: \"blur\" }\n        ],\n        expireDays: [\n          { required: true, message: \"有效期天数不能为空\", trigger: \"blur\" },\n          { type: 'number', min: 1, max: 999, message: '有效期天数必须在1-999之间', trigger: 'blur' }\n        ]\n      },\n      // 分类配置相关\n      categoryConfigOpen: false,\n      categoryLoading: false,\n      categorySubmitting: false,\n      categoryList: [],\n      selectedCategories: [],\n      currentPrinter: {},\n      printerCategoriesMap: {} // 存储每个打印机的分类关联\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    // 查询列表\n    getList() {\n      this.loading = true;\n      getPrinterList(this.queryParams).then( response => {\n          this.list = response.data.paginationResponse.content;\n          this.total = response.data.paginationResponse.totalElements;\n          this.storeList = response.data.storeList;\n          this.loading = false;\n          // 加载打印机分类关联数据\n          this.loadPrinterCategories();\n        }\n      );\n    },\n    // 搜索按钮操作\n    handleQuery() {\n      this.queryParams.page = 1;\n      this.getList();\n    },\n    // 打印设置\n    handleSetting() {\n      getSettingInfo().then(response => {\n        this.settingForm.userName = response.data.userName;\n        this.settingForm.userKey = response.data.userKey;\n        this.settingForm.enable = response.data.enable;\n        this.openSetting = true;\n      });\n    },\n    // 重置按钮操作\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\n      this.handleQuery();\n    },\n    // 状态修改\n    handleStatusChange(row) {\n      let text = row.status == \"A\" ? \"启用\" : \"禁用\";\n      this.$modal.confirm('确认要' + text + '\"' + row.title + '\"吗？').then(function() {\n        return updatePrinterStatus(row.id, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"N\" ? \"A\" : \"N\";\n      });\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.multiple = !selection.length\n    },\n    // 排序触发事件\n    handleSortChange(column, prop, order) {\n      this.queryParams.orderByColumn = column.prop;\n      this.queryParams.isAsc = column.order;\n      this.getList();\n    },\n    // 新增按钮操作\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"新增打印设备\";\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: \"\",\n        name: \"\",\n        sn: \"\",\n        storeId: \"\",\n        autoPrint: \"Y\",\n        type: 'RECEIPT',\n        beforePay: 'Y',\n        afterPay: 'Y',\n        description: \"\",\n        status: \"A\",\n      };\n      this.resetForm(\"form\");\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 提交按钮\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id) {\n              savePrinter(this.form).then(response => {\n                this.$modal.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n              });\n          } else {\n              savePrinter(this.form).then(response => {\n                this.$modal.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n              });\n          }\n        }\n      });\n    },\n    // 修改按钮操作\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getPrinterInfo(id).then(response => {\n        this.form = response.data.printerInfo;\n        this.open = true;\n        this.title = \"编辑打印机\";\n      });\n    },\n    // 删除按钮操作\n    handleDelete(row) {\n      const name = row.name\n      this.$modal.confirm('是否确认删除\"' + name + '\"打印机？').then(function() {\n        return updatePrinterStatus(row.id, 'D');\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      });\n    },\n    // 提交设置按钮\n    submitSettingForm: function() {\n      this.$refs[\"settingForm\"].validate(valid => {\n        if (valid) {\n            saveSetting(this.settingForm).then(response => {\n              this.$modal.msgSuccess(\"保存设置成功\");\n              this.openSetting = false;\n            });\n        }\n      });\n    },\n    // 取消设置按钮\n    cancelSetting() {\n      this.openSetting = false;\n    },\n    // 打印标签按钮操作\n    // 计算有效期日期\n    calculateExpireDate(productDate, days) {\n      const date = new Date(productDate);\n      date.setDate(date.getDate() + days);\n      return date.getFullYear() + '-' +\n             String(date.getMonth() + 1).padStart(2, '0') + '-' +\n             String(date.getDate()).padStart(2, '0');\n    },\n    // 打印标签按钮操作\n    handlePrintLabel(row) {\n      this.currentPrinterId = row.id;\n      this.labelForm = {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      };\n      this.printLabelOpen = true;\n    },\n    // 取消打印标签\n    cancelPrintLabel() {\n      this.printLabelOpen = false;\n      this.currentPrinterId = null;\n      this.labelForm = {\n        name: '',\n        productDate: '',\n        expireDays: 30\n      };\n    },\n    // 提交打印标签\n    submitLabelForm() {\n      this.$refs[\"labelForm\"].validate(valid => {\n        if (valid) {\n          const expireDate = this.calculateExpireDate(this.labelForm.productDate, this.labelForm.expireDays);\n          const data = {\n            id: this.currentPrinterId,\n            name: this.labelForm.name,\n            productDate: this.labelForm.productDate,\n            expireDate: expireDate,\n            printerId: this.currentPrinterId\n          };\n          printExpiryLabel(data).then(response => {\n            this.$modal.msgSuccess(\"打印成功\");\n            this.printLabelOpen = false;\n            this.cancelPrintLabel();\n          });\n        }\n      });\n    },\n    // 加载商品分类列表\n        loadCategoryList(storeId) {\n          const params = {};\n          if (storeId !== undefined && storeId !== null) {\n            params.storeId = storeId;\n          }\n          getCategoryList(params).then(response => {\n            this.categoryList = response.data || [];\n          }).catch(error => {\n            console.error('加载分类列表失败:', error);\n            this.categoryList = [];\n          });\n        },\n    // 加载打印机分类关联数据\n    loadPrinterCategories() {\n      this.printerCategoriesMap = {};\n      const labelPrinters = this.list.filter(printer => printer.type === 'LABEL');\n\n      labelPrinters.forEach(printer => {\n        getPrinterCategories(printer.id).then(response => {\n          this.$set(this.printerCategoriesMap, printer.id, response.data || []);\n        }).catch(error => {\n          console.error(`加载打印机${printer.id}分类关联失败:`, error);\n        });\n      });\n    },\n    // 获取打印机关联的分类名称\n    getPrinterCategoryNames(printerId) {\n      const printerCategories = this.printerCategoriesMap[printerId] || [];\n      return printerCategories.map(pc => {\n        const category = this.categoryList.find(c => c.id === pc.cateId);\n        return category ? category.name : `分类${pc.cateId}`;\n      });\n    },\n    // 打开分类配置对话框\n        handleCategoryConfig(row) {\n          this.currentPrinter = row;\n          this.categoryConfigOpen = true;\n\n          // 加载当前打印机的分类关联\n          this.categoryLoading = true;\n          getPrinterCategories(row.id).then(response => {\n            const printerCategories = response.data || [];\n            this.selectedCategories = printerCategories.map(pc => pc.cateId);\n            this.categoryLoading = false;\n          }).catch(error => {\n            console.error('加载打印机分类关联失败:', error);\n            this.categoryLoading = false;\n          });\n\n          // 重新加载该店铺的商品分类\n          this.loadCategoryList(row.storeId);\n        },\n    // 提交分类配置\n    submitCategoryConfig() {\n      this.categorySubmitting = true;\n\n      const data = {\n        printerId: this.currentPrinter.id,\n        cateIds: this.selectedCategories\n      };\n\n      savePrinterCategories(data).then(response => {\n        this.$modal.msgSuccess(\"配置保存成功\");\n        this.categoryConfigOpen = false;\n        this.categorySubmitting = false;\n        // 重新加载分类关联数据\n        this.loadPrinterCategories();\n      }).catch(error => {\n        console.error('保存分类配置失败:', error);\n        this.categorySubmitting = false;\n      });\n    },\n    // 取消分类配置\n    cancelCategoryConfig() {\n      this.categoryConfigOpen = false;\n      this.selectedCategories = [];\n      this.currentPrinter = {};\n    },\n  }\n};\n</script>\n\n<style scoped>\n.category-checkbox-group {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 10px;\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #fafafa;\n}\n\n.category-checkbox {\n  margin-right: 0 !important;\n  margin-bottom: 8px;\n  padding: 8px;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  background: white;\n  transition: all 0.3s;\n}\n\n.category-checkbox:hover {\n  background: #f0f8ff;\n  border-color: #409eff;\n}\n\n.category-checkbox.is-checked {\n  background: #ecf5ff;\n  border-color: #409eff;\n}\n</style>\n"]}]}