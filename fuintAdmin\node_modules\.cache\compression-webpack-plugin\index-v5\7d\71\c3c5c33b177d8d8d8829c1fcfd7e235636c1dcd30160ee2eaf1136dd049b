
96724f43b8e1e21ba576c6b3f763f01b055c27ab	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.4736218215ff1c322578.hot-update.js\",\"contentHash\":\"06cb8861e2c511f3277a3a4615a5256c\"}","integrity":"sha512-+XGE+GI5M4P+eOFdqAiwVL4BfHS8WvbiNYgJ2UIC00MnlJu9k5lAnRj89wjYRNXvgbHTePocxWDDQ7zXrbV1vg==","time":1754921082923,"size":18795}