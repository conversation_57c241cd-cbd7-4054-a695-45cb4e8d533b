
24269e69adee617abaa51ee8883a81ff62bfbf1d	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"5d3522116c7149756d5de4b3744ab14e\"}","integrity":"sha512-Jw0Bq6lww32+iMZBkDIlEvqlG8aEzBWt51fUZAhTv5TfTvTD+g9INqDMo/vqeX6GCennkSlSwvfLAgWEb3K2GA==","time":1754921484325,"size":6735064}