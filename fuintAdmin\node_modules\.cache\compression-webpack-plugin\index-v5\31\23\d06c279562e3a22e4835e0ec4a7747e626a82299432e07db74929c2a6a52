
f6bf773315980ad6cc58e8dd5e02acd059fe83df	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"bae0bdf8c61d8c4c76c1897c1d3243f6\"}","integrity":"sha512-1T/yIofrCvf0TZbMlQIRXhTV3aomqpcigZrmutA8vl/DKfBbFlwis4Av07nzRYJjTgMDWv+loMQtikiBL/SIUw==","time":1754921138172,"size":2946503}