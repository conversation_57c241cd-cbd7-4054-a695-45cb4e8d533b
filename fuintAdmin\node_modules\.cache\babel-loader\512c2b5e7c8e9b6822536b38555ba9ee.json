{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\api\\goodsTimeConfig.js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\api\\goodsTimeConfig.js", "mtime": 1754838747044}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\eslint-loader\\index.js", "mtime": 1734093918652}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOiOt+WPluWVhuWTgeaXtumXtOautemFjee9ruWIl+ihqApleHBvcnQgZnVuY3Rpb24gZ2V0R29vZHNUaW1lQ29uZmlncyhnb29kc0lkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnYmFja2VuZEFwaS9nb29kcy90aW1lQ29uZmlnL2xpc3QvJyArIGdvb2RzSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOS/neWtmOWVhuWTgeaXtumXtOautemFjee9rgpleHBvcnQgZnVuY3Rpb24gc2F2ZUdvb2RzVGltZUNvbmZpZyhkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnYmFja2VuZEFwaS9nb29kcy90aW1lQ29uZmlnL3NhdmUnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICBjb25zb2xlLmVycm9yKCfkv53lrZjml7bpl7TmrrXphY3nva7lpLHotKU6JywgZXJyb3IpOwogICAgdGhyb3cgZXJyb3I7CiAgfSk7Cn0KCi8vIOabtOaWsOWVhuWTgeaXtumXtOautemFjee9rgpleHBvcnQgZnVuY3Rpb24gdXBkYXRlR29vZHNUaW1lQ29uZmlnKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICdiYWNrZW5kQXBpL2dvb2RzL3RpbWVDb25maWcvdXBkYXRlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgY29uc29sZS5lcnJvcign5pu05paw5pe26Ze05q616YWN572u5aSx6LSlOicsIGVycm9yKTsKICAgIHRocm93IGVycm9yOwogIH0pOwp9CgovLyDliKDpmaTllYblk4Hml7bpl7TmrrXphY3nva4KZXhwb3J0IGZ1bmN0aW9uIGRlbGV0ZUdvb2RzVGltZUNvbmZpZyhpZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJ2JhY2tlbmRBcGkvZ29vZHMvdGltZUNvbmZpZy9kZWxldGUvJyArIGlkLAogICAgbWV0aG9kOiAncG9zdCcKICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOaXtumXtOautemFjee9ruWksei0pTonLCBlcnJvcik7CiAgICB0aHJvdyBlcnJvcjsKICB9KTsKfQoKLy8g6I635Y+W5ZWG5ZOB5pe26Ze05q616YWN572u6K+m5oOFCmV4cG9ydCBmdW5jdGlvbiBnZXRHb29kc1RpbWVDb25maWdEZXRhaWwoaWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICdiYWNrZW5kQXBpL2dvb2RzL3RpbWVDb25maWcvZGV0YWlsLycgKyBpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaXtumXtOautemFjee9ruivpuaDheWksei0pTonLCBlcnJvcik7CiAgICB0aHJvdyBlcnJvcjsKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "getGoodsTimeConfigs", "goodsId", "url", "method", "saveGoodsTimeConfig", "data", "catch", "error", "console", "updateGoodsTimeConfig", "deleteGoodsTimeConfig", "id", "getGoodsTimeConfigDetail"], "sources": ["D:/workspace/fuintFoodSystem/fuintAdmin/src/api/goodsTimeConfig.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 获取商品时间段配置列表\nexport function getGoodsTimeConfigs(goodsId) {\n  return request({\n    url: 'backendApi/goods/timeConfig/list/' + goodsId,\n    method: 'get'\n  })\n}\n\n// 保存商品时间段配置\nexport function saveGoodsTimeConfig(data) {\n  return request({\n    url: 'backendApi/goods/timeConfig/save',\n    method: 'post',\n    data: data\n  }).catch(error => {\n    console.error('保存时间段配置失败:', error)\n    throw error\n  })\n}\n\n// 更新商品时间段配置\nexport function updateGoodsTimeConfig(data) {\n  return request({\n    url: 'backendApi/goods/timeConfig/update',\n    method: 'post',\n    data: data\n  }).catch(error => {\n    console.error('更新时间段配置失败:', error)\n    throw error\n  })\n}\n\n// 删除商品时间段配置\nexport function deleteGoodsTimeConfig(id) {\n  return request({\n    url: 'backendApi/goods/timeConfig/delete/' + id,\n    method: 'post'\n  }).catch(error => {\n    console.error('删除时间段配置失败:', error)\n    throw error\n  })\n}\n\n// 获取商品时间段配置详情\nexport function getGoodsTimeConfigDetail(id) {\n  return request({\n    url: 'backendApi/goods/timeConfig/detail/' + id,\n    method: 'get'\n  }).catch(error => {\n    console.error('获取时间段配置详情失败:', error)\n    throw error\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mCAAmC,GAAGD,OAAO;IAClDE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAEA;EACR,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAK,EAAI;IAChBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,qBAAqBA,CAACJ,IAAI,EAAE;EAC1C,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAEA;EACR,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAK,EAAI;IAChBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,qBAAqBA,CAACC,EAAE,EAAE;EACxC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,qCAAqC,GAAGS,EAAE;IAC/CR,MAAM,EAAE;EACV,CAAC,CAAC,CAACG,KAAK,CAAC,UAAAC,KAAK,EAAI;IAChBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,wBAAwBA,CAACD,EAAE,EAAE;EAC3C,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,qCAAqC,GAAGS,EAAE;IAC/CR,MAAM,EAAE;EACV,CAAC,CAAC,CAACG,KAAK,CAAC,UAAAC,KAAK,EAAI;IAChBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,MAAMA,KAAK;EACb,CAAC,CAAC;AACJ", "ignoreList": []}]}